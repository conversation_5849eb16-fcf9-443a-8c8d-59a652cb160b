package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 会话对象 ChatConversationsVo
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@ApiModel(value = " ChatConversationsVo", description = "会话对象VO")
public class ChatConversationsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 会话ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long conversationId;

    /** 会话名称 */
    @Excel(name = "会话名称")
    @ApiModelProperty(value = "会话名称", name="conversationName")
    private String conversationName;

    /** 会话类型(1-私聊,2-群聊) */
    @Excel(name = "会话类型(1-私聊,2-群聊)")
    @NotNull(message = "会话类型(1-私聊,2-群聊)显示顺序不能为空")
    @ApiModelProperty(value = "会话类型(1-私聊,2-群聊)", name="conversationType", required = true)
    private Integer conversationType;

}
