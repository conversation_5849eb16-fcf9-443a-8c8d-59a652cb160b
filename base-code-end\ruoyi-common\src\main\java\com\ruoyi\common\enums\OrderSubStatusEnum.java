package com.ruoyi.common.enums;

import lombok.Getter;

/**
 * 订单支付状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum OrderSubStatusEnum {

    REFUNDED(0, "已退款"),
    PENDING(1, "待支付"),
    PAID(3, "已支付"),
    COMPLETED(5, "交易结束");


    private final Integer code;
    private final String desc;

    OrderSubStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }



    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举对象
     */
    public static OrderSubStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (OrderSubStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }

        return null;
    }

    /**
     * 判断状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }

}
