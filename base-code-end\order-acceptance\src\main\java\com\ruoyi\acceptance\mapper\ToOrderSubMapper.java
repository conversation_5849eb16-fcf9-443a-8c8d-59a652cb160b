package com.ruoyi.acceptance.mapper;
import com.ruoyi.acceptance.dto.ToOrderSubDTO;
import org.springframework.stereotype.Repository;
import com.ruoyi.acceptance.domain.ToOrderSub;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 接单订单子订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Repository
public interface ToOrderSubMapper extends BaseMapper<ToOrderSub> {
    List<ToOrderSubDTO> selectListByOpenID(String openid);
}
