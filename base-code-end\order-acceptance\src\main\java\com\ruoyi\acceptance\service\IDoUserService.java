package com.ruoyi.acceptance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.DoUser;
import com.ruoyi.common.core.domain.vo.DoUserCardVo;
import com.ruoyi.common.core.domain.vo.DoUserVO;

import java.util.List;

/**
 * 派单用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface IDoUserService extends IService<DoUser>
{

    public DoUser selectToUserByOpenId(String openId);

    public DoUserVO selectToUserByOpenId();

    /**
     * 查询派单用户
     * 
     * @param id 派单用户主键
     * @return 派单用户
     */
    public DoUser selectDoUserById(Long id);

    /**
     * 查询派单用户列表
     * 
     * @param doUser 派单用户
     * @return 派单用户集合
     */
    public List<DoUser> selectDoUserList(DoUser doUser);

    /**
     * 新增派单用户
     * 
     * @param doUser 派单用户
     * @return 结果
     */
    public int insertDoUser(DoUser doUser);

    /**
     * 修改派单用户
     * 
     * @param doUser 派单用户
     * @return 结果
     */
    public int updateDoUser(DoUser doUser);

    /**
     * 批量删除派单用户
     * 
     * @param ids 需要删除的派单用户主键集合
     * @return 结果
     */
    public int deleteDoUserByIds(Long[] ids);

    /**
     * 删除派单用户信息
     * 
     * @param id 派单用户主键
     * @return 结果
     */
    public int deleteDoUserById(Long id);

    R saveDoUser(DoUser doUser);

    DoUserCardVo selectToUserById(Long id);
}
