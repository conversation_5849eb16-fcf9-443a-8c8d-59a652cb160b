package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToOrderHistory;
import com.ruoyi.app.vo.ToOrderHistoryVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 订单流水PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToOrderHistoryConvert extends BeanPoVoMapper<ToOrderHistory, ToOrderHistoryVo> {

        ToOrderHistoryConvert INSTANCE = Mappers.getMapper(ToOrderHistoryConvert.class);

}
