package com.ruoyi.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 派单小程序用户登录对象 do_user_login
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Data
public class DoUserLogin extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户唯一标识 */
    private String openid;

    /** 字典码 */
    @Excel(name = "字典码")
    private String sessionKey;

    /** 用户在开放平台的唯一标识符 */
    @Excel(name = "用户在开放平台的唯一标识符")
    private String unionid;

    /** 是否授权用户信息（0未授权，1已授权） */
    @Excel(name = "是否授权用户信息", readConverterExp = "0=未授权，1已授权")
    private Integer empowerInfo;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    /** 最后登录ip */
    @Excel(name = "最后登录ip")
    private String loginIp;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDateTime loginDate;

    /** 删除标志（0代表存在 1代表删除） */
    private Integer delFlag;
}
