package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 消息对象 ChatMessagesVo
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@ApiModel(value = " ChatMessagesVo", description = "消息对象VO")
public class ChatMessagesVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long messageId;

    /** 会话ID */
    @Excel(name = "会话ID")
    @NotNull(message = "会话ID显示顺序不能为空")
    @ApiModelProperty(value = "会话ID", name="conversationId", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long conversationId;

    /** 发送者ID */
    @Excel(name = "发送者ID")
    @NotNull(message = "发送者ID显示顺序不能为空")
    @ApiModelProperty(value = "发送者ID", name="senderId", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long senderId;

    /** 内容类型(1-文本,2-图片,3-视频,4-文件,5-语音) */
    @Excel(name = "内容类型(1-文本,2-图片,3-视频,4-文件,5-语音)")
    @NotNull(message = "内容类型(1-文本,2-图片,3-视频,4-文件,5-语音)显示顺序不能为空")
    @ApiModelProperty(value = "内容类型(1-文本,2-图片,3-视频,4-文件,5-语音)", name="contentType", required = true)
    private Integer contentType;

    /** 消息内容 */
    @Excel(name = "消息内容")
    @ApiModelProperty(value = "消息内容", name="content")
    private String content;

    /** 文件URL */
    @Excel(name = "文件URL")
    @ApiModelProperty(value = "文件URL", name="fileUrl")
    private String fileUrl;

    /** 文件大小(字节) */
    @Excel(name = "文件大小(字节)")
    @ApiModelProperty(value = "文件大小(字节)", name="fileSize")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileSize;

    /** 是否撤回(0-否,1-是) */
    @Excel(name = "是否撤回(0-否,1-是)")
    @ApiModelProperty(value = "是否撤回(0-否,1-是)", name="isRecalled")
    private Integer isRecalled;

}
