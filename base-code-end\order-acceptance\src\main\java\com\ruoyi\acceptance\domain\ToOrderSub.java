package com.ruoyi.acceptance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.PoBaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 接单订单子订单对象 to_order_sub
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@TableName("to_order_sub")
public class ToOrderSub extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 主项目ID */
    @TableField(value = "project_id")
    private Long projectId;

    /** 标题 */
    @TableField(value = "title")
    private String title;

    /** 需求描述 */
    @TableField(value = "requirement_description")
    private String requirementDescription;

    /** 全额价格 */
    @TableField(value = "total_price")
    private BigDecimal totalPrice;

    /** 是否申请退款（0否1是） */
    @TableField(value = "apply_refund")
    private Integer applyRefund;

    /** 订单付款状态（0已退款1待支付2已支付3交易结束） */
    @TableField(value = "order_pay_status")
    private Integer orderPayStatus;

    /** 商户单号（微信支付交易标识） */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /** 交易单号 */
    @TableField(value = "transaction_id")
    private String transactionId;

    /** 支付时间 */
    @TableField(value = "deposit_payment_time")
    private Date depositPaymentTime;

}
