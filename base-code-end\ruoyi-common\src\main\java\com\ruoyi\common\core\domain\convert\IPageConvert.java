package com.ruoyi.common.core.domain.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * <AUTHOR>
 * @description IPage转换类
 * @date 2022/10/12
 */
public class IPageConvert<P, D, V> {
    private static final IPageConvert INSTANCE = new IPageConvert();

    public static IPageConvert instance() {
        return INSTANCE;
    }

    private IPageConvert() {
    }

    /**
     * IPagePo To IPageDto
     *
     * @param iPagePO        iPagePO
     * @param iPageVO        iPageDTO
     * @param beanPoVoMapper beanPoVoMapper
     * @return IPage<D>
     */
    @Deprecated
    public IPage<V> IPagePoToIPageDto(IPage<P> iPagePO, IPage<V> iPageVO, BeanPoVoMapper<P, V> beanPoVoMapper) {
        iPageVO.setTotal(iPagePO.getTotal());
        iPageVO.setPages(iPagePO.getPages());
        iPageVO.setRecords(beanPoVoMapper.poToVoList(iPagePO.getRecords()));
        iPageVO.setCurrent(iPagePO.getCurrent());
        iPageVO.setSize(iPagePO.getSize());
        return iPageVO;
    }

    /**
     *
     * @param iPageDTO       iPageDTO
     * @param iPageVO        iPageVO
     * @param beanDtoVoMapper beanDtoVoMapper
     * @return IPage<V>
     */
    public IPage<V> IPageDtoToIPageVo(IPage<D> iPageDTO, IPage<V> iPageVO, BeanDtoVoMapper<D, V> beanDtoVoMapper) {
        iPageVO.setTotal(iPageDTO.getTotal());
        iPageVO.setPages(iPageDTO.getPages());
        iPageVO.setRecords(beanDtoVoMapper.dtoToVoList(iPageDTO.getRecords()));
        iPageVO.setCurrent(iPageDTO.getCurrent());
        iPageVO.setSize(iPageDTO.getSize());
        return iPageVO;
    }

    /**
     *
     * @param iPagePO       iPagePO
     * @param iPageDTO        iPageDTO
     * @param beanPoDtoMapper beanPoDtoMapper
     * @return IPage<V>
     */
    public IPage<D> IPagePoToIPageDto(IPage<P> iPagePO, IPage<D> iPageDTO, BeanPoDtoMapper<P, D> beanPoDtoMapper) {
        iPageDTO.setTotal(iPagePO.getTotal());
        iPageDTO.setPages(iPagePO.getPages());
        iPageDTO.setRecords(beanPoDtoMapper.poToDtoList(iPagePO.getRecords()));
        iPageDTO.setCurrent(iPagePO.getCurrent());
        iPageDTO.setSize(iPagePO.getSize());
        return iPageDTO;
    }

    /**
     * IPagePo To IPageDto
     *
     * @param iPagePO        iPagePO
     * @param iPageVO        iPageDTO
     * @param beanPoVoMapper beanPoVoMapper
     * @return IPage<D>
     */
    public IPage<V> IPagePoToIPageVo(IPage<P> iPagePO, IPage<V> iPageVO, BeanPoVoMapper<P, V> beanPoVoMapper) {
        iPageVO.setTotal(iPagePO.getTotal());
        iPageVO.setPages(iPagePO.getPages());
        iPageVO.setRecords(beanPoVoMapper.poToVoList(iPagePO.getRecords()));
        iPageVO.setCurrent(iPagePO.getCurrent());
        iPageVO.setSize(iPagePO.getSize());
        return iPageVO;
    }
}
