package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToWallet;
import com.ruoyi.app.vo.ToWalletVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToWalletConvertImpl implements ToWalletConvert {

    @Override
    public ToWalletVo poToVo(ToWallet arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToWalletVo toWalletVo = new ToWalletVo();

        toWalletVo.setAccountName( arg0.getAccountName() );
        toWalletVo.setBalance( arg0.getBalance() );
        toWalletVo.setId( arg0.getId() );
        toWalletVo.setOpenId( arg0.getOpenId() );

        return toWalletVo;
    }

    @Override
    public List<ToWalletVo> poToVoList(List<ToWallet> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToWalletVo> list = new ArrayList<ToWalletVo>( arg0.size() );
        for ( ToWallet toWallet : arg0 ) {
            list.add( poToVo( toWallet ) );
        }

        return list;
    }

    @Override
    public ToWallet voToPo(ToWalletVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToWallet toWallet = new ToWallet();

        toWallet.setAccountName( arg0.getAccountName() );
        toWallet.setBalance( arg0.getBalance() );
        toWallet.setId( arg0.getId() );
        toWallet.setOpenId( arg0.getOpenId() );

        return toWallet;
    }

    @Override
    public List<ToWallet> voToPoList(List<ToWalletVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToWallet> list = new ArrayList<ToWallet>( arg0.size() );
        for ( ToWalletVo toWalletVo : arg0 ) {
            list.add( voToPo( toWalletVo ) );
        }

        return list;
    }
}
