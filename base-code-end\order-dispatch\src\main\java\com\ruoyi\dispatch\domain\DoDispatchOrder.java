package com.ruoyi.dispatch.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 派单对象 do_dispatch_order
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
@Data
@TableName("do_dispatch_order")
public class DoDispatchOrder extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 派单id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 订单项目id */
    @TableField(value = "project_id")
    private Long projectId;

    /** 派单人id */
    @TableField(value = "taker_id")
    private Long takerId;

}
