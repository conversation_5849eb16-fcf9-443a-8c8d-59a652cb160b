package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
#if($table.crud || $table.sub)
import com.ruoyi.common.core.domain.PoBaseEntity;
#elseif($table.tree)
import com.ruoyi.common.core.domain.TreeEntity;
#end

/**
 * ${functionName}对象 ${tableName}
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="BaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@Data
@TableName("${tableName}")
public class ${ClassName} extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if(${column.isPk} == "1")
#if(${column.isIncrement} == "1")
    @TableId(type = IdType.AUTO)
#else
    @TableId(type = IdType.ASSIGN_ID)
#end
#else
    @TableField(value = "$column.columnName")
#end
    private $column.javaType $column.javaField;

#end
#end
#if($table.sub)
    /** $table.subTable.functionName信息 */
    @TableField(exist = false)
    private List<${subClassName}> ${subclassName}List;

#end
}
