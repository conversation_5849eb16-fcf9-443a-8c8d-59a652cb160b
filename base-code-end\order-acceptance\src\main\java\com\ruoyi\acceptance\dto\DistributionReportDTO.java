package com.ruoyi.acceptance.dto;

import lombok.Data;

import java.math.BigDecimal;


@Data
public class DistributionReportDTO {
    // 总单量
    private BigDecimal totalOrderQuantity;
    // 总佣金 金额
    private BigDecimal totalAmount;
    // 冻结金额
    private BigDecimal totalFrozenAmount;
    // 待提现金额
    private BigDecimal totalNotWithdrawAmount;
    // 已提现金额
    private BigDecimal totalWithdrawnAmount;
    // 总邀请人数
    private BigDecimal totalCustomerQuantity;

    
}
