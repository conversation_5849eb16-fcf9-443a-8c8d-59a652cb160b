package com.ruoyi.acceptance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.domain.ToPaper;
import com.ruoyi.acceptance.dto.ToPaperDTO;
import com.ruoyi.acceptance.vo.ToPaperVo;
import com.ruoyi.common.core.domain.R;

import java.util.HashMap;
import java.util.List;

/**
 * 论文管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
public interface IToPaperService extends IService<ToPaper> {

    /**
     * 分页查询论文管理
     * @param toPaper 论文管理
     * @param page 分页条件
     * @return 论文管理集合
     */
    IPage<ToPaper> pages(ToPaper toPaper, IPage<ToPaper> page);

    IPage<ToPaperDTO> selectToPapers(ToPaperVo toPaperVo, IPage<ToPaperDTO> page);

    /**
     * 查询论文管理列表
     * 
     * @param toPaper 论文管理
     * @return 论文管理集合
     */
     List<ToPaper> selectList(ToPaper toPaper);
    ToPaperDTO getToPaperDtoById(Long id);

    List<ToPaperDTO> tradedList();

    List<ToPaperDTO> obligationList();

    List<ToPaperDTO> orderStatusList(String status,String paperName);
    HashMap orderStatusListByMap(String paperName);
    List<ToPaperDTO> notSaleList();

    ToPaperDTO getInfoById(Long id);
    ToPaperDTO getInfoForPcById(Long id);

    R<?> savePaperFromWeb(ToPaperVo toPaperVo);
    R<?> savePaperFromWx(ToPaper paper);

    R<?> removeForWeb(Long[] ids,Long[] orderIds);
    R<?> editForWeb(ToPaperVo toPaperVo);
}
