package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ToOrderSubVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.acceptance.domain.ToOrderSub;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 接单订单子订单PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToOrderSubConvert extends BeanPoVoMapper<ToOrderSub, ToOrderSubVo> {

        ToOrderSubConvert INSTANCE = Mappers.getMapper(ToOrderSubConvert.class);

}
