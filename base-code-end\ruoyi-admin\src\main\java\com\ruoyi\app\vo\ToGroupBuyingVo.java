package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 团购信息对象 ToGroupBuyingVo
 * 
 * <AUTHOR>
 * @date 2024-08-11
 */
@Data
@ApiModel(value = " ToGroupBuyingVo", description = "团购信息对象VO")
public class ToGroupBuyingVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 团购id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id", name="orderId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /** 团购组别id */
    @Excel(name = "团购组别id")
    @ApiModelProperty(value = "团购组别id", name="groupId")
    private String groupId;

    /** openid */
    @Excel(name = "openid")
    @ApiModelProperty(value = "openid", name="openId")
    private String openId;

}
