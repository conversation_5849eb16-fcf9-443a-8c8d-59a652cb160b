package com.ruoyi.web.controller.projectCategory;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ToProjectCategoryVo;
import com.ruoyi.app.manager.ToProjectCategoryManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 项目类目信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Api(tags = "项目类目信息管理")
@RestController
@RequestMapping("/acceptance/category")
public class ToProjectCategoryController {

    @Autowired
    private ToProjectCategoryManager toProjectCategoryManager;

    /**
     * 分页查询项目类目信息
     */
    @ApiOperation("分页查询项目类目信息")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('acceptance:category:list')")
    @GetMapping("/list")
    public R<IPage<ToProjectCategoryVo>> list(ToProjectCategoryVo toProjectCategoryVo, Query query)
    {
        return R.ok(toProjectCategoryManager.page(toProjectCategoryVo, query));
    }

    /**
    * 查询项目类目信息全部列表
    */
    @ApiOperation("查询项目类目信息全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('acceptance:category:list')")
    @GetMapping("/allList")
    public R<List<ToProjectCategoryVo>> allList(ToProjectCategoryVo toProjectCategoryVo){
        return R.ok(toProjectCategoryManager.list(toProjectCategoryVo));
    }

    /**
     * 导出项目类目信息列表
     */
    @ApiOperation("导出项目类目信息列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('acceptance:category:export')")
    @Log(title = "项目类目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToProjectCategoryVo toProjectCategoryVo, Query query)
    {
        List<ToProjectCategoryVo> list = toProjectCategoryManager.page(toProjectCategoryVo, query).getRecords();
        ExcelUtil<ToProjectCategoryVo> util = new ExcelUtil<ToProjectCategoryVo>(ToProjectCategoryVo.class);
        util.exportExcel(response, list, "项目类目信息数据");
    }

    /**
     * 获取项目类目信息详细信息
     */
    @ApiOperation("获取项目类目信息详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:category:query')")
    @GetMapping(value = "/{id}")
    public R<ToProjectCategoryVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toProjectCategoryManager.getInfo(id));
    }

    /**
     * 新增项目类目信息
     */
    @ApiOperation("新增项目类目信息")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('acceptance:category:add')")
    @Log(title = "项目类目信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToProjectCategoryVo toProjectCategoryVo)
    {
        return toProjectCategoryManager.add(toProjectCategoryVo) ? R.ok() : R.fail();
    }

    /**
     * 修改项目类目信息
     */
    @ApiOperation("修改项目类目信息")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('acceptance:category:edit')")
    @Log(title = "项目类目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToProjectCategoryVo toProjectCategoryVo)
    {
        return toProjectCategoryManager.edit(toProjectCategoryVo) ? R.ok() : R.fail();
    }

    /**
     * 删除项目类目信息
     */
    @ApiOperation("删除项目类目信息")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:category:remove')")
    @Log(title = "项目类目信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toProjectCategoryManager.remove(ids) ? R.ok() : R.fail();
    }
}
