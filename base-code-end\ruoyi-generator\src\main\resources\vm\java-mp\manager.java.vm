package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import ${packageName}.service.I${ClassName}Service;
import com.ruoyi.app.vo.${ClassName}Vo;
import com.ruoyi.app.vo.convert.${ClassName}Convert;
import ${packageName}.domain.${ClassName};

import java.util.List;

/**
 * ${functionName}Manager
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Component
public class ${ClassName}Manager {

    @Autowired
    private I${ClassName}Service ${className}Service;

    @Autowired
    private ${ClassName}Convert ${className}Convert;

    /**
    * 分页查询${functionName}
    */
    public IPage<${ClassName}Vo> page(${ClassName}Vo ${className}Vo, Query query){
        IPage<${ClassName}> page = ${className}Service.pages(${className}Convert.voToPo(${className}Vo), Condition.getPage(query));
        return (IPage<${ClassName}Vo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<${ClassName}Vo>(), ${ClassName}Convert.INSTANCE);
    }

    /**
    * 查询${functionName}列表
    */
    public List<${ClassName}Vo> list(${ClassName}Vo ${className}Vo) {
        List<${ClassName}> ${className}List = ${className}Service.selectList(${className}Convert.voToPo(${className}Vo));
        return ${className}Convert.poToVoList(${className}List);
    }

    /**
     * 查询${functionName}详细信息
     */
    public ${ClassName}Vo getInfo(${pkColumn.javaType} ${pkColumn.javaField}) {
        ${ClassName} ${className} = ${className}Service.getById(${pkColumn.javaField});
        return ${className}Convert.poToVo(${className});
    }

    /**
     * 新增${functionName}
     */
    public boolean add(${ClassName}Vo ${className}Vo) {
        return ${className}Service.save(${className}Convert.voToPo(${className}Vo));
    }

    /**
     * 修改${functionName}
     */
    public boolean edit(${ClassName}Vo ${className}Vo) {
        return ${className}Service.updateById(${className}Convert.voToPo(${className}Vo));
    }

    /**
     * 批量删除${functionName}
     */
    public boolean remove(${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return ${className}Service.removeByIds(CollUtil.toList(${pkColumn.javaField}s));
    }

}
