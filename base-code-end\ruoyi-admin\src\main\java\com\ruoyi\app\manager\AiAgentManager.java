package com.ruoyi.app.manager;

import com.alibaba.dashscope.common.Message;
import com.ruoyi.system.domain.CommonPromptProjectType;
import com.ruoyi.system.dto.CommonPromptDTO;
import com.ruoyi.system.integration.sdkadapter.AliyunAiAdapter;
import com.ruoyi.system.mapper.CommonPromptProjectTypeMapper;
import com.ruoyi.system.service.ICommonPromptService;
import io.reactivex.Flowable;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AI代理管理器
 * 负责调用service和封装的方法，不做具体的实现内容
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Component
public class AiAgentManager {
    
    private static final Logger logger = LoggerFactory.getLogger(AiAgentManager.class);
    
    @Autowired
    private ICommonPromptService commonPromptService;

    @Autowired
    private CommonPromptProjectTypeMapper commonPromptProjectTypeMapper;
    
    /**
     * 提示词封装类
     */
    @Getter
    public static class PromptWrapper {
        private String systemPrompt;
        private String userPrompt;
        private CommonPromptDTO promptDTO;
        
        public PromptWrapper(String systemPrompt, String userPrompt, CommonPromptDTO promptDTO) {
            this.systemPrompt = systemPrompt;
            this.userPrompt = userPrompt;
            this.promptDTO = promptDTO;
        }

    }
    
    /**
     * 生成项目需求（流式响应）
     * 
     * @param projectName 项目名称
     * @param categoryCode 类目代码
     * @return 流式响应
     */
    public Flowable<String> generateProjectRequirements(String projectName, String categoryCode) {
        try {
            // 根据类目代码查找对应的提示词
            PromptWrapper promptWrapper = buildPromptContent(projectName, categoryCode);
            
            // 调用AI适配器的流式方法
            return AliyunAiAdapter.streamCallWithMessages(promptWrapper.getSystemPrompt(), promptWrapper.getUserPrompt(), 
                                                         promptWrapper.getPromptDTO());
            
        } catch (Exception e) {
            logger.error("生成项目需求失败", e);
            return Flowable.error(e);
        }
    }
    
    /**
     * 生成项目需求（同步响应）
     * 
     * @param projectName 项目名称
     * @param categoryCode 类目代码
     * @return 完整响应内容
     */
    public String generateProjectRequirementsSync(String projectName, String categoryCode) {
        try {
            // 根据类目代码查找对应的提示词
            PromptWrapper promptWrapper = buildPromptContent(projectName, categoryCode);
            
            // 调用AI适配器的同步方法
            return AliyunAiAdapter.syncCallWithMessages(promptWrapper.getSystemPrompt(), promptWrapper.getUserPrompt(),
                                                      promptWrapper.getPromptDTO());
            
        } catch (Exception e) {
            logger.error("生成项目需求失败", e);
            throw new RuntimeException("生成项目需求失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建提示词内容
     * 
     * @param projectName 项目名称
     * @param categoryCode 类目代码
     * @return 提示词封装对象
     */
    private PromptWrapper buildPromptContent(String projectName, String categoryCode) {
        // 查询所有启用的提示词
        CommonPromptDTO promptQuery = new CommonPromptDTO();
        promptQuery.setIsActive(0); // 0表示启用

//        CommonPromptProjectType commonPromptProjectType = commonPromptProjectTypeMapper.selectById();

        List<CommonPromptDTO> allPrompts = commonPromptService.selectList(promptQuery);
        
        // 查找对应类目的提示词
        CommonPromptDTO targetPrompt = null;
        for (CommonPromptDTO prompt : allPrompts) {
            if (prompt.getCommonPromptProjectTypes() != null && 
                prompt.getCommonPromptProjectTypes().contains(Long.valueOf(categoryCode))) {
                targetPrompt = prompt;
                break;
            }
        }
        
        if (targetPrompt == null) {
            throw new RuntimeException("未找到类目代码为 " + categoryCode + " 的提示词");
        }
        
        // 处理系统提示词
        String systemPrompt = targetPrompt.getSystemPrompt();
        if (systemPrompt != null) {
            systemPrompt = replaceVariables(systemPrompt, projectName);
        }
        
        // 处理用户提示词
        String userPrompt = targetPrompt.getContent();
        if (userPrompt != null) {
            userPrompt = replaceVariables(userPrompt, projectName);
        }
        
        return new PromptWrapper(systemPrompt, userPrompt, targetPrompt);
    }
    
    /**
     * 替换提示词中的变量
     * 
     * @param content 原始内容
     * @param projectName 项目名称
     * @return 替换后的内容
     */
    private String replaceVariables(String content, String projectName) {
        if (content == null) {
            return null;
        }

        // 替换项目名称变量
        content = content.replace("${project.name}", projectName);

        return content;
    }
} 