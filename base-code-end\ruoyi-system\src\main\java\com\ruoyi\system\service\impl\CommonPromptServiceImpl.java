package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.system.domain.CommonPromptProjectType;
import com.ruoyi.system.dto.CommonPromptDTO;
import com.ruoyi.system.dto.convert.CommonPromptDTOConvert;
import com.ruoyi.system.mapper.CommonPromptProjectTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.system.mapper.CommonPromptMapper;
import com.ruoyi.system.domain.CommonPrompt;
import com.ruoyi.system.service.ICommonPromptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;


/**
 * 提示词Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
public class CommonPromptServiceImpl extends ServiceImpl<CommonPromptMapper,CommonPrompt> implements ICommonPromptService {

    @Autowired
    private CommonPromptMapper commonPromptMapper;

    @Autowired
    private CommonPromptProjectTypeMapper commonPromptProjectTypeMapper;
    
    @Autowired
    private CommonPromptDTOConvert commonPromptConvert;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<CommonPrompt> queryWrapper(CommonPrompt commonPrompt) {
        LambdaQueryWrapper<CommonPrompt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(commonPrompt.getAiType()), CommonPrompt::getAiType, commonPrompt.getAiType());
        queryWrapper.eq(ObjUtil.isNotEmpty(commonPrompt.getPromptTitle()), CommonPrompt::getPromptTitle, commonPrompt.getPromptTitle());
        queryWrapper.eq(ObjUtil.isNotEmpty(commonPrompt.getContent()), CommonPrompt::getContent, commonPrompt.getContent());
        queryWrapper.eq(ObjUtil.isNotEmpty(commonPrompt.getIsActive()), CommonPrompt::getIsActive, commonPrompt.getIsActive());
        return queryWrapper;
    }

    /**
     * 查询提示词分页
     *
     * @param commonPromptDTO 提示词
     * @return 提示词
     */
    @Override
    public IPage<CommonPromptDTO> pages(CommonPromptDTO commonPromptDTO, IPage<CommonPrompt> page) {
        CommonPrompt commonPrompt = commonPromptConvert.dtoToPo(commonPromptDTO);
        IPage<CommonPrompt> pageResult = commonPromptMapper.selectPage(page, this.queryWrapper(commonPrompt));
        
        // 转换为DTO分页结果
        IPage<CommonPromptDTO> dtoPage = IPageConvert.instance().IPagePoToIPageDto(pageResult, new Page<CommonPromptDTO>(), CommonPromptDTOConvert.INSTANCE);
        
        // 为每个DTO加载关联的项目类型
        for (CommonPromptDTO dto : dtoPage.getRecords()) {
            getProjectTypes(dto, commonPromptProjectTypeMapper);
        }
        
        return dtoPage;
    }

    /**
     * 查询提示词列表
     * 
     * @param commonPromptDTO 提示词
     * @return 提示词
     */
    @Override
    public List<CommonPromptDTO> selectList(CommonPromptDTO commonPromptDTO) {
        CommonPrompt commonPrompt = commonPromptConvert.dtoToPo(commonPromptDTO);
        List<CommonPrompt> promptList = commonPromptMapper.selectList(this.queryWrapper(commonPrompt));
        
        // 转换为DTO并加载关联数据
        List<CommonPromptDTO> resultList = commonPromptConvert.poToDtoList(promptList);
        
        // 为每个DTO加载关联的项目类型
        for (CommonPromptDTO dto : resultList) {
            getProjectTypes(dto, commonPromptProjectTypeMapper);
        }
        
        return resultList;
    }

    private static void getProjectTypes(CommonPromptDTO dto, CommonPromptProjectTypeMapper commonPromptProjectTypeMapper) {
        QueryWrapper<CommonPromptProjectType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CommonPromptProjectType::getPromptId, dto.getPromptId());
        List<CommonPromptProjectType> projectTypes = commonPromptProjectTypeMapper.selectList(queryWrapper);
        List<Long> collect = projectTypes.stream().map(CommonPromptProjectType::getCategoryTypeId).collect(Collectors.toList());
        dto.setCommonPromptProjectTypes(collect);
    }

    /**
     * 根据ID获取提示词信息
     *
     * @param promptId 提示词ID
     * @return 提示词信息
     */
    @Override
    public CommonPromptDTO getById(Long promptId) {
        CommonPrompt commonPrompt = commonPromptMapper.selectById(promptId);
        if (commonPrompt != null) {
            // 查询关联的项目类型
            QueryWrapper<CommonPromptProjectType> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CommonPromptProjectType::getPromptId, promptId);
            List<CommonPromptProjectType> projectTypes = commonPromptProjectTypeMapper.selectList(queryWrapper);
            List<Long> collect = projectTypes.stream().map(CommonPromptProjectType::getCategoryTypeId).collect(Collectors.toList());
            // 使用MapStruct转换器处理两个实体到DTO的映射
            return commonPromptConvert.poToDto(commonPrompt, collect);
        }
        return null;
    }
    
    /**
     * 保存提示词信息
     *
     * @param commonPromptDTO 提示词信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(CommonPromptDTO commonPromptDTO) {
        // 保存提示词基本信息
        CommonPrompt commonPrompt = commonPromptConvert.dtoToPo(commonPromptDTO);
        boolean result = super.save(commonPrompt);
        
        // 保存关联项目类型
        if (result && ObjUtil.isNotEmpty(commonPromptDTO.getCommonPromptProjectTypes())) {
            Long promptId = commonPrompt.getPromptId();
            List<Long> projectTypes = commonPromptDTO.getCommonPromptProjectTypes();
            for (Long projectType : projectTypes) {
                CommonPromptProjectType commonPromptProjectType = new CommonPromptProjectType();
                commonPromptProjectType.setPromptId(promptId);
                commonPromptProjectType.setCategoryTypeId(projectType);
                commonPromptProjectTypeMapper.insert(commonPromptProjectType);
            }
        }
        
        return result;
    }
    
    /**
     * 更新提示词信息
     *
     * @param commonPromptDTO 提示词信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(CommonPromptDTO commonPromptDTO) {
        // 更新提示词基本信息
        CommonPrompt commonPrompt = commonPromptConvert.dtoToPo(commonPromptDTO);
        boolean result = super.updateById(commonPrompt);
        
        if (result) {
            Long promptId = commonPromptDTO.getPromptId();
            // 删除原有关联
            QueryWrapper<CommonPromptProjectType> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CommonPromptProjectType::getPromptId, promptId);
            commonPromptProjectTypeMapper.delete(queryWrapper);
            
            // 保存新的关联
            if (ObjUtil.isNotEmpty(commonPromptDTO.getCommonPromptProjectTypes())) {
                List<Long> projectTypes = commonPromptDTO.getCommonPromptProjectTypes();
                for (Long projectType : projectTypes) {
                    CommonPromptProjectType commonPromptProjectType = new CommonPromptProjectType();
                    commonPromptProjectType.setPromptId(promptId);
                    commonPromptProjectType.setCategoryTypeId(projectType);
                    commonPromptProjectTypeMapper.insert(commonPromptProjectType);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 批量删除提示词
     *
     * @param ids 需要删除的提示词ID集合
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(List<Long> ids) {
        // 删除关联表数据
        for (Long promptId : ids) {
            QueryWrapper<CommonPromptProjectType> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CommonPromptProjectType::getPromptId, promptId);
            commonPromptProjectTypeMapper.delete(queryWrapper);
        }
        // 删除主表数据
        return super.removeByIds(ids);
    }
}
