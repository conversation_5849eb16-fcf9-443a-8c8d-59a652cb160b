package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatConversationMembersVo;
import com.ruoyi.dispatch.domain.ChatConversationMembers;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatConversationMembersConvertImpl implements ChatConversationMembersConvert {

    @Override
    public ChatConversationMembersVo poToVo(ChatConversationMembers arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatConversationMembersVo chatConversationMembersVo = new ChatConversationMembersVo();

        chatConversationMembersVo.setConversationId( arg0.getConversationId() );
        chatConversationMembersVo.setId( arg0.getId() );
        chatConversationMembersVo.setJoinTime( arg0.getJoinTime() );
        chatConversationMembersVo.setRole( arg0.getRole() );
        if ( arg0.getUserOpenid() != null ) {
            chatConversationMembersVo.setUserOpenid( Long.parseLong( arg0.getUserOpenid() ) );
        }

        return chatConversationMembersVo;
    }

    @Override
    public List<ChatConversationMembersVo> poToVoList(List<ChatConversationMembers> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatConversationMembersVo> list = new ArrayList<ChatConversationMembersVo>( arg0.size() );
        for ( ChatConversationMembers chatConversationMembers : arg0 ) {
            list.add( poToVo( chatConversationMembers ) );
        }

        return list;
    }

    @Override
    public ChatConversationMembers voToPo(ChatConversationMembersVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatConversationMembers chatConversationMembers = new ChatConversationMembers();

        chatConversationMembers.setConversationId( arg0.getConversationId() );
        chatConversationMembers.setId( arg0.getId() );
        chatConversationMembers.setJoinTime( arg0.getJoinTime() );
        chatConversationMembers.setRole( arg0.getRole() );
        if ( arg0.getUserOpenid() != null ) {
            chatConversationMembers.setUserOpenid( String.valueOf( arg0.getUserOpenid() ) );
        }

        return chatConversationMembers;
    }

    @Override
    public List<ChatConversationMembers> voToPoList(List<ChatConversationMembersVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatConversationMembers> list = new ArrayList<ChatConversationMembers>( arg0.size() );
        for ( ChatConversationMembersVo chatConversationMembersVo : arg0 ) {
            list.add( voToPo( chatConversationMembersVo ) );
        }

        return list;
    }
}
