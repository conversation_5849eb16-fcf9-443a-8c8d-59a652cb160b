package com.ruoyi.app.controller.groupBuying;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.acceptance.vo.GroupInfoVO;
import com.ruoyi.acceptance.vo.MyGroupOrderVO;
import com.ruoyi.app.manager.ToGroupBuyingManager;
import com.ruoyi.app.vo.ToGroupBuyingVo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 团购信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-11
 */
@Api(tags = "团购信息管理")
@RestController
@RequestMapping("/wx/acceptance/buying")
public class ToGroupBuyingController {

    @Autowired
    private ToGroupBuyingManager toGroupBuyingManager;

    /**
     * 分页查询团购信息
     */
    @ApiOperation("分页查询团购信息")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public R<IPage<ToGroupBuyingVo>> list(ToGroupBuyingVo toGroupBuyingVo, Query query)
    {
        return R.ok(toGroupBuyingManager.page(toGroupBuyingVo, query));
    }

    /**
    * 查询团购信息全部列表
    */
    @ApiOperation("查询团购信息全部列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/allList")
    public R<List<ToGroupBuyingVo>> allList(ToGroupBuyingVo toGroupBuyingVo){
        return R.ok(toGroupBuyingManager.list(toGroupBuyingVo));
    }

    /**
     * 导出团购信息列表
     */
    @ApiOperation("导出团购信息列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToGroupBuyingVo toGroupBuyingVo, Query query)
    {
        List<ToGroupBuyingVo> list = toGroupBuyingManager.page(toGroupBuyingVo, query).getRecords();
        ExcelUtil<ToGroupBuyingVo> util = new ExcelUtil<ToGroupBuyingVo>(ToGroupBuyingVo.class);
        util.exportExcel(response, list, "团购信息数据");
    }

    /**
     * 获取团购信息详细信息
     */
    @ApiOperation("获取团购信息详细")
    @ApiOperationSupport(order = 3)
    @GetMapping(value = "/{id}")
    public R<ToGroupBuyingVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toGroupBuyingManager.getInfo(id));
    }

    /**
     * 新增团购信息
     */
    @ApiOperation("申请团购接口")
    @ApiOperationSupport(order = 4)
    @GetMapping("/add/{orderId}")
    public R<?> add(@PathVariable Long orderId)
    {
        return toGroupBuyingManager.add(orderId);
    }

    /**
     * 修改团购信息
     */
    @ApiOperation("修改团购信息")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToGroupBuyingVo toGroupBuyingVo)
    {
        return toGroupBuyingManager.edit(toGroupBuyingVo) ? R.ok() : R.fail();
    }

    /**
     * 删除团购信息
     */
    @ApiOperation("删除团购信息")
    @ApiOperationSupport(order = 6)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toGroupBuyingManager.remove(ids) ? R.ok() : R.fail();
    }

    /**
     * 新增团购信息
     */
    @ApiOperation("接受团购接口")
    @ApiOperationSupport(order = 7)
    @PostMapping("/addGroup")
    public R<?> addGroup(@Valid @RequestBody ToGroupBuyingVo toGroupBuyingVo)
    {
        return toGroupBuyingManager.addGroup(toGroupBuyingVo) ? R.ok("参加团购成功！") : R.fail("参加团购失败！");
    }

    @ApiOperation("通过论文id获取团购明细")
    @ApiOperationSupport(order = 8)
    @GetMapping(value = "/getGroupInfoByPaperId/{groupId}")
    public R<GroupInfoVO> getGroupInfoByPaperId(@PathVariable("groupId") String groupId)
    {
        return R.ok(toGroupBuyingManager.getGroupInfoByPaperId(groupId));
    }

    @ApiOperation("查看我的团购订单列表")
    @GetMapping("/myGroupOrderList")
    public R<List<MyGroupOrderVO>> myGroupOrderList()
    {
        return R.ok(toGroupBuyingManager.myGroupOrderList());
    }
}
