<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToOrderSubMapper">

    <select id="selectListByOpenID" resultType="com.ruoyi.acceptance.dto.ToOrderSubDTO">
        SELECT t1.*,t2.title as project_title
        FROM to_order_sub t1
        join to_project t2
        on t1.project_id = t2.id
        WHERE t2.customer_openid = #{openid}
    </select>
</mapper>