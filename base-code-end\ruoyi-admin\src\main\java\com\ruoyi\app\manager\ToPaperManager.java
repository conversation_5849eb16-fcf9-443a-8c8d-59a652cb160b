package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.domain.ToOrder;
import com.ruoyi.acceptance.domain.ToPaper;
import com.ruoyi.acceptance.dto.ToPaperDTO;
import com.ruoyi.acceptance.service.IToGroupBuyingService;
import com.ruoyi.acceptance.service.IToOrderService;
import com.ruoyi.acceptance.service.IToPaperService;
import com.ruoyi.acceptance.vo.ToPaperVo;
import com.ruoyi.app.vo.convert.ToPaperConvert;
import com.ruoyi.app.vo.convert.ToPaperConvertTwo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * 论文管理Manager
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Component
public class ToPaperManager {

    @Autowired
    private IToPaperService toPaperService;

    @Autowired
    private IToOrderService toOrderService;

    @Autowired
    private IToGroupBuyingService toGroupBuyingService;

    @Autowired
    private ToPaperConvert toPaperConvert;

    @Autowired
    private ToPaperConvertTwo toPaperConvertTwo;

    /**
    * 分页查询论文管理
    */
    public IPage<ToPaperVo> page(ToPaperVo toPaperVo, Query query){
        IPage<ToPaper> page = toPaperService.pages(toPaperConvert.voToPo(toPaperVo), Condition.getPage(query));
        return (IPage<ToPaperVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToPaperVo>(), ToPaperConvert.INSTANCE);
    }

    public IPage<ToPaperVo> selectToPapers(ToPaperVo toPaperVo, Query query){
        IPage<ToPaperDTO> page = toPaperService.selectToPapers(toPaperVo, Condition.getPage(query));
        return (IPage<ToPaperVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToPaperVo>(), ToPaperConvertTwo.INSTANCE);
    }

    /**
    * 查询论文管理列表
    */
    public List<ToPaperVo> list(ToPaperVo toPaperVo) {
        List<ToPaper> toPaperList = toPaperService.selectList(toPaperConvert.voToPo(toPaperVo));
        return toPaperConvert.poToVoList(toPaperList);
    }

    /**
     * 查询论文管理详细信息
     */
    public ToPaperVo getInfo(Long id) {
//        ToPaper toPaper = toPaperService.getById(id);
//        return toPaperConvert.poToVo(toPaper);
        ToPaperDTO infoById = toPaperService.getInfoById(id);
        return toPaperConvertTwo.poToVo(infoById);
    }

    public ToPaperVo getInfoForPc(Long id) {
        ToPaperDTO toPaperDTO = toPaperService.getInfoForPcById(id);
        return toPaperConvertTwo.poToVo(toPaperDTO);
    }

    /**
     * 新增论文管理
     */
    public boolean add(ToPaperVo toPaperVo) {
        ToOrder toOrder = new ToOrder();
        BeanUtils.copyProperties(toPaperVo,toOrder);
        toOrder.setOrderPayStatus(1);
        toOrderService.save(toOrder);
        toPaperVo.setOrderId(toOrder.getId());
        return toPaperService.save(toPaperConvert.voToPo(toPaperVo));
    }

    public R<?> savePaperFromWx(ToPaperVo toPaperVo) {
        ToPaper toPaper = toPaperConvert.voToPo(toPaperVo);
        return toPaperService.savePaperFromWx(toPaper);
    }

    public R<?> savePaperFromWeb(ToPaperVo toPaperVo) {
        return toPaperService.savePaperFromWeb(toPaperVo);
    }

    /**
     * 修改论文管理
     */
    public boolean edit(ToPaperVo toPaperVo) {
        ToOrder toOrder = new ToOrder();
        BeanUtils.copyProperties(toPaperVo,toOrder);
        QueryWrapper queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",toPaperVo.getOrderId());
        toOrderService.update(toOrder,queryWrapper);
        return toPaperService.updateById(toPaperConvert.voToPo(toPaperVo));
    }

    public R<?> editForWeb(ToPaperVo toPaperVo) {
        return toPaperService.editForWeb(toPaperVo);
    }

    /**
     * 批量删除论文管理
     */
    public boolean remove(Long[] ids,Long[] orderIds) {
//        toPaperService.removeByIds(CollUtil.toList(ids));
        toOrderService.removeByIds(CollUtil.toList(orderIds));
        return toPaperService.removeByIds(CollUtil.toList(ids));
    }

    public R<?>  removeForWeb(Long[] ids,Long[] orderIds) {
        return toPaperService.removeForWeb(ids,orderIds);
    }

    public List<ToPaperDTO> tradedList() {
        return toPaperService.tradedList();
    }

    public List<ToPaperDTO> obligationList() {
        return toPaperService.obligationList();
    }

    public List<ToPaperDTO> orderStatusList(String status,String paperName) {
        return toPaperService.orderStatusList(status,paperName);
    }

    public HashMap orderStatusListByMap(String paperName) {
        return toPaperService.orderStatusListByMap(paperName);
    }

    public List<ToPaperDTO> notSaleList() {
        return toPaperService.notSaleList();
    }
}
