package com.ruoyi.acceptance.service;

import java.math.BigDecimal;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.acceptance.domain.ToWallet;

/**
 * 钱包Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
public interface IToWalletService extends IService<ToWallet> {

    /**
     * 分页查询钱包
     * @param toWallet 钱包
     * @param page 分页条件
     * @return 钱包集合
     */
    IPage<ToWallet> pages(ToWallet toWallet, IPage<ToWallet> page);

    /**
     * 查询钱包列表
     * 
     * @param toWallet 钱包
     * @return 钱包集合
     */
     List<ToWallet> selectList(ToWallet toWallet);

    ToWallet getInfoByOpenId();

    List<ToWallet> selectToWalletsByOpenId(List<String> openIds);

    R<?> withdrawal(BigDecimal withdrawalAmount);
}
