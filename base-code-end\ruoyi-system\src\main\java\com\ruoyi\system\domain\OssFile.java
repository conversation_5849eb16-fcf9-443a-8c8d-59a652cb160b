package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.PoBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @description: oss云存储实体类
 * @author: 天鸣
 */
@Data
@TableName("oss_file")
@EqualsAndHashCode(callSuper = false)
public class OssFile extends PoBaseEntity {

	private static final long serialVersionUID = 1L;

	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	private String oldFileName;

	private String url;

	private String fileName;

	private Integer clearFlag;

	@JsonFormat(shape = JsonFormat.Shape.STRING)
	private Long fileSize;

}
