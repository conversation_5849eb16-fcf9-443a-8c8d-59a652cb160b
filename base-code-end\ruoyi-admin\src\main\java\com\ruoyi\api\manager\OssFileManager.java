package com.ruoyi.api.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.api.vo.OssFileVo;
import com.ruoyi.api.vo.convert.OssFileConvert;
import com.ruoyi.common.config.OssConfig;
import com.ruoyi.common.constant.DbConstant;
import com.ruoyi.system.domain.OssFile;
import com.ruoyi.system.integration.dto.OssResultDto;
import com.ruoyi.system.service.IOssFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 文件管理Manager
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Component
public class OssFileManager {

    @Autowired
    private IOssFileService ossFileService;

    @Autowired
    private OssFileConvert ossFileConvert;

    /**
     * 根据id查询文件管理详细信息
     */
    public OssFileVo getInfo(Long id) {
        LambdaQueryWrapper<OssFile> wrapper = Wrappers.<OssFile>lambdaQuery().eq(OssFile::getId, id).last("limit 1");
        OssFile ossFile = ossFileService.getOne(wrapper);
        return ossFileConvert.poToVo(ossFile);
    }

    /**
     * 查询文件管理详细信息
     */
    public OssFileVo getInfo(String fileName) {
        LambdaQueryWrapper<OssFile> wrapper = Wrappers.<OssFile>lambdaQuery().eq(OssFile::getFileName, fileName).last("limit 1");
        OssFile ossFile = ossFileService.getOne(wrapper);
        return ossFileConvert.poToVo(ossFile);
    }

    /**
     * 物理删除文件OSS通过fileName
     */
    public boolean remove(String fileName) {
        return ossFileService.remove(Wrappers.<OssFile>lambdaQuery().eq(OssFile::getFileName, fileName));
    }

    /**
     * 标记删除文件通过fileName
     */
    public void removeFlag(String fileName) {
        ossFileService.update(Wrappers.<OssFile>lambdaUpdate().set(OssFile::getClearFlag, DbConstant.DB_STATUS_0).eq(OssFile::getFileName, fileName));
    }

    /**
     * 新增文件
     *
     * @param OssResultDto 存储
     */
    public OssFileVo getResult(OssResultDto OssResultDto) {
        OssFile ossFile = new OssFile();
        ossFile.setOldFileName(OssResultDto.getOldFileName());
        ossFile.setUrl(OssConfig.OSS_DNS + "/" + OssResultDto.getFileName());
        ossFile.setFileName(OssResultDto.getFileName());
        ossFile.setFileSize(OssResultDto.getFileSize());
        ossFile.setClearFlag(DbConstant.DB_STATUS_0);
        ossFileService.save(ossFile);
        return ossFileConvert.poToVo(ossFile);
    }
}
