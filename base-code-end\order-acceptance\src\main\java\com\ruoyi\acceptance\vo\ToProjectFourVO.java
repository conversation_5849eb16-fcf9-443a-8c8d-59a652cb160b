package com.ruoyi.acceptance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基本信息对象 ToProjectVo
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@ApiModel(value = "ToProjectFourVO", description = "项目基本信息对象VO")
public class ToProjectFourVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /** 标题 */
    @ApiModelProperty(value = "标题", name="title")
    private String title;
    /** 选择类目， */
    @ApiModelProperty(value = "选择类目，按逗号分隔", name="categorySelectionOne")
    private String categorySelectionOne;
    /** 选择类目 */
    @ApiModelProperty(value = "选择类目，按逗号分隔", name="categorySelectionTwo")
    private String categorySelectionTwo;
    /** 期望交付时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "期望交付时间", name="expectedDeliveryTime")
    private Date expectedDeliveryTime;
    private BigDecimal totalPrice;
    private BigDecimal advancePrice;
    /** 追加服务 */
    @ApiModelProperty(value = "追加服务", name="additionalServices")
    private String additionalServices;
    /** 需求描述 */
    @ApiModelProperty(value = "需求描述", name="requirementDescription")
    private String requirementDescription;
    private String attachments;
    private Integer orderPayStatus;
    private String customerOpenid;
    private String headSculpture;

}
