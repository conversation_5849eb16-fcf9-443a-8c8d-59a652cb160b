package com.ruoyi.acceptance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.acceptance.domain.ToGroupBuying;
import com.ruoyi.acceptance.dto.GroupInfoTwoDTO;
import com.ruoyi.acceptance.dto.HeadSculptureDTO;
import com.ruoyi.acceptance.vo.GroupInfoVO;
import com.ruoyi.acceptance.vo.MyGroupOrderVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 团购信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-11
 */
@Repository
public interface ToGroupBuyingMapper extends BaseMapper<ToGroupBuying> {
    List<GroupInfoVO> getGroupInfoByGroupId(String groupId);

    Date getCreateTimeByMainOrder(String groupId);

    GroupInfoTwoDTO selectGroupCountdown(Long orderId);

    List<GroupInfoTwoDTO> selectGroupCountdownByOpenId(@Param("groupId") String groupId,@Param("openId") String openId);

    List<Integer> selectOrderStatusByCroup(Long orderId);

    List<Integer> selectOrderStatusByCroupByGroupId(String groupId);

    List<HeadSculptureDTO> selectHeadSculpture(Long orderId);

    List<HeadSculptureDTO> selectHeadSculptureByGroupId(String groupId);

    List<MyGroupOrderVO> myGroupOrderList(String openId);
}
