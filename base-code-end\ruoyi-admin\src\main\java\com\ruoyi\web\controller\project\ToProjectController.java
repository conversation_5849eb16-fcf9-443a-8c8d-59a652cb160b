package com.ruoyi.web.controller.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.app.manager.ToProjectManager;
import com.ruoyi.app.vo.ToProjectVo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 项目基本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Api(tags = "项目基本信息管理")
@RestController
@RequestMapping("/acceptance/project")
public class ToProjectController {

    @Autowired
    private ToProjectManager toProjectManager;

    /**
     * 分页查询项目基本信息
     */
    @ApiOperation("分页查询项目基本信息")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('acceptance:project:list')")
    @GetMapping("/list")
    public R<IPage<ToProjectVo>> list(ToProjectVo toProjectVo, Query query)
    {
        return R.ok(toProjectManager.page(toProjectVo, query));
    }

    /**
    * 查询项目基本信息全部列表
    */
    @ApiOperation("查询项目基本信息全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('acceptance:project:list')")
    @GetMapping("/allList")
    public R<List<ToProjectVo>> allList(ToProjectVo toProjectVo){
        return R.ok(toProjectManager.list(toProjectVo));
    }

    /**
     * 导出项目基本信息列表
     */
    @ApiOperation("导出项目基本信息列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('acceptance:project:export')")
    @Log(title = "项目基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToProjectVo toProjectVo, Query query)
    {
        List<ToProjectVo> list = toProjectManager.page(toProjectVo, query).getRecords();
        ExcelUtil<ToProjectVo> util = new ExcelUtil<ToProjectVo>(ToProjectVo.class);
        util.exportExcel(response, list, "项目基本信息数据");
    }

    /**
     * 获取项目基本信息详细信息
     */
    @ApiOperation("获取项目基本信息详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:project:query')")
    @GetMapping(value = "/{id}")
    public R<ToProjectVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toProjectManager.getInfo(id));
    }

    /**
     * 新增项目基本信息
     */
    @ApiOperation("新增项目基本信息")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('acceptance:project:add')")
    @Log(title = "项目基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToProjectVo toProjectVo)
    {
        return toProjectManager.add(toProjectVo) ? R.ok() : R.fail();
    }

    /**
     * 修改项目基本信息
     */
    @ApiOperation("修改项目基本信息")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('acceptance:project:edit')")
    @Log(title = "项目基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToProjectVo toProjectVo)
    {
        return toProjectManager.edit(toProjectVo) ? R.ok() : R.fail();
    }

    /**
     * 删除项目基本信息
     */
    @ApiOperation("删除项目基本信息")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:project:remove')")
    @Log(title = "项目基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toProjectManager.remove(ids) ? R.ok() : R.fail();
    }
}
