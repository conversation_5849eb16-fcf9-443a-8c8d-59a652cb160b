package ${packageName}.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
#if($table.sub)
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.domain.${subClassName};
#end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * ${functionName}Service业务层处理
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper,${ClassName}> implements I${ClassName}Service {

    @Autowired
    private ${ClassName}Mapper ${className}Mapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<${ClassName}> queryWrapper(${ClassName} ${className}) {
        LambdaQueryWrapper<${ClassName}> queryWrapper = new LambdaQueryWrapper<>();
#foreach($column in $columns)
#if($column.isQuery == "1")
#set($columnFirst = $hutoolStrUtil.upperFirst($column.javaField))
#if($column.queryType == "EQ")
        queryWrapper.eq(ObjUtil.isNotEmpty(${className}.get$columnFirst()), ${ClassName}::get$columnFirst, ${className}.get$columnFirst());
#elseif($queryType == "NE")
        queryWrapper.ne(ObjUtil.isNotEmpty(${className}.get$columnFirst()), ${ClassName}::get$columnFirst, ${className}.get$columnFirst());
#elseif($queryType == "GT")
        queryWrapper.gt(ObjUtil.isNotEmpty(${className}.get$columnFirst()), ${ClassName}::get$columnFirst, ${className}.get$columnFirst());
#elseif($queryType == "GTE")
        queryWrapper.ge(ObjUtil.isNotEmpty(${className}.get$columnFirst()), ${ClassName}::get$columnFirst, ${className}.get$columnFirst());
#elseif($queryType == "LT")
        queryWrapper.lt(ObjUtil.isNotEmpty(${className}.get$columnFirst()), ${ClassName}::get$columnFirst, ${className}.get$columnFirst());
#elseif($queryType == "LTE")
        queryWrapper.le(ObjUtil.isNotEmpty(${className}.get$columnFirst()), ${ClassName}::get$columnFirst, ${className}.get$columnFirst());
#elseif($queryType == "LIKE")
        queryWrapper.like(ObjUtil.isNotEmpty(${className}.get$columnFirst()), ${ClassName}::get$columnFirst, ${className}.get$columnFirst());
#end
#end
#end
        return queryWrapper;
    }

    /**
     * 查询${functionName}分页
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public IPage<${ClassName}> pages(${ClassName} ${className}, IPage<${ClassName}> page)
    {
        return ${className}Mapper.selectPage(page, this.queryWrapper(${className}));
    }

    /**
     * 查询${functionName}列表
     * 
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}> selectList(${ClassName} ${className})
    {
        return ${className}Mapper.selectList(this.queryWrapper(${className}));
    }

#if($table.sub)
    /**
     * 新增${functionName}
     * 
     * @param ${className} ${functionName}
     * @return 结果
     */
    @Transactional
    @Override
    public int insert${ClassName}(${ClassName} ${className})
    {
        int rows = ${className}Mapper.insert${ClassName}(${className});
        insert${subClassName}(${className});
        return rows;
    }

    /**
     * 修改${functionName}
     * 
     * @param ${className} ${functionName}
     * @return 结果
     */
    @Transactional
    @Override
    public int update${ClassName}(${ClassName} ${className})
    {

        ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${className}.get${pkColumn.capJavaField}());
        insert${subClassName}(${className});
        return ${className}Mapper.update${ClassName}(${className});
    }

    /**
     * 批量删除${functionName}
     * 
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键
     * @return 结果
     */
    @Transactional
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        ${className}Mapper.delete${subClassName}By${subTableFkClassName}s(${pkColumn.javaField}s);

    }

    /**
     * 删除${functionName}信息
     * 
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return 结果
     */

    @Transactional
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField})
    {
        ${className}Mapper.delete${subClassName}By${subTableFkClassName}(${pkColumn.javaField});
        return ${className}Mapper.delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }

    /**
     * 新增${subTable.functionName}信息
     * 
     * @param ${className} ${functionName}对象
     */
    public void insert${subClassName}(${ClassName} ${className})
    {
        List<${subClassName}> ${subclassName}List = ${className}.get${subClassName}List();
        ${pkColumn.javaType} ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
        if (StringUtils.isNotNull(${subclassName}List))
        {
            List<${subClassName}> list = new ArrayList<${subClassName}>();
            for (${subClassName} ${subclassName} : ${subclassName}List)
            {
                ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                list.add(${subclassName});
            }
            if (list.size() > 0)
            {
                ${className}Mapper.batch${subClassName}(list);
            }
        }
    }
#end
}
