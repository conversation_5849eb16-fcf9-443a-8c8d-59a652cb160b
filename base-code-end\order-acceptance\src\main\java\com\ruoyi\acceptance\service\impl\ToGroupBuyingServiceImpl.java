package com.ruoyi.acceptance.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.acceptance.domain.ToGroupBuying;
import com.ruoyi.acceptance.dto.GroupInfoTwoDTO;
import com.ruoyi.acceptance.dto.HeadSculptureDTO;
import com.ruoyi.acceptance.dto.ToPaperDTO;
import com.ruoyi.acceptance.mapper.ToGroupBuyingMapper;
import com.ruoyi.acceptance.mapper.ToPaperMapper;
import com.ruoyi.acceptance.service.IToGroupBuyingService;
import com.ruoyi.acceptance.vo.GroupInfoVO;
import com.ruoyi.acceptance.vo.MyGroupOrderVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.acceptance.mapper.ToUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.ruoyi.common.constant.AcceptanceConstants.*;
import static com.ruoyi.common.constant.OrderAcceptanceConstant.GROUP_DAYS;
import static com.ruoyi.common.constant.OrderAcceptanceConstant.GROUP_SUCCESS_PEOPLE_NUM;


/**
 * 团购信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-11
 */
@Service
public class ToGroupBuyingServiceImpl extends ServiceImpl<ToGroupBuyingMapper,ToGroupBuying> implements IToGroupBuyingService {

    @Autowired
    private ToGroupBuyingMapper toGroupBuyingMapper;
    @Autowired
    private ToUserMapper toUserMapper;
    @Autowired
    private ToPaperMapper toPaperMapper;
    @Autowired
    private ISysConfigService sysConfigService;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToGroupBuying> queryWrapper(ToGroupBuying toGroupBuying) {
        LambdaQueryWrapper<ToGroupBuying> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toGroupBuying.getOrderId()), ToGroupBuying::getOrderId, toGroupBuying.getOrderId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toGroupBuying.getGroupId()), ToGroupBuying::getGroupId, toGroupBuying.getGroupId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toGroupBuying.getOpenId()), ToGroupBuying::getOpenId, toGroupBuying.getOpenId());
        return queryWrapper;
    }

    /**
     * 查询团购信息分页
     *
     * @param toGroupBuying 团购信息
     * @return 团购信息
     */
    @Override
    public IPage<ToGroupBuying> pages(ToGroupBuying toGroupBuying, IPage<ToGroupBuying> page)
    {
        return toGroupBuyingMapper.selectPage(page, this.queryWrapper(toGroupBuying));
    }

    /**
     * 查询团购信息列表
     * 
     * @param toGroupBuying 团购信息
     * @return 团购信息
     */
    @Override
    public List<ToGroupBuying> selectList(ToGroupBuying toGroupBuying)
    {
        return toGroupBuyingMapper.selectList(this.queryWrapper(toGroupBuying));
    }

    @Override
    public R saveToGroupBuying(Long orderId) {
        LambdaQueryWrapper<ToGroupBuying> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ToGroupBuying::getOrderId,orderId).eq(ToGroupBuying::getWhetherOutGroup,0);
        ToGroupBuying toGroupBuyingFromDb = toGroupBuyingMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(toGroupBuyingFromDb)){
            return R.fail("该订单已团购！");
        }
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        ToGroupBuying toGroupBuying = new ToGroupBuying();
        toGroupBuying.setOrderId(orderId);
        String groupId = RandomUtil.randomString(8);
        toGroupBuying.setGroupId(groupId);
        toGroupBuying.setOpenId(openId);
        toGroupBuying.setGroupMainOrder(1);
        toGroupBuying.setWhetherOutGroup(0);
        int insert = toGroupBuyingMapper.insert(toGroupBuying);
        if (insert>0){
            return R.ok("申请团购成功！");
        }else {
            return R.fail("申请团购失败！");
        }
    }

    @Override
    public boolean acceptGroupBuying(ToGroupBuying toGroupBuying) {
        // 同一个订单只能参与一次团购
        LambdaQueryWrapper<ToGroupBuying> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ToGroupBuying::getOrderId, toGroupBuying.getOrderId());
        ToGroupBuying toGroupBuying1 = toGroupBuyingMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(toGroupBuying1)){
            return false;
        }else {
            WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
            String openId= wxLoginUser.getOpenid();
            toGroupBuying.setOpenId(openId);
            toGroupBuying.setWhetherOutGroup(0);
            int insert = toGroupBuyingMapper.insert(toGroupBuying);
            if (insert > 0){
                return true;
            }else {
                return false;
            }
        }
    }

    @Override
    public GroupInfoVO getGroupInfoByPaperId(String groupId) {
        GroupInfoVO result = new GroupInfoVO();
        List<GroupInfoVO> groupInfoByGroupId = toGroupBuyingMapper.getGroupInfoByGroupId(groupId);
        // 判断是否空团
        if(StringUtils.isEmpty(groupInfoByGroupId)){
            result.setWhetherEmptyGroup(1);
            return result;
        }
        result = groupInfoByGroupId.get(0);
        result.setWhetherEmptyGroup(0);
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        List<ToPaperDTO> toPaperDTOS = toPaperMapper.orderStatusListForGroup(openId);
        result.setPaperList(toPaperDTOS);
        List<HeadSculptureDTO> headSculptureDTOS = toGroupBuyingMapper.selectHeadSculptureByGroupId(groupId);
        result.setHeadSculptureDTOS(headSculptureDTOS);
        //判断团购状态
        Date createTimeByMainOrder = toGroupBuyingMapper.getCreateTimeByMainOrder(groupId);
        Calendar now1 = Calendar.getInstance();
        Date currentDate = now1.getTime();
        long groupDaysHavePassed = currentDate.getTime() - createTimeByMainOrder.getTime();
        String temp1 = sysConfigService.selectConfigByKey(GROUP_DAYS);
        long groupDays = Integer.parseInt(temp1)*24*60*60;
        long groupDaysRemaining = groupDays-groupDaysHavePassed / 1000;
        List<GroupInfoTwoDTO> groupInfoTwoDTOS = toGroupBuyingMapper.selectGroupCountdownByOpenId(groupId, openId);
        if (StringUtils.isNull(groupInfoTwoDTOS)){//未团购
            result.setGroupStatus(GROUP_PURCHASE_NOT_START);
            result.setGroupEndFlag(NO);
            result.setGroupDaysRemaining(groupDaysRemaining);
        }else {
            if (groupDaysRemaining<ZERO){
                result.setGroupEndFlag(YES);
                Integer groupSuccessPeopleNum = Integer.parseInt(sysConfigService.selectConfigByKey(GROUP_SUCCESS_PEOPLE_NUM));
                List<Integer> integers = toGroupBuyingMapper.selectOrderStatusByCroupByGroupId(groupId);
                if (integers.contains(REFUNDABLE_DEPOSIT)){//5团购倒计时结束
                    result.setGroupStatus(GROUP_PURCHASE_END);
                    result.setGroupDaysRemaining(-1l);
                }else {
                    if (integers.size() >= groupSuccessPeopleNum) {
                        if (integers.contains(REFUNDED)||integers.contains(DEPOSIT_TO_PAID)){//团购失败
                            result.setGroupStatus(GROUP_PURCHASE_FAIL);
                            result.setGroupDaysRemaining(-1l);
                        }else {//团购成功
                            result.setGroupStatus(GROUP_PURCHASE_SUCCESSFUL);
                            result.setGroupDaysRemaining(-1l);
                        }
                    }else {//团购失败
                        result.setGroupStatus(GROUP_PURCHASE_FAIL);
                        result.setGroupDaysRemaining(-1l);
                    }
                }
            }else {//团购中
                result.setGroupStatus(GROUP_BUYING);
                result.setGroupEndFlag(NO);
                result.setGroupDaysRemaining(groupDaysRemaining);
            }
        }
        return result;
    }

    @Override
    public boolean addGroup(ToGroupBuying toGroupBuying) {
        LambdaQueryWrapper<ToGroupBuying> lambdaQueryWrapper= new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ToGroupBuying::getOrderId,toGroupBuying.getOrderId());
        ToGroupBuying toGroupBuying1 = toGroupBuyingMapper.selectOne(lambdaQueryWrapper);
        if (StringUtils.isNotNull(toGroupBuying1)){
            toGroupBuying.setId(toGroupBuying1.getId());
            toGroupBuyingMapper.updateById(toGroupBuying);
        }else {

        }
        return false;
    }

    @Override
    public GroupInfoTwoDTO selectGroupCountdown(Long orderId) {
        return toGroupBuyingMapper.selectGroupCountdown(orderId);
    }

    @Override
    public List<MyGroupOrderVO> myGroupOrderList() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId = wxLoginUser.getOpenid();
        List<MyGroupOrderVO> myGroupOrderVOS = toGroupBuyingMapper.myGroupOrderList(openId);
        for (MyGroupOrderVO myGroupOrderVO : myGroupOrderVOS){
            String groupId = myGroupOrderVO.getGroupId();
            //团购成员头像
            List<HeadSculptureDTO> headSculptureDTOS = toGroupBuyingMapper.selectHeadSculptureByGroupId(groupId);
            myGroupOrderVO.setHeadSculptureDTOS(headSculptureDTOS);
            //判断团购状态
            Date createTimeByMainOrder = toGroupBuyingMapper.getCreateTimeByMainOrder(groupId);
            Calendar now1 = Calendar.getInstance();
            Date currentDate = now1.getTime();
            long groupDaysHavePassed = currentDate.getTime() - createTimeByMainOrder.getTime();
            String temp1 = sysConfigService.selectConfigByKey(GROUP_DAYS);
            long groupDays = Integer.parseInt(temp1)*24*60*60;
            long groupDaysRemaining = groupDays-groupDaysHavePassed / 1000;
            List<GroupInfoTwoDTO> groupInfoTwoDTOS = toGroupBuyingMapper.selectGroupCountdownByOpenId(groupId, openId);
            if (StringUtils.isNull(groupInfoTwoDTOS)){//未团购
                myGroupOrderVO.setGroupStatus(GROUP_PURCHASE_NOT_START);
                myGroupOrderVO.setGroupEndFlag(NO);
            }else {
                if (groupDaysRemaining<ZERO){
                    myGroupOrderVO.setGroupEndFlag(YES);
                    Integer groupSuccessPeopleNum = Integer.parseInt(sysConfigService.selectConfigByKey(GROUP_SUCCESS_PEOPLE_NUM));
                    List<Integer> integers = toGroupBuyingMapper.selectOrderStatusByCroupByGroupId(groupId);
                    if (integers.contains(REFUNDABLE_DEPOSIT)){//5团购倒计时结束
                        myGroupOrderVO.setGroupStatus(GROUP_PURCHASE_END);
                    }else {
                        if (integers.size() >= groupSuccessPeopleNum) {
                            if (integers.contains(REFUNDED)||integers.contains(DEPOSIT_TO_PAID)){//团购失败
                                myGroupOrderVO.setGroupStatus(GROUP_PURCHASE_FAIL);
                            }else {//团购成功
                                myGroupOrderVO.setGroupStatus(GROUP_PURCHASE_SUCCESSFUL);
                            }
                        }else {//团购失败
                            myGroupOrderVO.setGroupStatus(GROUP_PURCHASE_FAIL);
                        }
                    }
                }else {//团购中
                    myGroupOrderVO.setGroupStatus(GROUP_BUYING);
                    myGroupOrderVO.setGroupEndFlag(NO);
                }
            }
        }
        return myGroupOrderVOS;
    }

}
