package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.domain.ToWalletHistory;
import com.ruoyi.acceptance.service.IToWalletHistoryService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.app.vo.ToWalletHistoryVo;
import com.ruoyi.app.vo.convert.ToWalletHistoryConvert;

import java.util.List;

/**
 * 钱包流水Manager
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Component
public class ToWalletHistoryManager {

    @Autowired
    private IToWalletHistoryService toWalletHistoryService;

    @Autowired
    private ToWalletHistoryConvert toWalletHistoryConvert;

    /**
    * 分页查询钱包流水
    */
    public IPage<ToWalletHistoryVo> page(ToWalletHistoryVo toWalletHistoryVo, Query query){
        IPage<ToWalletHistory> page = toWalletHistoryService.pages(toWalletHistoryConvert.voToPo(toWalletHistoryVo), Condition.getPage(query));
        return (IPage<ToWalletHistoryVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToWalletHistoryVo>(), ToWalletHistoryConvert.INSTANCE);
    }

    /**
    * 查询钱包流水列表
    */
    public List<ToWalletHistoryVo> list(ToWalletHistoryVo toWalletHistoryVo) {
        List<ToWalletHistory> toWalletHistoryList = toWalletHistoryService.selectList(toWalletHistoryConvert.voToPo(toWalletHistoryVo));
        return toWalletHistoryConvert.poToVoList(toWalletHistoryList);
    }

    /**
     * 查询钱包流水详细信息
     */
    public ToWalletHistoryVo getInfo(Long id) {
        ToWalletHistory toWalletHistory = toWalletHistoryService.getById(id);
        return toWalletHistoryConvert.poToVo(toWalletHistory);
    }

    /**
     * 新增钱包流水
     */
    public boolean add(ToWalletHistoryVo toWalletHistoryVo) {
        return toWalletHistoryService.save(toWalletHistoryConvert.voToPo(toWalletHistoryVo));
    }

    /**
     * 修改钱包流水
     */
    public boolean edit(ToWalletHistoryVo toWalletHistoryVo) {
        return toWalletHistoryService.updateById(toWalletHistoryConvert.voToPo(toWalletHistoryVo));
    }

    /**
     * 批量删除钱包流水
     */
    public boolean remove(Long[] ids) {
        return toWalletHistoryService.removeByIds(CollUtil.toList(ids));
    }

    public R<?> withdrawalList(String yearMonth) {
        return toWalletHistoryService.withdrawalList(yearMonth);
    }
}
