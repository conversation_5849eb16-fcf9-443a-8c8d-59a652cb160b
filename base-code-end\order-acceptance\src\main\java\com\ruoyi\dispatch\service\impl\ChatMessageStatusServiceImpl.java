package com.ruoyi.dispatch.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.dispatch.mapper.ChatMessageStatusMapper;
import com.ruoyi.dispatch.domain.ChatMessageStatus;
import com.ruoyi.dispatch.service.IChatMessageStatusService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 消息状态Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class ChatMessageStatusServiceImpl extends ServiceImpl<ChatMessageStatusMapper,ChatMessageStatus> implements IChatMessageStatusService {

    @Autowired
    private ChatMessageStatusMapper chatMessageStatusMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ChatMessageStatus> queryWrapper(ChatMessageStatus chatMessageStatus) {
        LambdaQueryWrapper<ChatMessageStatus> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessageStatus.getMessageId()), ChatMessageStatus::getMessageId, chatMessageStatus.getMessageId());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessageStatus.getUserId()), ChatMessageStatus::getUserId, chatMessageStatus.getUserId());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessageStatus.getIsRead()), ChatMessageStatus::getIsRead, chatMessageStatus.getIsRead());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessageStatus.getReadTime()), ChatMessageStatus::getReadTime, chatMessageStatus.getReadTime());
        return queryWrapper;
    }

    /**
     * 查询消息状态分页
     *
     * @param chatMessageStatus 消息状态
     * @return 消息状态
     */
    @Override
    public IPage<ChatMessageStatus> pages(ChatMessageStatus chatMessageStatus, IPage<ChatMessageStatus> page)
    {
        return chatMessageStatusMapper.selectPage(page, this.queryWrapper(chatMessageStatus));
    }

    /**
     * 查询消息状态列表
     * 
     * @param chatMessageStatus 消息状态
     * @return 消息状态
     */
    @Override
    public List<ChatMessageStatus> selectList(ChatMessageStatus chatMessageStatus)
    {
        return chatMessageStatusMapper.selectList(this.queryWrapper(chatMessageStatus));
    }

}
