package com.ruoyi.dispatch.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.PoBaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 会话成员对象 chat_conversation_members
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@TableName("chat_conversation_members")
public class ChatConversationMembers extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 会话ID */
    @TableField(value = "conversation_id")
    private Long conversationId;

    /** 用户ID */
    @TableField(value = "user_openid")
    private String userOpenid;

    /** 加入时间 */
    @TableField(value = "join_time")
    private Date joinTime;

    /** 角色(0-普通成员,1-管理员,2-群主) */
    @TableField(value = "role")
    private Integer role;

}
