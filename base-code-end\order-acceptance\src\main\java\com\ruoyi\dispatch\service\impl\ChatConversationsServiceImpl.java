package com.ruoyi.dispatch.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.acceptance.vo.ChatConversationsTwoVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.DispatchLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.dispatch.domain.ChatConversationMembers;
import com.ruoyi.dispatch.domain.ChatConversations;
import com.ruoyi.dispatch.mapper.ChatConversationMembersMapper;
import com.ruoyi.dispatch.mapper.ChatConversationsMapper;
import com.ruoyi.dispatch.service.IChatConversationsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


/**
 * 会话Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class ChatConversationsServiceImpl extends ServiceImpl<ChatConversationsMapper,ChatConversations> implements IChatConversationsService {

    @Autowired
    private ChatConversationsMapper chatConversationsMapper;
    @Autowired
    private ChatConversationMembersMapper chatConversationMembersMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ChatConversations> queryWrapper(ChatConversations chatConversations) {
        LambdaQueryWrapper<ChatConversations> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }

    /**
     * 查询会话分页
     *
     * @param chatConversations 会话
     * @return 会话
     */
    @Override
    public IPage<ChatConversations> pages(ChatConversations chatConversations, IPage<ChatConversations> page)
    {
        return chatConversationsMapper.selectPage(page, this.queryWrapper(chatConversations));
    }

    /**
     * 查询会话列表
     * 
     * @param chatConversations 会话
     * @return 会话
     */
    @Override
    public List<ChatConversations> selectList(ChatConversations chatConversations)
    {
        return chatConversationsMapper.selectList(this.queryWrapper(chatConversations));
    }

    @Override
    public R<?> saveConversation(ChatConversationsTwoVO chatConversationsTwoVO) {
        DispatchLoginUser dispatchLoginUser = SecurityUtils.getDispatchLoginUser();
        String merchantOpenid= dispatchLoginUser.getOpenid();
        ChatConversations chatConversations = new ChatConversations();
        chatConversations.setConversationName(merchantOpenid+"_"+chatConversationsTwoVO.getCustomerOpenid());
        chatConversations.setConversationType(1);

        chatConversationsMapper.insert(chatConversations);

        ChatConversationMembers chatConversationMembers = new ChatConversationMembers();
        chatConversationMembers.setConversationId(chatConversations.getConversationId());
        chatConversationMembers.setUserOpenid(merchantOpenid);
        chatConversationMembers.setJoinTime(new Date(System.currentTimeMillis()));
        chatConversationMembers.setRole(0);
        chatConversationMembersMapper.insert(chatConversationMembers);

        ChatConversationMembers chatConversationMembers1 = new ChatConversationMembers();
        chatConversationMembers1.setConversationId(chatConversations.getConversationId());
        chatConversationMembers1.setUserOpenid(chatConversationsTwoVO.getCustomerOpenid());
        chatConversationMembers1.setJoinTime(new Date(System.currentTimeMillis()));
        chatConversationMembers1.setRole(0);
        chatConversationMembersMapper.insert(chatConversationMembers1);

        return R.ok(chatConversations.getConversationId());
    }

}
