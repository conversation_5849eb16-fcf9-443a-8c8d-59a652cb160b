<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToWalletHistoryMapper">

    <select id="withdrawalList" resultType="com.ruoyi.acceptance.dto.WithdrawalListDTO">
        SELECT A.withdrawn_amount withdrawnAmount, A.create_time createTime, C.balance ,B.head_sculpture headSculpture,B.nickname FROM to_wallet_history A
        LEFT JOIN to_user B ON A.open_id = B.open_id
        LEFT JOIN to_wallet C ON A.open_id = C.open_id
        WHERE 1=1
        AND  A.open_id = #{openId}
        AND  DATE_FORMAT(A.create_time, '%Y-%m') = #{yearMonth}
    </select>
</mapper>