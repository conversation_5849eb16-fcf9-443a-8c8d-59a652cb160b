package com.ruoyi.acceptance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.domain.ToOrder;
import com.ruoyi.acceptance.dto.ToOrderDTO;
import com.ruoyi.acceptance.vo.ToOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 接单订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Repository
public interface ToOrderMapper extends BaseMapper<ToOrder> {
    IPage<ToOrderDTO> selectOrders(@Param("toOrderVo")ToOrderVo toOrderVo,@Param("page") IPage<ToOrderDTO> page);

    ToOrderDTO getInfoForPc(Long id);
}
