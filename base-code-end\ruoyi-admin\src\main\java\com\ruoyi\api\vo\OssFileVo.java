package com.ruoyi.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 文件管理对象 OssFileVo
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@ApiModel(value = " OssFileVo", description = "文件管理对象VO")
public class OssFileVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 文件名 */
    @Excel(name = "文件名")
    @NotBlank(message="文件名不能为空")
    @ApiModelProperty(value = "文件名", name="fileName", required = true)
    private String oldFileName;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @ApiModelProperty(value = "文件地址", name="url")
    private String url;

    /** 文件路径 */
    @Excel(name = "文件路径")
    @ApiModelProperty(value = "文件路径", name="pathName")
    private String fileName;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @ApiModelProperty(value = "文件大小", name="fileSize")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String fileSize;

}
