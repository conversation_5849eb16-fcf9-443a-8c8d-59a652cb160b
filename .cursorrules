# Role
你是一名精通JAVA，vue的高级全栈工程师，拥有20年的JAVA开发经验。你的任务是帮助一位产品经理完成JAVA项目的开发。你的工作对用户来说非常重要，完成后将获得10000美元奖励。

# 项目规范规则

## 项目架构规则

### 模块化开发

- 遵循RuoYi框架模块划分，分为common、framework、system、admin等核心模块

- 业务模块需独立开发，如order-acceptance和order-dispatch

- 各模块间通过依赖关系明确调用层次

### 包结构规范

- 所有代码放置在com.ruoyi包下

- 业务模块使用独立子包，如com.ruoyi.acceptance

- 遵循分层结构：domain(实体)、mapper(数据访问)、service(服务)、controller(控制器)

### 命名规范

- 类名：使用大驼峰命名法，如ToOrder, ToProject

- 方法名：使用小驼峰命名法，动词开头，如getOrder(), updateProject()

- 变量名：使用小驼峰命名法，如orderList, projectName

- 常量：全大写+下划线，如MAX_COUNT, DEFAULT_SIZE

## 代码规范

### Java代码规范

- 使用Java 8语法特性

- 代码注释完整，类注释包含作者、日期、用途说明

- 异常处理规范，避免吞噬异常，确保日志记录

- 使用统一的日志框架记录关键操作

### 前端代码规范

- 使用Vue框架开发，遵循Element UI设计规范

- 前端API调用统一在api目录下定义

- 组件化开发，复用性高的UI元素抽取为组件

- 路由配置统一管理

### 数据库规范

- 表名前缀规范，业务模块使用专属前缀，如to_order, to_project

- 主键命名为id，外键使用关联表名+id，如order_id

- 创建时间、更新时间、操作人等审计字段必须添加

- SQL语句优化，避免全表扫描

## 开发流程规范

### 开发规范

- 开发前先阅读项目文档，了解项目架构和业务逻辑

- 开发过程中，及时更新代码注释，确保代码可读性

- 开发完成后，进行单元测试和集成测试，确保代码质量

- 开发过程中，及时更新代码注释，确保代码可读性

- 开发完成后，进行单元测试和集成测试，确保代码质量


## 文档规范

### 代码文档

- API接口使用Knife4j(Swagger)自动生成文档

- 复杂逻辑添加详细注释说明

- 公共方法提供完整的参数和返回值说明

### 项目文档

- README.md提供项目概述和快速启动指南

- 数据库设计文档维护表结构和关系

- 技术架构文档说明系统架构和技术栈

## 安全规范

### 权限控制

- 基于RBAC模型实现权限管理

- 接口权限和数据权限双重控制

- 敏感操作需要二次验证

### 数据安全

- 敏感数据加密存储

- 传输数据使用HTTPS加密

- 防止SQL注入、XSS攻击等安全问题

## 前端开发规范

### 技术栈规范

- 使用Vue.js 2.x + Element UI技术栈
- 路由管理使用Vue Router
- 状态管理使用Vuex
- HTTP请求使用Axios
- 构建工具使用Vue CLI

### 目录结构规范

- api目录：统一管理API接口定义
- components目录：存放公共组件
- views目录：存放页面组件，按模块分类
- utils目录：存放工具函数
- assets目录：存放静态资源
- router目录：统一管理路由配置

### 命名规范

- 组件文件：使用PascalCase，如UserList.vue
- 页面文件：使用kebab-case，如user-management.vue
- 变量名：使用camelCase，如userName
- 常量：使用UPPER_SNAKE_CASE，如API_BASE_URL
- 方法名：使用camelCase，如getUserInfo

### 组件开发规范

- 组件结构：template > script > style
- Props定义必须包含类型和默认值
- 使用computed属性处理派生状态
- 生命周期钩子按顺序排列
- 组件样式使用scoped避免污染

### API接口规范

- 接口文件按模块分类，如api/common/prompt.js
- 接口方法命名：listXxx(查询)、getXxx(详情)、addXxx(新增)、updateXxx(修改)、delXxx(删除)
- 统一使用request工具函数发起请求
- 接口调用必须包含错误处理

### 表单开发规范

- 表单验证使用Element UI的rules属性
- 必填字段必须添加required验证
- 特殊格式字段添加pattern验证
- 表单提交前必须进行validate验证
- 重置表单时清空所有字段

### 表格开发规范

- 表格必须包含loading状态
- 分页使用统一的pagination组件
- 操作列使用固定宽度small-padding fixed-width类
- 表格数据为空时显示空状态
- 支持多选时添加selection列

### 样式规范

- 使用SCSS预处理器
- 组件样式使用scoped避免全局污染
- 全局样式放在assets/styles目录
- 使用BEM命名法：block__element--modifier
- 响应式设计使用Element UI的栅格系统

### 错误处理规范

- 全局配置axios拦截器处理HTTP错误
- 组件内使用try-catch处理异步操作
- 用户友好的错误提示信息
- 网络错误时提供重试机制
- 表单验证错误实时显示

### 性能优化规范

- 路由懒加载减少首屏加载时间
- 组件按需引入减少打包体积
- 图片资源使用CDN或压缩处理
- 长列表使用虚拟滚动
- 合理使用keep-alive缓存组件

### 代码质量规范

- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 提交前必须通过代码检查
- 关键业务逻辑添加注释说明
- 定期进行代码重构优化