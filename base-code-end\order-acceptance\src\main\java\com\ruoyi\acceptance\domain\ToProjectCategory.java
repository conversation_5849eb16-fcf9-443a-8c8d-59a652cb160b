package com.ruoyi.acceptance.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 项目类目信息对象 to_project_category
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@TableName("to_project_category")
public class ToProjectCategory extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 类目编码 */
    @TableField(value = "category_code")
    private String categoryCode;

    /** 上级类目编码 */
    @TableField(value = "parent_code")
    private String parentCode;

    /** 类目名称 */
    @TableField(value = "category_name")
    private String categoryName;

    /** 类目顺序 */
    @TableField(value = "category_no")
    private Long categoryNo;

    /** 祖级列表 */
    @TableField(value = "ancestors")
    private String ancestors;

}
