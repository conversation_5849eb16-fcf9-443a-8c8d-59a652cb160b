package com.ruoyi.acceptance.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.acceptance.dto.HeadSculptureDTO;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 论文管理对象 ToPaperVo
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
@ApiModel(value = " ToPaperVo", description = "论文管理对象VO")
public class ToPaperVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 订单id */
    @Excel(name = "订单id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "订单id", name="orderId")
    private Long orderId;

    /** 标题 */
    @Excel(name = "标题")
    @ApiModelProperty(value = "标题", name="title")
    private String title;

    /** 技术选型 */
    @Excel(name = "技术选型")
    @ApiModelProperty(value = "技术选型", name="technicalSelection")
    private String technicalSelection;

    /** 包含服务 */
    @Excel(name = "包含服务")
    @ApiModelProperty(value = "包含服务", name="includingServices")
    private String includingServices;

    /** 简介 */
    @Excel(name = "简介")
    @ApiModelProperty(value = "简介", name="introduction")
    private String introduction;

    /** 团购成员（用户信息表id关联） */
    @Excel(name = "团购成员", readConverterExp = "用=户信息表id关联")
    @ApiModelProperty(value = "团购成员", name="buyMembers")
    private String buyMembers;

    /** 预付款价 */
    @Excel(name = "预付款价")
    @ApiModelProperty(value = "预付款价", name="advancePrice")
    private BigDecimal advancePrice;

    /** 尾款 */
    @Excel(name = "尾款")
    @ApiModelProperty(value = "尾款", name="balancePayment")
    private BigDecimal balancePayment;

    /** 定金支付时间 */
    @TableField(value = "deposit_payment_time")
    private Date depositPaymentTime;

    /** 尾款支付时间 */
    @TableField(value = "balance_payment_time")
    private Date balancePaymentTime;

    /** 全额价格 */
    @Excel(name = "全额价格")
    @ApiModelProperty(value = "全额价格", name="totalPrice")
    private BigDecimal totalPrice;

    /** 交付时间 */
    @Excel(name = "交付时间")
    @ApiModelProperty(value = "交付时间", name="deliveryTime")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryTime;

    /** 是否申请退款 */
    @Excel(name = "是否申请退款")
    @ApiModelProperty(value = "是否申请退款", name="applyRefund")
    private Integer applyRefund;

    /** 是否团购 */
    @Excel(name = "是否团购")
    @ApiModelProperty(value = "是否团购", name="groupPurchase")
    private Integer groupPurchase;

    /** 是否结清尾款 */
    @Excel(name = "是否结清尾款")
    @ApiModelProperty(value = "是否结清尾款", name="settlement")
    private Integer settlement;

    @Excel(name = "订单付款状态")
    @ApiModelProperty(value = "订单付款状态", name="orderPayStatus")
    private Integer orderPayStatus;

    @Excel(name = "客户")
    @ApiModelProperty(value = "客户", name="custom")
    private String custom;

    private Integer refundFlag;
    private Date createTime;
    //是否非卖品
    private Integer notSale;
    //团购倒计时时间差
    private Long groupDaysRemaining;
    //1为结束 0为未结束
    private Integer groupEndFlag;
    //1未团购 2团购中 3团购成功 4团购失败
    private Integer groupStatus;
    private String groupId;
    private List<HeadSculptureDTO> headSculptureDTOS;
}
