package com.ruoyi.app.vo;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import java.io.Serializable;

/**
 * 订单流水对象 ToOrderHistoryVo
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
@ApiModel(value = " ToOrderHistoryVo", description = "订单流水对象VO")
public class ToOrderHistoryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 订单ID */
    @Excel(name = "订单ID")
    @ApiModelProperty(value = "订单ID", name="orderId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /** 代理的OpenID */
    @Excel(name = "代理的OpenID")
    @ApiModelProperty(value = "代理的OpenID", name="openId")
    private String openId;

    /** 冻结金额 */
    @Excel(name = "冻结金额")
    @ApiModelProperty(value = "冻结金额", name="frozenAmount")
    private BigDecimal frozenAmount;

    /** 待提现金额 */
    @Excel(name = "待提现金额")
    @ApiModelProperty(value = "待提现金额", name="notWithdrawAmount")
    private BigDecimal notWithdrawAmount;

    /** 已提现金额 */
    @Excel(name = "已提现金额")
    @ApiModelProperty(value = "已提现金额", name="withdrawnAmount")
    private BigDecimal withdrawnAmount;

    /** 总金额 */
    @Excel(name = "总金额")
    @ApiModelProperty(value = "总金额", name="totalAmount")
    private BigDecimal totalAmount;

}
