package com.ruoyi.acceptance.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.domain.ToWalletHistory;
import com.ruoyi.common.core.domain.R;

/**
 * 钱包流水Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IToWalletHistoryService extends IService<ToWalletHistory> {

    /**
     * 分页查询钱包流水
     * @param toWalletHistory 钱包流水
     * @param page 分页条件
     * @return 钱包流水集合
     */
    IPage<ToWalletHistory> pages(ToWalletHistory toWalletHistory, IPage<ToWalletHistory> page);

    /**
     * 查询钱包流水列表
     * 
     * @param toWalletHistory 钱包流水
     * @return 钱包流水集合
     */
     List<ToWalletHistory> selectList(ToWalletHistory toWalletHistory);

    R<?> withdrawalList(String month);
}
