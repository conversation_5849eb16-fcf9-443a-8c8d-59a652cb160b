<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToOrderHistoryMapper">

    <select id="select1" resultType="java.math.BigDecimal">

        SELECT
            COUNT( order_id ) total_order_quantity
        FROM
            to_order_history t
                JOIN to_order t2 ON t.order_id = t2.id
        WHERE
            t.agent_open_id = #{openId}
        <if test="date == 'day'">
            AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 DAY );
        </if>
        <if test="date == 'week'">
            AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 WEEK );
        </if>
        <if test="date == 'year'">
            AND t.create_time >= DATE_SUB( NOW(), INTERVAL 1 YEAR );
        </if>


    </select>

    <select id="select2" resultType="com.ruoyi.acceptance.dto.DistributionReportDTO">
        SELECT
        SUM(total_amount) total_amount,
        SUM(frozen_amount) total_frozen_amount,
        SUM(not_withdraw_amount) total_not_withdraw_amount,
        SUM(withdrawn_amount) total_withdrawn_amount,
        COUNT(customer_open_id) total_customer_quantity
        FROM to_order_history
        WHERE  agent_open_id = #{openId}
        AND  create_time >= DATE_SUB(NOW(), INTERVAL 1 YEAR);



    </select>

    <select id="invitationRanking" resultType="com.ruoyi.acceptance.dto.InvitationRankingDTO">
        SELECT
            v1.open_id AS agentOpenId,
            v1.nickname,
            v1.head_sculpture,
            v2.invite_num AS total_customer_quantity
        FROM
                ( SELECT * FROM to_user t3 WHERE t3.whether_agent = 1 ) v1
                    JOIN (
                SELECT
                    t.invite_code,
                    count(*) AS invite_num
                FROM
                    to_user t
                WHERE
                    t.invite_code IN ( SELECT t2.share_code FROM to_user t2 WHERE t2.whether_agent = 1 )
                GROUP BY
                    t.invite_code
            ) v2 ON v1.share_code = v2.invite_code
        ORDER BY
            v2.invite_num DESC
            LIMIT 10 ;
    </select>

    <select id="commissionRanking" resultType="com.ruoyi.acceptance.dto.CommissionRankingDTO">
        SELECT
            t1.open_id as agentOpenId,
            t1.nickname,
            t1.head_sculpture,
            sum( t2.total_amount )  as total_amount
        FROM
            to_user t1
                JOIN to_order_history t2 ON t1.open_id = t2.agent_open_id
                JOIN to_order t3 ON t2.order_id = t3.id
        WHERE
            t1.whether_agent = 1
        GROUP BY
            t1.open_id,
            t1.nickname,
            t1.head_sculpture
    </select>

    <select id="transactionList" resultType="com.ruoyi.acceptance.dto.TransactionListDTO">
        SELECT
        A.order_id orderId,A.frozen_amount frozenAmount, A.not_withdraw_amount notWithdrawAmount, B.title,B.create_time createTime,C.order_pay_status orderPayStatus
        FROM to_order_history  A
        LEFT JOIN to_paper B  ON A.order_id = B.order_id
        LEFT JOIN to_order C  ON A.order_id = C.id
        WHERE A.agent_open_id = #{openId}
        <if test="date == 'day'">
            AND  B.create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY );
        </if>
        <if test="date == 'week'">
            AND  B.create_time >= DATE_SUB(NOW(), INTERVAL 1 WEEK );
        </if>
        <if test="date == 'year'">
            AND  B.create_time >= DATE_SUB(NOW(), INTERVAL 1 YEAR );
        </if>

    </select>

    <select id="selectSumFrozenAmount" resultType="BigDecimal">
        SELECT SUM(frozen_amount) sum_frozen_amount FROM (
        SELECT
        A.order_id,A.frozen_amount, A.not_withdraw_amount, B.title,B.create_time,C.order_pay_status
        FROM to_order_history A
        LEFT JOIN to_paper B ON A.order_id = B.order_id
        LEFT JOIN to_order C ON A.order_id = C.id
        WHERE A.agent_open_id = #{openId}
        <if test="date == 'day'">
            AND B.create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        </if>
        <if test="date == 'week'">
            AND B.create_time >= DATE_SUB(NOW(), INTERVAL 1 WEEK)
        </if>
        <if test="date == 'year'">
            AND B.create_time >= DATE_SUB(NOW(), INTERVAL 1 YEAR)
        </if>

        ) AA
    </select>

</mapper>