package com.ruoyi.dispatch.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.dispatch.mapper.ChatConversationMembersMapper;
import com.ruoyi.dispatch.domain.ChatConversationMembers;
import com.ruoyi.dispatch.service.IChatConversationMembersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 会话成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class ChatConversationMembersServiceImpl extends ServiceImpl<ChatConversationMembersMapper,ChatConversationMembers> implements IChatConversationMembersService {

    @Autowired
    private ChatConversationMembersMapper chatConversationMembersMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ChatConversationMembers> queryWrapper(ChatConversationMembers chatConversationMembers) {
        LambdaQueryWrapper<ChatConversationMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(chatConversationMembers.getConversationId()), ChatConversationMembers::getConversationId, chatConversationMembers.getConversationId());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatConversationMembers.getUserOpenid()), ChatConversationMembers::getUserOpenid, chatConversationMembers.getUserOpenid());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatConversationMembers.getJoinTime()), ChatConversationMembers::getJoinTime, chatConversationMembers.getJoinTime());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatConversationMembers.getRole()), ChatConversationMembers::getRole, chatConversationMembers.getRole());
        return queryWrapper;
    }

    /**
     * 查询会话成员分页
     *
     * @param chatConversationMembers 会话成员
     * @return 会话成员
     */
    @Override
    public IPage<ChatConversationMembers> pages(ChatConversationMembers chatConversationMembers, IPage<ChatConversationMembers> page)
    {
        return chatConversationMembersMapper.selectPage(page, this.queryWrapper(chatConversationMembers));
    }

    /**
     * 查询会话成员列表
     * 
     * @param chatConversationMembers 会话成员
     * @return 会话成员
     */
    @Override
    public List<ChatConversationMembers> selectList(ChatConversationMembers chatConversationMembers)
    {
        return chatConversationMembersMapper.selectList(this.queryWrapper(chatConversationMembers));
    }

}
