package com.ruoyi.web.controller.tool;

import com.alibaba.druid.filter.config.ConfigTools;
import com.alibaba.druid.pool.DruidDataSource;

/**
 * druid 加解密工具
 *
 * @author: 天鸣
 * @date: 2024-06-18
 */
public class TestDruidTool {
    public static void main(String[] args) throws Exception {
        // 使用ConfigFilter加密密码
        String password = "Lema@2025";
        String[] arr = ConfigTools.genKeyPair(512);
        String publicKey = arr[1];
        String encryptedPassword=ConfigTools.encrypt(arr[0], password);
        System.out.println("privateKey:" + arr[0]);
        System.out.println("publicKey:" + arr[1]);
        System.out.println("password:" + ConfigTools.encrypt(arr[0], password));
        System.out.println("decryptPassword:" +  ConfigTools.decrypt(publicKey, encryptedPassword));
    }
}
