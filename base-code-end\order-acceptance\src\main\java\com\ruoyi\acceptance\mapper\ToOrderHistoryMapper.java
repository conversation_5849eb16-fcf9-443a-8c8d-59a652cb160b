package com.ruoyi.acceptance.mapper;
import com.ruoyi.acceptance.domain.ToOrderHistory;
import com.ruoyi.acceptance.dto.CommissionRankingDTO;
import com.ruoyi.acceptance.dto.DistributionReportDTO;
import com.ruoyi.acceptance.dto.InvitationRankingDTO;
import com.ruoyi.acceptance.dto.TransactionListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单流水Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Repository
public interface ToOrderHistoryMapper extends BaseMapper<ToOrderHistory> {
    BigDecimal select1(@Param("date") String date,@Param("openId")  String openId);

    DistributionReportDTO select2(@Param("date") String date,@Param("openId")  String openId);

    List<InvitationRankingDTO> invitationRanking();

    List<CommissionRankingDTO> commissionRanking();

    List<TransactionListDTO> transactionList(@Param("date")String date,@Param("openId")String openId);

    BigDecimal selectSumFrozenAmount(@Param("date")String date,@Param("openId")String openId);
}
