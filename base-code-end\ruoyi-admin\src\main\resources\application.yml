# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.7
  # 版权年份
  copyrightYear: 2023
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: info
    com.alibaba.dashscope.protocol.okhttp.OkHttpHttpClient: INFO

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  profiles:
    active: dev

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 请求源
  source: x-source
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
  # 数据源类型, 枚举
  sourceType:
    #微信请求
    wxApplet: wx
    #派单小程序
    dispatchApplet: dispatch


# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  # 关闭logo打印
  global-config:
    banner: false
    db-config:
      logic-delete-field: delFlag # 全局逻辑删除字段名
      logic-delete-value: 1 # 逻辑已删除值
      logic-not-delete-value: 0 # 逻辑未删除值


# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping:

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

#oss配置
oss:
  # API鉴权，访问身份验证中用到用户标识
  accessKeyId: LTAI5tDFZFTmxK5tAeS6Zuzv
  # 用户用于加密签名字符串和oss用来验证签名字符串的密钥
  accessKeySecret: ******************************
  # oss访问域名（内网）
  endpoint: oss-cn-beijing.aliyuncs.com
  # oss自定义域名
  dns: https://itclub.oss-cn-beijing.aliyuncs.com
  # oss的存储空间
  bucketName: itclub
  #文件路径 如：example 或者 example/img
  filePath: example
  #防止文件命名冲突 支持 uuid或timestamp两种方式，推荐uuid
  fileName: uuid
  # url有效期(S)
  policyExpire: 300
  # 上传文件大小(M)
  maxSize: 10
