package ${packageName}.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import ${packageName}.domain.${ClassName};
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * ${functionName}Service接口
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IService<${ClassName}> {

    /**
     * 分页查询${functionName}
     * @param ${className} ${functionName}
     * @param page 分页条件
     * @return ${functionName}集合
     */
    IPage<${ClassName}> pages(${ClassName} ${className}, IPage<${ClassName}> page);

    /**
     * 查询${functionName}列表
     * 
     * @param ${className} ${functionName}
     * @return ${functionName}集合
     */
     List<${ClassName}> selectList(${ClassName} ${className});

}
