package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.DoUserLogin;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.DoUserLoginMapper;
import com.ruoyi.system.service.IDoUserLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 派单小程序用户登录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Service
public class DoUserLoginServiceImpl implements IDoUserLoginService {
    @Autowired
    private DoUserLoginMapper doUserLoginMapper;
    @Override
    public void save(DoUserLogin doUserLogin) {
        doUserLogin.setCreateTime(new Date());
        doUserLoginMapper.save(doUserLogin);
    }

    @Override
    public void updateById(DoUserLogin doUserLogin) {
        doUserLoginMapper.updateById(doUserLogin);
    }

    @Override
    public DoUserLogin getById(String openid) {
        return doUserLoginMapper.getById(openid);
    }

    /**
     * 查询派单小程序用户登录
     * 
     * @param openid 派单小程序用户登录主键
     * @return 派单小程序用户登录
     */
    @Override
    public DoUserLogin selectDoUserLoginByOpenid(String openid)
    {
        return doUserLoginMapper.selectDoUserLoginByOpenid(openid);
    }

    /**
     * 查询派单小程序用户登录列表
     * 
     * @param doUserLogin 派单小程序用户登录
     * @return 派单小程序用户登录
     */
    @Override
    public List<DoUserLogin> selectDoUserLoginList(DoUserLogin doUserLogin)
    {
        return doUserLoginMapper.selectDoUserLoginList(doUserLogin);
    }

    /**
     * 新增派单小程序用户登录
     * 
     * @param doUserLogin 派单小程序用户登录
     * @return 结果
     */
    @Override
    public int insertDoUserLogin(DoUserLogin doUserLogin)
    {
        doUserLogin.setCreateTime(DateUtils.getNowDate());
        return doUserLoginMapper.insertDoUserLogin(doUserLogin);
    }

    /**
     * 修改派单小程序用户登录
     * 
     * @param doUserLogin 派单小程序用户登录
     * @return 结果
     */
    @Override
    public int updateDoUserLogin(DoUserLogin doUserLogin)
    {
        return doUserLoginMapper.updateDoUserLogin(doUserLogin);
    }

    /**
     * 批量删除派单小程序用户登录
     * 
     * @param openids 需要删除的派单小程序用户登录主键
     * @return 结果
     */
    @Override
    public int deleteDoUserLoginByOpenids(String[] openids)
    {
        return doUserLoginMapper.deleteDoUserLoginByOpenids(openids);
    }

    /**
     * 删除派单小程序用户登录信息
     * 
     * @param openid 派单小程序用户登录主键
     * @return 结果
     */
    @Override
    public int deleteDoUserLoginByOpenid(String openid)
    {
        return doUserLoginMapper.deleteDoUserLoginByOpenid(openid);
    }
}
