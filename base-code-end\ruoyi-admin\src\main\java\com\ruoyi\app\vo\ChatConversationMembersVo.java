package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 会话成员对象 ChatConversationMembersVo
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@ApiModel(value = " ChatConversationMembersVo", description = "会话成员对象VO")
public class ChatConversationMembersVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 会话ID */
    @Excel(name = "会话ID")
    @NotNull(message = "会话ID显示顺序不能为空")
    @ApiModelProperty(value = "会话ID", name="conversationId", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long conversationId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @NotNull(message = "用户ID显示顺序不能为空")
    @ApiModelProperty(value = "用户ID", name="userOpenid", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userOpenid;

    /** 加入时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "加入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "加入时间显示顺序不能为空")
    @ApiModelProperty(value = "加入时间", name="joinTime", required = true)
    private Date joinTime;

    /** 角色(0-普通成员,1-管理员,2-群主) */
    @Excel(name = "角色(0-普通成员,1-管理员,2-群主)")
    @ApiModelProperty(value = "角色(0-普通成员,1-管理员,2-群主)", name="role")
    private Integer role;

}
