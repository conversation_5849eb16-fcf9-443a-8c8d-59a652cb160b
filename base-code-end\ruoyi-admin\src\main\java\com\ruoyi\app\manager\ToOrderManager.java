package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.dto.ToOrderDTO;
import com.ruoyi.app.vo.convert.ToOrderDTOConvert;
import com.ruoyi.common.core.domain.convert.IPageConvert;

import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.acceptance.service.IToOrderService;
import com.ruoyi.acceptance.vo.ToOrderVo;
import com.ruoyi.app.vo.convert.ToOrderConvert;
import com.ruoyi.acceptance.domain.ToOrder;

import java.util.List;

/**
 * 接单订单Manager
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class ToOrderManager {

    @Autowired
    private IToOrderService toOrderService;

    @Autowired
    private ToOrderConvert toOrderConvert;
    @Autowired
    private ToOrderDTOConvert toOrderDTOConvert;

    /**
    * 分页查询接单订单
    */
    public IPage<ToOrderVo> page(ToOrderVo toOrderVo, Query query){
        IPage<ToOrder> page = toOrderService.pages(toOrderConvert.voToPo(toOrderVo), Condition.getPage(query));
        return (IPage<ToOrderVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToOrderVo>(), ToOrderConvert.INSTANCE);
    }

    public IPage<ToOrderVo> selectOrders(ToOrderVo toOrderVo, Query query){
        IPage<ToOrderDTO> page = toOrderService.selectOrders(toOrderVo, Condition.getPage(query));
        return (IPage<ToOrderVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToOrderVo>(), ToOrderDTOConvert.INSTANCE);
    }

    /**
    * 查询接单订单列表
    */
    public List<ToOrderVo> list(ToOrderVo toOrderVo) {
        List<ToOrder> toOrderList = toOrderService.selectList(toOrderConvert.voToPo(toOrderVo));
        return toOrderConvert.poToVoList(toOrderList);
    }

    /**
     * 查询接单订单详细信息
     */
    public ToOrderVo getInfo(Long id) {
        ToOrder toOrder = toOrderService.getById(id);
        return toOrderConvert.poToVo(toOrder);
    }

    /**
     * 查询接单订单详细信息
     */
    public ToOrderVo getInfoForPc(Long id) {
        ToOrderDTO toOrderDTO = toOrderService.getInfoForPc(id);
        return toOrderDTOConvert.poToVo(toOrderDTO);
    }

    /**
     * 新增接单订单
     */
    public boolean add(ToOrderVo toOrderVo) {
        return toOrderService.save(toOrderConvert.voToPo(toOrderVo));
    }

    /**
     * 修改接单订单
     */
    public boolean edit(ToOrderVo toOrderVo) {
        return toOrderService.updateById(toOrderConvert.voToPo(toOrderVo));
    }

    /**
     * 批量删除接单订单
     */
    public boolean remove(Long[] ids) {
        return toOrderService.removeByIds(CollUtil.toList(ids));
    }

}
