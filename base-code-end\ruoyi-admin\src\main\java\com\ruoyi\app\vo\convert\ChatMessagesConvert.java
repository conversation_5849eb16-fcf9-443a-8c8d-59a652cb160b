package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatMessagesVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.dispatch.domain.ChatMessages;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 消息PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ChatMessagesConvert extends BeanPoVoMapper<ChatMessages, ChatMessagesVo> {

        ChatMessagesConvert INSTANCE = Mappers.getMapper(ChatMessagesConvert.class);

}
