package com.ruoyi.app.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ChatMessageStatusVo;
import com.ruoyi.app.manager.ChatMessageStatusManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 消息状态Controller
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Api(tags = "消息状态管理")
@RestController
@RequestMapping("/dispatch/status")
public class ChatMessageStatusController {

    @Autowired
    private ChatMessageStatusManager chatMessageStatusManager;

    /**
     * 分页查询消息状态
     */
    @ApiOperation("分页查询消息状态")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('dispatch:status:list')")
    @GetMapping("/list")
    public R<IPage<ChatMessageStatusVo>> list(ChatMessageStatusVo chatMessageStatusVo, Query query)
    {
        return R.ok(chatMessageStatusManager.page(chatMessageStatusVo, query));
    }

    /**
    * 查询消息状态全部列表
    */
    @ApiOperation("查询消息状态全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('dispatch:status:list')")
    @GetMapping("/allList")
    public R<List<ChatMessageStatusVo>> allList(ChatMessageStatusVo chatMessageStatusVo){
        return R.ok(chatMessageStatusManager.list(chatMessageStatusVo));
    }

    /**
     * 导出消息状态列表
     */
    @ApiOperation("导出消息状态列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('dispatch:status:export')")
    @Log(title = "消息状态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChatMessageStatusVo chatMessageStatusVo, Query query)
    {
        List<ChatMessageStatusVo> list = chatMessageStatusManager.page(chatMessageStatusVo, query).getRecords();
        ExcelUtil<ChatMessageStatusVo> util = new ExcelUtil<ChatMessageStatusVo>(ChatMessageStatusVo.class);
        util.exportExcel(response, list, "消息状态数据");
    }

    /**
     * 获取消息状态详细信息
     */
    @ApiOperation("获取消息状态详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:status:query')")
    @GetMapping(value = "/{id}")
    public R<ChatMessageStatusVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(chatMessageStatusManager.getInfo(id));
    }

    /**
     * 新增消息状态
     */
    @ApiOperation("新增消息状态")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('dispatch:status:add')")
    @Log(title = "消息状态", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ChatMessageStatusVo chatMessageStatusVo)
    {
        return chatMessageStatusManager.add(chatMessageStatusVo) ? R.ok() : R.fail();
    }

    /**
     * 修改消息状态
     */
    @ApiOperation("修改消息状态")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('dispatch:status:edit')")
    @Log(title = "消息状态", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ChatMessageStatusVo chatMessageStatusVo)
    {
        return chatMessageStatusManager.edit(chatMessageStatusVo) ? R.ok() : R.fail();
    }

    /**
     * 删除消息状态
     */
    @ApiOperation("删除消息状态")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:status:remove')")
    @Log(title = "消息状态", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return chatMessageStatusManager.remove(ids) ? R.ok() : R.fail();
    }
}
