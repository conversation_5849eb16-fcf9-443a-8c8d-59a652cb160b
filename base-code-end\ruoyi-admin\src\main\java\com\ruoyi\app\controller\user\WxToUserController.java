package com.ruoyi.app.controller.user;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.vo.ToUserVO;
import com.ruoyi.common.core.domain.vo.TransactionUserVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.ToUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.acceptance.service.IToUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Api(tags = "微信用户信息")
@RestController
@RequestMapping("/wx/user")
public class WxToUserController extends BaseController
{
    @Autowired
    private IToUserService toUserService;

    /**
     * 查询用户信息列表
     */
    @ApiOperation("查询用户信息列表")
    @GetMapping("/list")
    public TableDataInfo list(ToUser toUser)
    {
        startPage();
        List<ToUser> list = toUserService.selectToUserList(toUser);
        return getDataTable(list);
    }

    /**
     * 邀请客户列表接口
     */
    @ApiOperation("邀请客户列表接口")
    @GetMapping("/inviteCustomerList")
    public R<List<ToUser>> inviteCustomerList()
    {
        List<ToUser> list = toUserService.inviteCustomerList();
        return R.ok(list);
    }

    @ApiOperation("交易客户列表")
    @GetMapping("/transactionCustomerList")
    public R<List<TransactionUserVO>>  transactionCustomerList()
    {
        List<TransactionUserVO> list = toUserService.transactionCustomerList();
        return R.ok(list);
    }

    /**
     * 导出用户信息列表
     */
    @ApiOperation("")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToUser toUser)
    {
        List<ToUser> list = toUserService.selectToUserList(toUser);
        ExcelUtil<ToUser> util = new ExcelUtil<ToUser>(ToUser.class);
        util.exportExcel(response, list, "用户信息数据");
    }

    /**
     * 获取用户信息详细信息
     */
    @ApiOperation("")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        logger.debug("111");
        return success(toUserService.selectToUserById(id));
    }

    @ApiOperation("通过openid获取用户信息")
    @GetMapping(value = "/getInfoByOpenId")
    public AjaxResult getInfoByOpenId()
    {
        ToUserVO toUserVO = toUserService.selectToUserByOpenId();
        if (StringUtils.isNotNull(toUserVO)){
            return success(toUserVO);
        }else {
            return error("该用户没有注册");
        }
    }

    /**
     * 新增用户信息
     */
    @ApiOperation("新增用户信息")
    @PostMapping
    public R<?> add(@RequestBody ToUser toUser)
    {
        return toUserService.insertToUserForWx(toUser);
    }

    /**
     * 修改用户信息
     */
    @ApiOperation("")
    @PutMapping
    public AjaxResult edit(@RequestBody ToUser toUser)
    {
        return toAjax(toUserService.updateToUser(toUser));
    }

    /**
     * 删除用户信息
     */
    @ApiOperation("")
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(toUserService.deleteToUserByIds(ids));
    }
}
