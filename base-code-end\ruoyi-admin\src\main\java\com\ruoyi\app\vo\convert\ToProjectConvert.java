package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ToProjectVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.acceptance.domain.ToProject;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 项目基本信息PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToProjectConvert extends BeanPoVoMapper<ToProject, ToProjectVo> {

        ToProjectConvert INSTANCE = Mappers.getMapper(ToProjectConvert.class);

}
