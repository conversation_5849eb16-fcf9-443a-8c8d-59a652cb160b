package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ToGroupBuyingVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.acceptance.domain.ToGroupBuying;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 团购信息PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-08-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToGroupBuyingConvert extends BeanPoVoMapper<ToGroupBuying, ToGroupBuyingVo> {

        ToGroupBuyingConvert INSTANCE = Mappers.getMapper(ToGroupBuyingConvert.class);

}
