<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToOrderMapper">

    <select id="selectOrders" resultType="com.ruoyi.acceptance.dto.ToOrderDTO">
        SELECT A.*,B.title FROM to_order A
        LEFT JOIN to_paper B ON A.id = B.order_id
        where 1 = 1
        <if test="toOrderVo.title != null and toOrderVo.title != ''">
            AND B.title LIKE CONCAT('%', #{toOrderVo.title}, '%')
        </if>
        <if test="toOrderVo.id != null and toOrderVo.id != ''">
            AND A.id = #{toOrderVo.id}
        </if>
    </select>

    <select id="getInfoForPc" resultType="com.ruoyi.acceptance.dto.ToOrderDTO">
        SELECT A.*,B.title FROM to_order A
        LEFT JOIN to_paper B ON A.id = B.order_id
        where A.id = #{id}
    </select>

</mapper>