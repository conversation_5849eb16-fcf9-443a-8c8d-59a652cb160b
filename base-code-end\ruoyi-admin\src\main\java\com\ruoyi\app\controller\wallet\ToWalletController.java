package com.ruoyi.app.controller.wallet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.app.manager.ToWalletManager;
import com.ruoyi.app.vo.ToWalletVo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包Controller
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
@Api(tags = "钱包管理")
@RestController
@RequestMapping("/wx/acceptance/wallet")
public class ToWalletController {

    @Autowired
    private ToWalletManager toWalletManager;

    /**
     * 分页查询钱包
     */
    @ApiOperation("分页查询钱包")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public R<IPage<ToWalletVo>> list(ToWalletVo toWalletVo, Query query)
    {
        return R.ok(toWalletManager.page(toWalletVo, query));
    }

    /**
    * 查询钱包全部列表
    */
    @ApiOperation("查询钱包全部列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/allList")
    public R<List<ToWalletVo>> allList(ToWalletVo toWalletVo){
        return R.ok(toWalletManager.list(toWalletVo));
    }

    /**
     * 导出钱包列表
     */
    @ApiOperation("导出钱包列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToWalletVo toWalletVo, Query query)
    {
        List<ToWalletVo> list = toWalletManager.page(toWalletVo, query).getRecords();
        ExcelUtil<ToWalletVo> util = new ExcelUtil<ToWalletVo>(ToWalletVo.class);
        util.exportExcel(response, list, "钱包数据");
    }

    /**
     * 获取钱包详细信息
     */
    @ApiOperation("获取钱包详细")
    @ApiOperationSupport(order = 3)
    @GetMapping(value = "/{id}")
    public R<ToWalletVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toWalletManager.getInfo(id));
    }

    @ApiOperation("通过openid获取钱包明细")
    @GetMapping(value = "/getInfoByOpenId")
    public R<ToWalletVo> getInfoByOpenId()
    {
        return R.ok(toWalletManager.getInfoByOpenId());
    }

    /**
     * 新增钱包
     */
    @ApiOperation("新增钱包")
    @ApiOperationSupport(order = 4)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToWalletVo toWalletVo)
    {
        return toWalletManager.add(toWalletVo) ? R.ok() : R.fail();
    }

    /**
     * 修改钱包
     */
    @ApiOperation("修改钱包")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToWalletVo toWalletVo)
    {
        return toWalletManager.edit(toWalletVo) ? R.ok() : R.fail();
    }

    /**
     * 删除钱包
     */
    @ApiOperation("删除钱包")
    @ApiOperationSupport(order = 6)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toWalletManager.remove(ids) ? R.ok() : R.fail();
    }

    /**
     * 修改钱包
     */
    @ApiOperation("钱包提现")
    @GetMapping("/withdrawal/{withdrawalAmount}")
    public R<?> withdrawal(@PathVariable("withdrawalAmount") BigDecimal withdrawalAmount)
    {
        return toWalletManager.withdrawal(withdrawalAmount);
    }


}
