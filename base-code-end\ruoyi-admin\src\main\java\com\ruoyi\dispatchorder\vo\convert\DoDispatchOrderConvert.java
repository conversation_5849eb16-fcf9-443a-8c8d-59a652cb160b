package com.ruoyi.dispatchorder.vo.convert;

import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.dispatch.domain.DoDispatchOrder;
import com.ruoyi.dispatchorder.vo.DoDispatchOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 派单PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-08-27
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DoDispatchOrderConvert extends BeanPoVoMapper<DoDispatchOrder, DoDispatchOrderVo> {

        DoDispatchOrderConvert INSTANCE = Mappers.getMapper(DoDispatchOrderConvert.class);

}
