package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ToProjectCategoryVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.acceptance.domain.ToProjectCategory;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 项目类目信息PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToProjectCategoryConvert extends BeanPoVoMapper<ToProjectCategory, ToProjectCategoryVo> {

        ToProjectCategoryConvert INSTANCE = Mappers.getMapper(ToProjectCategoryConvert.class);

}
