<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧表单区域 -->
      <el-col :span="8">
        <el-card class="form-card">
          <div slot="header" class="clearfix">
            <span class="card-title">AI需求生成助手</span>
          </div>

          <el-form
            :model="form"
            :rules="rules"
            ref="form"
            label-width="100px"
            class="form-content"
          >
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="form.projectName"
                placeholder="请输入项目名称"
                size="medium"
              />
            </el-form-item>

            <el-form-item label="项目类目" prop="categoryCode">
              <treeselect
                v-model="form.categoryCode"
                :options="categoryOptions"
                :normalizer="normalizer"
                :show-count="true"
                placeholder="请选择项目类目"
                class="treeselect-custom"
              />
            </el-form-item>

            <el-form-item class="button-group">
              <el-button
                v-if="!generating"
                type="primary"
                size="medium"
                @click="generateRequirements"
                :disabled="!form.projectName || !form.categoryCode"
                icon="el-icon-magic-stick"
              >
                生成需求文档
              </el-button>
              <el-button
                v-else
                type="warning"
                size="medium"
                @click="cancelGeneration"
                icon="el-icon-close"
              >
                取消生成
              </el-button>
              <el-button
                size="medium"
                @click="clearResult"
                icon="el-icon-delete"
                :disabled="generating"
              >
                清空结果
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧结果区域 -->
      <el-col :span="16">
        <el-card class="result-card">
          <div slot="header" class="clearfix">
            <span class="card-title">生成结果 (文档格式)</span>
            <div style="float: right">
              <el-button
                style="padding: 3px 0; margin-right: 10px"
                type="text"
                @click="toggleViewMode"
                :icon="
                  viewMode === 'markdown' ? 'el-icon-document' : 'el-icon-view'
                "
              >
                {{ viewMode === "markdown" ? "纯文本" : "文档" }}
              </el-button>
              <el-button
                style="padding: 3px 0; margin-right: 10px"
                type="text"
                @click="exportMarkdown"
                icon="el-icon-download"
              >
                导出MD
              </el-button>
              <el-button
                style="padding: 3px 0"
                type="text"
                @click="copyResult"
                icon="el-icon-document-copy"
              >
                复制内容
              </el-button>
            </div>
          </div>

          <div class="result-container">
            <div v-if="generating && !result" class="generating-indicator">
              <i class="el-icon-loading"></i> 正在生成中，请稍候...
            </div>
            <div v-else-if="!result" class="empty-result">
              <i class="el-icon-document"></i>
              <p>请填写项目信息并选择类目，然后点击生成按钮</p>
            </div>
            <div
              v-else
              class="result-content"
              v-html="formattedResult"
              ref="resultContent"
              :key="resultKey"
            ></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  generateProjectRequirements,
  generateProjectRequirementsSync,
} from "@/api/ai/agent";
import { getCategoryTree } from "@/api/common/category";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import Vue from "vue";

export default {
  name: "AgentTool",
  components: { Treeselect },
  data() {
    return {
      form: {
        projectName: "",
        categoryCode: null,
      },
      rules: {
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        categoryCode: [
          { required: true, message: "请选择项目类目", trigger: "change" },
        ],
      },
      categoryOptions: [],
      generating: false,
      result: "",
      resultKey: 0, // 用于强制重新渲染
      viewMode: "markdown", // 默认使用markdown模式
      abortController: null,
    };
  },
  computed: {
    formattedResult() {
      if (!this.result) return "";

      if (this.viewMode === "text") {
        // 纯文本模式
        return this.result.replace(/\n/g, "<br>");
      } else {
        // Markdown模式 - 使用简单解析
        try {
          console.log("开始解析Markdown，内容长度:", this.result.length);
          console.log("原始内容前100字符:", this.result.substring(0, 100));
          
          // 按行分割处理
          const lines = this.result.split('\n');
          const processedLines = [];
          let inCodeBlock = false;
          let inList = false;
          
          for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 跳过空行
            if (!line) {
              if (inList) {
                processedLines.push('</ul>');
                inList = false;
              }
              processedLines.push('<br>');
              continue;
            }
            
            // 处理代码块
            if (line.startsWith('```')) {
              if (inCodeBlock) {
                processedLines.push('</code></pre>');
                inCodeBlock = false;
              } else {
                processedLines.push('<pre><code>');
                inCodeBlock = true;
              }
              continue;
            }
            
            if (inCodeBlock) {
              processedLines.push(line + '\n');
              continue;
            }
            
            // 处理标题
            if (line.startsWith('### ')) {
              console.log("处理三级标题:", line);
              processedLines.push(`<h3>${line.substring(4)}</h3>`);
              continue;
            }
            if (line.startsWith('## ')) {
              console.log("处理二级标题:", line);
              processedLines.push(`<h2>${line.substring(3)}</h2>`);
              continue;
            }
            if (line.startsWith('# ')) {
              console.log("处理一级标题:", line);
              processedLines.push(`<h1>${line.substring(2)}</h1>`);
              continue;
            }
            
            // 处理列表
            if (line.match(/^[\*\-]\s/) || line.match(/^\d+\.\s/)) {
              if (!inList) {
                processedLines.push('<ul>');
                inList = true;
              }
              const listItem = line.replace(/^[\*\-]\s/, '').replace(/^\d+\.\s/, '');
              processedLines.push(`<li>${this.processInlineFormatting(listItem)}</li>`);
              continue;
            }
            
            // 结束列表
            if (inList) {
              processedLines.push('</ul>');
              inList = false;
            }
            
            // 处理普通段落
            processedLines.push(`<p>${this.processInlineFormatting(line)}</p>`);
          }
          
          // 确保列表被正确关闭
          if (inList) {
            processedLines.push('</ul>');
          }
          
          const html = processedLines.join('');
          console.log("Markdown解析成功，HTML长度:", html.length);
          console.log("生成的HTML:", html.substring(0, 500) + "...");
          return html;
        } catch (error) {
          console.error("Markdown解析失败:", error);
          console.log("回退到简单格式化");
          // 如果解析失败，回退到简单格式化
          return this.result.replace(/\n/g, "<br>");
        }
      }
    },
  },
  created() {
    this.getCategoryTree();
  },
  methods: {
    /** 转换类目数据结构 */
    normalizer(node) {
      return {
        id: node.code,
        label: node.name,
        children: node.children,
      };
    },

    /** 获取类目树 */
    getCategoryTree() {
      getCategoryTree().then((response) => {
        this.categoryOptions = this.processCategoryTree(response.data);
      });
    },

    /** 处理类目树结构，只保留两层 */
    processCategoryTree(data) {
      if (!Array.isArray(data)) {
        return [];
      }

      return data.map((item) => {
        const processedItem = {
          code: item.code,
          name: item.name,
        };

        if (
          item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
        ) {
          processedItem.children = item.children.map((child) => ({
            code: child.code,
            name: child.name,
            isLeaf: true,
          }));
        }

        return processedItem;
      });
    },

    /** 生成需求文档 */
    async generateRequirements() {
      try {
        console.log("开始生成需求文档...");
        this.generating = true;
        this.result = "";
        console.log("生成状态已设置为true，结果已清空");

        // 创建AbortController用于取消请求
        this.abortController = new AbortController();

        // 使用流式接口
        const response = await generateProjectRequirements(
          this.form.projectName,
          this.form.categoryCode,
          this.abortController.signal
        );

        // 检查响应状态
        if (!response.ok) {
          console.log("响应状态错误:", response.status);
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        console.log("响应状态正常，开始处理流式数据");

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = "";

        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              break;
            }

            // 解码并处理数据
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // 处理完整的数据行
            const lines = buffer.split("\n");
            buffer = lines.pop() || ""; // 保留不完整的行

            console.log("处理数据块，行数:", lines.length);

            for (const line of lines) {
              console.log("处理行:", line);

              // 处理多种可能的data前缀格式
              let content = null;
              if (line.startsWith("data:")) {
                content = line.substring(5).trim();
              }
              if (content) {
                console.log("收到内容:", content);
                // 直接追加内容
                this.result += content;
                console.log("当前结果:", this.result);
                // 强制更新视图
                this.$forceUpdate();

                // 立即更新视图
                this.$nextTick(() => {
                  // 强制重新渲染结果区域
                  this.resultKey += 1;
                  // 确保滚动到底部
                  this.scrollToBottom();
                });
              }
            }
          }

          this.$message.success("需求生成完成");
        } finally {
          reader.releaseLock();
        }
      } catch (error) {
        console.error("生成需求失败:", error);
        if (error.name === "AbortError") {
          console.log("请求被取消");
          this.$message.warning("请求已取消");
        } else {
          console.log("其他错误:", error.message);
          this.$message.error("生成需求失败，请重试");
        }
      } finally {
        this.generating = false;
        this.abortController = null;
      }
    },

    /** 清空结果 */
    clearResult() {
      this.result = "";
    },

    /** 复制结果 */
    copyResult() {
      if (!this.result) {
        this.$message.warning("没有可复制的内容");
        return;
      }

      // 创建临时文本区域
      const textArea = document.createElement("textarea");
      textArea.value = this.result;
      document.body.appendChild(textArea);
      textArea.select();

      try {
        document.execCommand("copy");
        this.$message.success("内容已复制到剪贴板");
      } catch (err) {
        this.$message.error("复制失败");
      } finally {
        document.body.removeChild(textArea);
      }
    },

    /** 导出Markdown文件 */
    exportMarkdown() {
      if (!this.result) {
        this.$message.warning("没有可导出的内容");
        return;
      }

      // 创建文件名
      const fileName = `${this.form.projectName || "项目需求"}_需求文档.md`;

      // 创建Blob对象
      const blob = new Blob([this.result], {
        type: "text/markdown;charset=utf-8",
      });

      // 创建下载链接
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 释放URL对象
      URL.revokeObjectURL(link.href);

      this.$message.success("Markdown文件已导出");
    },

    /** 取消生成 */
    cancelGeneration() {
      if (this.abortController) {
        this.abortController.abort();
        this.$message.info("生成已取消");
      }
    },

    /** 处理内联格式化（粗体、斜体、代码） */
    processInlineFormatting(text) {
      return text
        // 粗体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 斜体
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // 行内代码
        .replace(/`([^`]+)`/g, '<code>$1</code>');
    },

    /** 切换显示模式 */
    toggleViewMode() {
      this.viewMode = this.viewMode === "markdown" ? "text" : "markdown";
      this.$message.success(
        `已切换到${this.viewMode === "markdown" ? "文档" : "纯文本"}模式`
      );
    },

    /** 自动滚动到底部 */
    scrollToBottom() {
      this.$nextTick(() => {
        const resultContent = this.$refs.resultContent;
        if (resultContent) {
          resultContent.scrollTop = resultContent.scrollHeight;
        }
      });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  height: calc(100vh - 84px);
  overflow: hidden;
}

.form-card {
  height: calc(100vh - 200px);
}

.result-card {
  height: calc(100vh - 200px);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-content {
  padding: 20px 0;
}

.button-group {
  margin-top: 30px;
  text-align: center;
}

.button-group .el-button {
  margin: 0 10px;
  min-width: 120px;
}

.treeselect-custom {
  width: 100%;
}

.result-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.result-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  line-height: 1.8;
  font-size: 14px;
  color: #333;
  border: 1px solid #e4e7ed;
}

/* Markdown样式 - 使用深度选择器确保样式生效 */
.result-content >>> h1,
.result-content >>> h2,
.result-content >>> h3,
.result-content >>> h4,
.result-content >>> h5,
.result-content >>> h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.4;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial,
    sans-serif;
}

.result-content h1,
.result-content h2,
.result-content h3,
.result-content h4,
.result-content h5,
.result-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  color: #1a202c;
  line-height: 1.4;
}

.result-content >>> h1 {
  font-size: 28px;
  border-bottom: 3px solid #409eff;
  padding-bottom: 12px;
  margin-top: 0;
}

.result-content >>> h2 {
  font-size: 24px;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 10px;
}

.result-content >>> h3 {
  font-size: 20px;
  color: #2d3748;
}

.result-content >>> h4 {
  font-size: 18px;
  color: #4a5568;
}

.result-content >>> h5 {
  font-size: 16px;
  color: #718096;
}

.result-content >>> h6 {
  font-size: 14px;
  color: #a0aec0;
}

.result-content >>> p {
  margin-bottom: 16px;
  line-height: 1.8;
  text-align: justify;
  color: #2c3e50;
}

.result-content >>> ul,
.result-content >>> ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.result-content >>> li {
  margin-bottom: 8px;
  line-height: 1.7;
  color: #2c3e50;
}

.result-content >>> li > ul,
.result-content >>> li > ol {
  margin-top: 8px;
  margin-bottom: 8px;
}

.result-content >>> code {
  background-color: #f7fafc;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  font-size: 13px;
  color: #e53e3e;
  border: 1px solid #e2e8f0;
}

.result-content >>> pre {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  overflow-x: auto;
  margin: 20px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.result-content >>> pre code {
  background-color: transparent;
  padding: 0;
  color: #2d3748;
  border: none;
  font-size: 14px;
  line-height: 1.6;
}

.result-content >>> blockquote {
  border-left: 4px solid #409eff;
  margin: 20px 0;
  padding: 16px 20px;
  background-color: #f7fafc;
  border-radius: 0 6px 6px 0;
  color: #4a5568;
  font-style: italic;
}

.result-content >>> blockquote p {
  margin: 0;
}

.result-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.result-content th,
.result-content td {
  border: 1px solid #e2e8f0;
  padding: 12px 16px;
  text-align: left;
  vertical-align: top;
}

.result-content th {
  background-color: #f7fafc;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 2px solid #e2e8f0;
}

.result-content tr:nth-child(even) {
  background-color: #fafbfc;
}

.result-content tr:hover {
  background-color: #f0f4f8;
}

.result-content strong {
  font-weight: 600;
  color: #1a202c;
}

.result-content em {
  font-style: italic;
  color: #4a5568;
}

.result-content hr {
  border: none;
  border-top: 2px solid #e2e8f0;
  margin: 32px 0;
}

.result-content a {
  color: #409eff;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s;
}

.result-content a:hover {
  border-bottom-color: #409eff;
}

/* 特殊样式 */
.result-content .highlight {
  background-color: #fff3cd;
  padding: 2px 4px;
  border-radius: 3px;
}

.result-content .note {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 16px 0;
  color: #0c5460;
}

.result-content .warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 16px 0;
  color: #856404;
}

.generating-indicator {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.generating-indicator i {
  margin-right: 8px;
  font-size: 20px;
}

.empty-result {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.empty-result i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-result p {
  font-size: 14px;
  margin: 0;
}

/* 自定义滚动条 */
.result-content::-webkit-scrollbar {
  width: 6px;
}

.result-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.result-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.result-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 