package com.ruoyi.common.config;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "wx")
public class WxPayConfig implements InitializingBean {
    //微信小程序appid
    private String appId;
    //商户号
    private String mchId;
    // 证书序列号
    private String mchSerialNo;
    //api密钥
    private String apiKey;

    private String partnerKey;
    //微信地址
    private String domain;
    //回调接口地址
    private String notifyUrl;
    //证书密钥地址
    private String apiClientKeyPath;
    //商户证书地址
    private String apiClientCertPath;
    //平台证书地址
    private String platformCertPath;


    public static String appIdOut;
    public static String mchIdOut;
    public static String mchSerialNoOut;
    public static String apiKeyOut;
    public static String partnerKeyOut;
    public static String domainOut;
    public static String notifyUrlOut;
    public static String apiClientKeyPathOut;
    public static String apiClientCertPathOut;
    public static String platformCertPathOut;

    @Override
    public void afterPropertiesSet() {
        appIdOut = appId;
        mchIdOut = mchId;
        mchSerialNoOut = mchSerialNo;
        apiKeyOut = apiKey;
        partnerKeyOut = partnerKey;
        domainOut = domain;
        notifyUrlOut = notifyUrl;
        apiClientKeyPathOut = apiClientKeyPath;
        apiClientCertPathOut = apiClientCertPath;
        platformCertPathOut = platformCertPath;
    }
}
