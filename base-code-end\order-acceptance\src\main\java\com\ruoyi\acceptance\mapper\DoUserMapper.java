package com.ruoyi.acceptance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.entity.DoUser;
import com.ruoyi.common.core.domain.vo.DoUserVO;

import java.util.List;

/**
 * 派单用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface DoUserMapper extends BaseMapper<DoUser> {

    public DoUser selectToUserByOpenId1(String openId);
    /**
     * 查询派单用户
     * 
     * @param id 派单用户主键
     * @return 派单用户
     */
    public DoUser selectDoUserById(Long id);

    /**
     * 查询派单用户列表
     * 
     * @param doUser 派单用户
     * @return 派单用户集合
     */
    public List<DoUser> selectDoUserList(DoUser doUser);

    /**
     * 新增派单用户
     * 
     * @param doUser 派单用户
     * @return 结果
     */
    public int insertDoUser(DoUser doUser);

    /**
     * 修改派单用户
     * 
     * @param doUser 派单用户
     * @return 结果
     */
    public int updateDoUser(DoUser doUser);

    /**
     * 删除派单用户
     * 
     * @param id 派单用户主键
     * @return 结果
     */
    public int deleteDoUserById(Long id);

    /**
     * 批量删除派单用户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDoUserByIds(Long[] ids);

    public DoUserVO selectToUserByOpenId(String openId);
}
