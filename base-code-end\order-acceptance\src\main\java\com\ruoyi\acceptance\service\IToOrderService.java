package com.ruoyi.acceptance.service;

import java.util.Arrays;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.domain.ToOrder;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.dto.ToOrderDTO;
import com.ruoyi.acceptance.vo.ToOrderVo;

/**
 * 接单订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface IToOrderService extends IService<ToOrder> {

    /**
     * 分页查询接单订单
     * @param toOrder 接单订单
     * @param page 分页条件
     * @return 接单订单集合
     */
    IPage<ToOrder> pages(ToOrder toOrder, IPage<ToOrder> page);

    /**
     * 查询接单订单列表
     * 
     * @param toOrder 接单订单
     * @return 接单订单集合
     */
     List<ToOrder> selectList(ToOrder toOrder);

    ToOrder selectOneById(Long id);

    ToOrder selectOneByOutTradeNo(String outTradeNo);

    IPage<ToOrderDTO> selectOrders(ToOrderVo toOrderVo, IPage<ToOrderDTO> page);

    ToOrderDTO getInfoForPc(Long id);

    boolean addWxUserToOrder(ToOrder toOrder);

    ToOrder selectOneByProjectId(Long projectId);

    List<ToOrder> selectByProjectIds(List<Long> projectIds);
}
