package com.ruoyi.acceptance.service.impl;

import java.util.Collections;
import java.util.List;
import cn.hutool.core.util.ObjUtil;
import com.ruoyi.acceptance.dto.ToOrderSubDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.mapper.ToOrderSubMapper;
import com.ruoyi.acceptance.domain.ToOrderSub;
import com.ruoyi.acceptance.service.IToOrderSubService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 接单订单子订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Service
public class ToOrderSubServiceImpl extends ServiceImpl<ToOrderSubMapper,ToOrderSub> implements IToOrderSubService {

    @Autowired
    private ToOrderSubMapper toOrderSubMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToOrderSub> queryWrapper(ToOrderSub toOrderSub) {
        LambdaQueryWrapper<ToOrderSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderSub.getTitle()), ToOrderSub::getTitle, toOrderSub.getTitle());
        return queryWrapper;
    }

    /**
     * 查询接单订单子订单分页
     *
     * @param toOrderSub 接单订单子订单
     * @return 接单订单子订单
     */
    @Override
    public IPage<ToOrderSub> pages(ToOrderSub toOrderSub, IPage<ToOrderSub> page)
    {
        return toOrderSubMapper.selectPage(page, this.queryWrapper(toOrderSub));
    }

    /**
     * 查询接单订单子订单列表
     * 
     * @param toOrderSub 接单订单子订单
     * @return 接单订单子订单
     */
    @Override
    public List<ToOrderSub> selectList(ToOrderSub toOrderSub)
    {
        return toOrderSubMapper.selectList(this.queryWrapper(toOrderSub));
    }

    @Override
    public List<ToOrderSubDTO> selectListByOpenId(String openid) {
        return toOrderSubMapper.selectListByOpenID(openid);
    }

}
