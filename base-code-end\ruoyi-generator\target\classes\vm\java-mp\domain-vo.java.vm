package com.ruoyi.app.vo;

#foreach ($import in $importList)
import ${import};
#end
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
#if($table.crud || $table.sub)
#elseif($table.tree)
import com.ruoyi.common.core.domain.TreeEntity;
#end

/**
 * ${functionName}对象 ${ClassName}Vo
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="BaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@Data
@ApiModel(value = " ${ClassName}Vo", description = "${functionName}对象VO")
public class ${ClassName}Vo implements Serializable {
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date')
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
#else
    @Excel(name = "${comment}")
#end
#if($column.isRequired == "1")
#if($column.javaType == "String")
    @NotBlank(message="${comment}不能为空")
#else
    @NotNull(message = "${comment}显示顺序不能为空")
#end
    @ApiModelProperty(value = "${comment}", name="$column.javaField", required = true)
#else
    @ApiModelProperty(value = "${comment}", name="$column.javaField")
#end
#end
#if($column.javaType == 'Long')
    @JsonFormat(shape = JsonFormat.Shape.STRING)
#end
    private $column.javaType $column.javaField;

#end
#end
#if($table.sub)
    /** $table.subTable.functionName信息 */
    private List<${subClassName}> ${subclassName}List;

#end
}
