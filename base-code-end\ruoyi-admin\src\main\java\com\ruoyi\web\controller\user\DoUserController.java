package com.ruoyi.web.controller.user;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.DoUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.acceptance.service.IDoUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 派单用户Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/dispatch/user")
public class DoUserController extends BaseController {
    @Autowired
    private IDoUserService doUserService;

    /**
     * 查询派单用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(DoUser doUser)
    {
        startPage();
        List<DoUser> list = doUserService.selectDoUserList(doUser);
        return getDataTable(list);
    }

    /**
     * 导出派单用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @Log(title = "派单用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DoUser doUser)
    {
        List<DoUser> list = doUserService.selectDoUserList(doUser);
        ExcelUtil<DoUser> util = new ExcelUtil<DoUser>(DoUser.class);
        util.exportExcel(response, list, "派单用户数据");
    }

    /**
     * 获取派单用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(doUserService.selectDoUserById(id));
    }

    /**
     * 新增派单用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "派单用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DoUser doUser)
    {
        return toAjax(doUserService.insertDoUser(doUser));
    }

    /**
     * 修改派单用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "派单用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DoUser doUser)
    {
        return toAjax(doUserService.updateDoUser(doUser));
    }

    /**
     * 删除派单用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "派单用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(doUserService.deleteDoUserByIds(ids));
    }
}
