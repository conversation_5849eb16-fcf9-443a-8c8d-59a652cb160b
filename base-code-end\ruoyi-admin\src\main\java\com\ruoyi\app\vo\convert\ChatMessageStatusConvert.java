package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatMessageStatusVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.dispatch.domain.ChatMessageStatus;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 消息状态PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ChatMessageStatusConvert extends BeanPoVoMapper<ChatMessageStatus, ChatMessageStatusVo> {

        ChatMessageStatusConvert INSTANCE = Mappers.getMapper(ChatMessageStatusConvert.class);

}
