<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToUserMapper">

    <resultMap type="com.ruoyi.common.core.domain.entity.ToUser" id="ToUserResult">
        <result property="id"    column="id"    />
        <result property="nickname"    column="nickname"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="wechatId"    column="wechat_id"    />
        <result property="openId"    column="open_id"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="headSculpture"    column="head_sculpture"    />
        <result property="shareCode"    column="share_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_date"    />
        <result property="updateBy"    column="update_time"    />
    </resultMap>

    <sql id="selectToUserVo">
        select id, nickname, phone_number, wechat_id, open_id, head_sculpture,invite_code, share_code,whether_agent,create_date, create_by, update_date, update_by from to_user
    </sql>

    <select id="selectToUserList" parameterType="com.ruoyi.common.core.domain.entity.ToUser" resultMap="ToUserResult">
        <include refid="selectToUserVo"/>
        <where>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number = #{phoneNumber}</if>
            <if test="inviteCode != null  and inviteCode != ''"> and invite_code like concat('%', #{inviteCode}, '%')</if>
            <if test="shareCode != null  and shareCode != ''"> and share_code like concat('%', #{shareCode}, '%')</if>
            <if test="whetherAgent != null  and whetherAgent != ''"> and whether_agent = #{whetherAgent} </if>
        </where>
    </select>

    <select id="selectToUserById" parameterType="Long" resultMap="ToUserResult">
        <include refid="selectToUserVo"/>
        where id = #{id}
    </select>

    <resultMap type="com.ruoyi.common.core.domain.vo.ToUserVO" id="selectToUserByOpenIdResult">
        <result property="id"    column="id"    />
        <result property="nickname"    column="nickname"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="wechatId"    column="wechat_id"    />
        <result property="openId"    column="open_id"    />
        <result property="inviteCode"    column="invite_code"    />
        <result property="headSculpture"    column="head_sculpture"    />
        <result property="shareCode"    column="share_code"    />
        <result property="createDate"    column="create_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="roleKey"    column="role_key"    />
    </resultMap>
    <select id="selectToUserByOpenId" parameterType="String" resultMap="selectToUserByOpenIdResult">
        select tu.id, tu.nickname, tu.phone_number, tu.wechat_id, tu.open_id, tu.head_sculpture,tu.invite_code, tu.share_code,tu.create_time, tu.create_by, tu.update_time, tu.update_by,sr.role_key  from to_user tu
        left join sys_user_role sur on tu.id = sur.user_id
        left join sys_role sr on sur.role_id = sr.role_id
        where tu.open_id = #{openId}
    </select>

    <select id="selectToUserByOpenId1" parameterType="String" resultMap="ToUserResult">
        <include refid="selectToUserVo"/>
        where open_id = #{openId}
    </select>

    <select id="inviteCustomerList" parameterType="String" resultMap="ToUserResult">
        select
        id,
        nickname,
        phone_number,
        wechat_id,
        open_id,
        head_sculpture,
        invite_code,
        share_code,
        create_time,
        create_by,
        update_date,
        update_by
        from
        to_user
        where invite_code = ( select share_code from to_user where open_id = #{openId})
    </select>

    <resultMap type="com.ruoyi.common.core.domain.vo.TransactionUserVO" id="transactionCustomerList">
        <result property="nickname"    column="nickname"    />
        <result property="headSculpture"    column="head_sculpture"    />
        <result property="orderPayStatus"    column="order_pay_status"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <select id="transactionCustomerList" parameterType="String" resultMap="transactionCustomerList">
        select
        tu.nickname  ,
        tu.head_sculpture ,
        to2.order_pay_status  ,
        to2.update_time
        from
        to_user tu
        left join to_order to2 on tu.open_id = to2.custom
        where tu.invite_code  = (select tu2.share_code  from to_user tu2 where tu2.open_id = #{openId})
    </select>

    <select id="getUserList" parameterType="String" resultMap="ToUserResult">
        <include refid="selectToUserVo"/>
        <where>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number like concat('%', #{phoneNumber}, '%')</if>
        </where>
    </select>
    <select id="getHeadsForGroupInfo" resultType="java.lang.String" parameterType="String">
        select tu.head_sculpture  from to_user tu
        where open_id in (
        select open_id
        from to_group_buying tgb
        where group_id = #{groupId}
        )
    </select>

    <insert id="insertToUser" parameterType="com.ruoyi.common.core.domain.entity.ToUser" useGeneratedKeys="true" keyProperty="id">
        insert into to_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="wechatId != null and wechatId != ''">wechat_id,</if>
            <if test="openId != null and openId != ''">open_id,</if>
            <if test="inviteCode != null and inviteCode != ''">invite_code,</if>
            <if test="headSculpture != null and headSculpture != ''">head_sculpture,</if>
            <if test="createDate != null and createDate != ''">create_date,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateDate != null and updateDate != ''">update_date,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="wechatId != null and wechatId != ''">#{wechatId},</if>
            <if test="openId != null and openId != ''">#{openId},</if>
            <if test="inviteCode != null and inviteCode != ''">#{inviteCode},</if>
            <if test="headSculpture != null and headSculpture != ''">#{headSculpture},</if>
            <if test="createDate != null and createDate != ''">#{createDate},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateDate != null and updateDate != ''">#{updateDate},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateToUser" parameterType="com.ruoyi.common.core.domain.entity.ToUser">
        update to_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="wechatId != null and wechatId != ''">wechat_id = #{wechatId},</if>
            <if test="inviteCode != null and inviteCode != ''">invite_code = #{inviteCode},</if>
            <if test="createDate != null and createDate != ''">create_date = #{createDate},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateDate != null and updateDate != ''">update_date = #{updateDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateToUserByOpenId" parameterType="com.ruoyi.common.core.domain.entity.ToUser">
        update to_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickname != null and nickname != ''">nickname = #{nickname},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="wechatId != null and wechatId != ''">wechat_id = #{wechatId},</if>
            <if test="inviteCode != null and inviteCode != ''">invite_code = #{inviteCode},</if>
            <if test="headSculpture != null and headSculpture != ''">head_sculpture = #{headSculpture},</if>
            <if test="createDate != null and createDate != ''">create_date = #{createDate},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateDate != null and updateDate != ''">update_date = #{updateDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where open_id = #{openId}
    </update>

    <delete id="deleteToUserById" parameterType="Integer">
        delete from to_user where id = #{id}
    </delete>

    <delete id="deleteToUserByIds" parameterType="String">
        delete from to_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getInviteUserCountByOpenId" parameterType="String" resultType="java.math.BigDecimal">
        SELECT
            count(*)
        FROM
            to_user t
        WHERE
            t.open_id = #{openId}
    </select>
    <select id="selectShareUserByInvitedUserOpenId"  parameterType="String" resultMap="ToUserResult">
        SELECT t2.* FROM to_user t
                             JOIN to_user t2
                                  on t.invite_code = t2.share_code
        WHERE t.open_id = #{openId}
    </select>
</mapper>