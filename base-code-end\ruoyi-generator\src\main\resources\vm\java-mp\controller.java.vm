package com.ruoyi.app.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.${ClassName}Vo;
import com.ruoyi.app.manager.${ClassName}Manager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
#if($table.crud || $table.sub)
import com.ruoyi.framework.web.wrapper.Query;
#elseif($table.tree)
#end
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * ${functionName}Controller
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Api(tags = "${functionName}管理")
@RestController
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller {

    @Autowired
    private ${ClassName}Manager ${className}Manager;

    /**
     * 分页查询${functionName}
     */
    @ApiOperation("分页查询${functionName}")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
#if($table.crud || $table.sub)
    @GetMapping("/list")
    public R<IPage<${ClassName}Vo>> list(${ClassName}Vo ${className}Vo, Query query)
    {
        return R.ok(${className}Manager.page(${className}Vo, query));
    }
#elseif($table.tree)
    public AjaxResult list(${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return success(list);
    }
#end

    /**
    * 查询${functionName}全部列表
    */
    @ApiOperation("查询${functionName}全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
    @GetMapping("/allList")
    public R<List<${ClassName}Vo>> allList(${ClassName}Vo ${className}Vo){
        return R.ok(${className}Manager.list(${className}Vo));
    }

    /**
     * 导出${functionName}列表
     */
    @ApiOperation("导出${functionName}列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName}Vo ${className}Vo, Query query)
    {
        List<${ClassName}Vo> list = ${className}Manager.page(${className}Vo, query).getRecords();
        ExcelUtil<${ClassName}Vo> util = new ExcelUtil<${ClassName}Vo>(${ClassName}Vo.class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @ApiOperation("获取${functionName}详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "${pkColumn.javaField}", value = "主键", required = true, dataType = "${pkColumn.javaType}", paramType = "path", dataTypeClass = ${pkColumn.javaType}.class)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public R<${ClassName}Vo> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField})
    {
        return R.ok(${className}Manager.getInfo(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @ApiOperation("新增${functionName}")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ${ClassName}Vo ${className}Vo)
    {
        return ${className}Manager.add(${className}Vo) ? R.ok() : R.fail();
    }

    /**
     * 修改${functionName}
     */
    @ApiOperation("修改${functionName}")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ${ClassName}Vo ${className}Vo)
    {
        return ${className}Manager.edit(${className}Vo) ? R.ok() : R.fail();
    }

    /**
     * 删除${functionName}
     */
    @ApiOperation("删除${functionName}")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "${pkColumn.javaField}s", value = "主键", required = true, dataType = "${pkColumn.javaType}", paramType = "path", allowMultiple = true, dataTypeClass = ${pkColumn.javaType}.class)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public R<?> remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        return ${className}Manager.remove(${pkColumn.javaField}s) ? R.ok() : R.fail();
    }
}
