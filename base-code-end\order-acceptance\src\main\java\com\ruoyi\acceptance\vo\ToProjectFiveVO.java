package com.ruoyi.acceptance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目基本信息对象 ToProjectVo
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@ApiModel(value = "ToProjectFourVO", description = "项目基本信息对象VO")
public class ToProjectFiveVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /** 标题 */
    private String title;
    /** 选择类目， */
    private BigDecimal totalPrice;
    /** 选择类目 */
    private BigDecimal advancePrice;
    /** 期望交付时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date depositPaymentTime;

    private BigDecimal balancePayment;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date balancePaymentTime;

    private BigDecimal groupPrice;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectEndTime;

    private Integer orderPayStatus;

    private List<ToOrderSubVO> projectSubList;

}
