package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.domain.ToProject;
import com.ruoyi.acceptance.dto.ToOrderSubDTO;
import com.ruoyi.acceptance.service.IToProjectService;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.enums.OrderSubStatusEnum;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.acceptance.service.IToOrderSubService;
import com.ruoyi.app.vo.ToOrderSubVo;
import com.ruoyi.app.vo.convert.ToOrderSubConvert;
import com.ruoyi.acceptance.domain.ToOrderSub;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 接单订单子订单Manager
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Component
public class ToOrderSubManager {

    @Autowired
    private IToOrderSubService toOrderSubService;

    @Autowired
    private IToProjectService toProjectService;

    @Autowired
    private ToOrderSubConvert toOrderSubConvert;

    /**
    * 分页查询接单订单子订单
    */
    public IPage<ToOrderSubVo> page(ToOrderSubVo toOrderSubVo, Query query){
        IPage<ToOrderSub> page = toOrderSubService.pages(toOrderSubConvert.voToPo(toOrderSubVo), Condition.getPage(query));
        return (IPage<ToOrderSubVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToOrderSubVo>(), ToOrderSubConvert.INSTANCE);
    }

    /**
    * 查询接单订单子订单列表
    */
    public List<ToOrderSubVo> list(ToOrderSubVo toOrderSubVo) {
        List<ToOrderSub> toOrderSubList = toOrderSubService.selectList(toOrderSubConvert.voToPo(toOrderSubVo));
        return toOrderSubConvert.poToVoList(toOrderSubList);
    }


    public List<ToOrderSubVo> currentWxUserOrderSublist() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openid = wxLoginUser.getOpenid();
        List<ToOrderSubDTO> orderSubDTOList = toOrderSubService.selectListByOpenId(openid);

        return orderSubDTOList.stream()
                .map(dto -> {
                    ToOrderSubVo vo = new ToOrderSubVo();
                    BeanUtils.copyProperties(dto, vo);
                    return vo;
                })
                .collect(Collectors.toList());

    }

    /**
     * 查询接单订单子订单详细信息
     */
    public ToOrderSubVo getInfo(Long id) {
        ToOrderSub toOrderSub = toOrderSubService.getById(id);
        ToProject project = toProjectService.getById(toOrderSub.getProjectId());
        ToOrderSubVo toOrderSubVo = toOrderSubConvert.poToVo(toOrderSub);
        toOrderSubVo.setProjectTitle(project.getTitle());
        return toOrderSubVo;
    }

    /**
     * 新增接单订单子订单
     */
    public boolean add(ToOrderSubVo toOrderSubVo) {
        return toOrderSubService.save(toOrderSubConvert.voToPo(toOrderSubVo));
    }

    /**
     * 修改接单订单子订单
     */
    public boolean edit(ToOrderSubVo toOrderSubVo) {
        return toOrderSubService.updateById(toOrderSubConvert.voToPo(toOrderSubVo));
    }

    /**
     * 批量删除接单订单子订单
     */
    public boolean remove(Long[] ids) {
        return toOrderSubService.removeByIds(CollUtil.toList(ids));
    }

    public boolean addOrderSubFromWx(@Valid ToOrderSubVo toOrderSubVo) {
        //1.创建子订单，完善自定单的付款状态
        ToOrderSub toOrderSub = toOrderSubConvert.voToPo(toOrderSubVo);
        toOrderSub.setOrderPayStatus(OrderSubStatusEnum.PENDING.getCode());
        return toOrderSubService.save(toOrderSub);
    }
}
