package com.ruoyi.common.core.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.List;
import java.util.Map;
@Data
public class DoUserCardVo {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

//    /** 手机号 */
//    @Excel(name = "手机号")
//    private String phoneNumber;

    /** 微信号 */
    @Excel(name = "微信号")
    private String wechatId;

    @Excel(name = "openId")
    private String openId;

    @Excel(name = "头像")
    private String  headSculpture;

    /** 创建时间 */
    @Excel(name = "创建时间")

    private String createTime;

//    /** 更新时间 */
//    @Excel(name = "更新时间")
//    private String updateTime;
//
//    /** 创建时间 */
//    @Excel(name = "创建时间")
//    private String createBy;
//
//    /** 更新时间 */
//    @Excel(name = "更新时间")
//    private String updateBy;
//    private String roleKey;

    /** 商户信誉分 */
    @Excel(name = "性别")
    private String sex;

//    /** 商户信誉分 */
//    @Excel(name = "生日")
//    @JsonFormat(pattern = "yyyy-MM-dd")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    private Date birthday;

    /** 商户信誉分 */
    @Excel(name = "接单一级类目")
    private String orderCategoryOne;

    /** 商户信誉分 */
    @Excel(name = "接单二级类目")
    private String orderCategoryTwo;

    /**
     * 简介
     */
    private String personalProfile;

    private Integer receivingOrdersCount;

    private Integer receivingOrdersDuration;

    /**
     * 选择类目中文名（非数据库字段，仅用于前端展示）
     */

    private Map<String,String> categorySelectionMap;

    private List<Map<String,Object>> projectList;
}
