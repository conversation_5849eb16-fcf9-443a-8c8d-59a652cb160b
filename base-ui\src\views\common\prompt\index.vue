<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="90px"
    >
      <el-form-item label="AI类型" prop="aiType">
        <el-select
          v-model="queryParams.aiType"
          placeholder="请选择AI类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.ai_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提示词标题" prop="promptTitle">
        <el-input
          v-model="queryParams.promptTitle"
          placeholder="请输入提示词标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="isActive">
        <el-select
          v-model="queryParams.isActive"
          placeholder="请选择是否启用"
          clearable
        >
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:prompt:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:prompt:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:prompt:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:prompt:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="promptList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="promptId" />
      <el-table-column label="AI类型" align="center" prop="aiType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ai_type" :value="scope.row.aiType" />
        </template>
      </el-table-column>
      <el-table-column label="提示词标题" align="center" prop="promptTitle" />
      <el-table-column label="提示词内容" align="center" prop="content" />
      <el-table-column label="是否启用" align="center" prop="isActive">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_normal_disable"
            :value="scope.row.isActive"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:prompt:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:prompt:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改提示词对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="AI类型" prop="aiType">
          <el-select v-model="form.aiType" placeholder="请选择AI类型">
            <el-option
              v-for="dict in dict.type.ai_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提示词标题" prop="promptTitle">
          <el-input v-model="form.promptTitle" placeholder="请输入提示词标题" />
        </el-form-item>
        <el-form-item label="系统人设" prop="systemPrompt">
          <el-input
            v-model="form.systemPrompt"
            type="textarea"
            :rows="3"
            placeholder="请输入系统人设"
          />
        </el-form-item>
        <el-form-item label="提示词类目" prop="projectTypes">
          <treeselect
            v-model="form.projectTypes"
            :options="categoryOptions"
            :show-count="true"
            multiple
            show-checkbox
            :value-consists-of="'ALL'"
            placeholder="请选择提示词类目"
          />
        </el-form-item>
        <el-form-item label="提示词内容" prop="content">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否启用" prop="isActive">
              <el-radio-group v-model="form.isActive">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="深度思考" prop="enableThinking">
              <el-radio-group v-model="form.enableThinking">
                <el-radio
                  v-for="dict in dict.type.ai_status"
                  :key="dict.value"
                  :label="parseInt(dict.value)"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="启用搜索" prop="enableSearch">
          <el-radio-group v-model="form.enableSearch">
            <el-radio
              v-for="dict in dict.type.ai_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPrompt,
  getPrompt,
  delPrompt,
  addPrompt,
  updatePrompt,
} from "@/api/common/prompt";
import { getCategoryTree } from "@/api/common/category";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Prompt",
  dicts: ["ai_type", "sys_normal_disable", "ai_status"],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 提示词表格数据
      promptList: [],
      // 类目树选项
      categoryOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        aiType: null,
        promptTitle: null,
        projectType: null,
        content: null,
        isActive: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        aiType: [
          { required: true, message: "AI类型不能为空", trigger: "change" },
        ],
        promptTitle: [
          { required: true, message: "提示词标题不能为空", trigger: "blur" },
        ],
        systemPrompt: [
          { required: true, message: "系统人设不能为空", trigger: "blur" },
        ],
        // projectTypes: [
        //   { required: true, message: "提示词类目不能为空", trigger: "change" },
        // ],
        content: [
          { required: true, message: "提示词内容不能为空", trigger: "blur" },
        ],
        isActive: [
          { required: true, message: "是否启用不能为空", trigger: "change" },
        ],
        enableThinking: [
          {
            required: true,
            message: "是否启用深度思考不能为空",
            trigger: "change",
          },
        ],
        enableSearch: [
          {
            required: true,
            message: "是否启用搜索不能为空",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getCategoryTree();
  },
  methods: {
    /** 查询提示词列表 */
    getList() {
      this.loading = true;
      listPrompt(this.queryParams).then((response) => {
        this.promptList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 查询类目树结构 */
    getCategoryTree() {
      getCategoryTree().then((response) => {
        this.categoryOptions = this.processCategoryTree(response.data);
      });
    },
    /** 处理类目树结构，只保留两层 */
    processCategoryTree(data) {
      if (!Array.isArray(data)) {
        return [];
      }

      return data.map((item) => {
        const processedItem = {
          id: item.code,
          label: item.name,
        };

        // 只处理一层子孩子
        if (item.children && item.children.length > 0) {
          processedItem.children = item.children.map((child) => ({
            id: child.code,
            label: child.name,
            isLeaf: true,
          }));
        }

        return processedItem;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        promptId: null,
        aiType: null,
        systemPrompt: null,
        promptTitle: null,
        content: null,
        isActive: 0,
        enableThinking: 0,
        enableSearch: 0,
        projectTypes: [],
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.promptId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加提示词";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const promptId = row.promptId || this.ids;
      getPrompt(promptId).then((response) => {
        this.form = response.data;
        // 处理类目数据，转换为前端需要的格式
        if (
          this.form.commonPromptProjectTypes &&
          this.form.commonPromptProjectTypes.length > 0
        ) {
          this.form.projectTypes = this.form.commonPromptProjectTypes;
        } else {
          this.form.projectTypes = [];
        }
        console.log("编辑时的projectTypes:", this.form.projectTypes);
        this.open = true;
        this.title = "修改提示词";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          console.log("提交前的projectTypes:", this.form.projectTypes);
          // 处理表单数据，转换为后端需要的格式
          if (this.form.projectTypes && this.form.projectTypes.length > 0) {
            this.form.commonPromptProjectTypes = this.form.projectTypes;
          }
          console.log(
            "转换后的commonPromptProjectTypes:",
            this.form.commonPromptProjectTypes
          );

          if (this.form.promptId != null) {
            updatePrompt(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPrompt(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const promptIds = row.promptId || this.ids;
      this.$modal
        .confirm('是否确认删除提示词编号为"' + promptIds + '"的数据项？')
        .then(function () {
          return delPrompt(promptIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/prompt/export",
        {
          ...this.queryParams,
        },
        `prompt_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
