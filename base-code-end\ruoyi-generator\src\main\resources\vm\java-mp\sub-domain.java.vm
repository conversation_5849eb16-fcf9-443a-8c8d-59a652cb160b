package ${packageName}.domain;

#foreach ($import in $subImportList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.POBaseEntity;

/**
 * ${subTable.functionName}对象 ${subTableName}
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@TableName("${tableName}")
public class ${subClassName} extends PoBaseEntity {

    private static final long serialVersionUID = 1L;

#foreach ($column in $subTable.columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
#else
    @Excel(name = "${comment}")
#end
#end
#if(${column.isPk} == 1)
#if(${column.isIncrement} == 1)
    @TableId(type = IdType.AUTO)
#else
    @TableId(type = IdType.ASSIGN_ID)
#end
#end
    private $column.javaType $column.javaField;

#end
#end
}
