package com.ruoyi.app.controller.orderHistory;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ToOrderHistoryVo;
import com.ruoyi.app.manager.ToOrderHistoryManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 订单流水Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Api(tags = "订单流水管理")
@RestController
@RequestMapping("/acceptance/orderHistory")
public class ToOrderHistoryController {

    @Autowired
    private ToOrderHistoryManager toOrderHistoryManager;

    /**
     * 分页查询订单流水
     */
    @ApiOperation("分页查询订单流水")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('acceptance:history:list')")
    @GetMapping("/list")
    public R<IPage<ToOrderHistoryVo>> list(ToOrderHistoryVo toOrderHistoryVo, Query query)
    {
        return R.ok(toOrderHistoryManager.page(toOrderHistoryVo, query));
    }

    /**
    * 查询订单流水全部列表
    */
    @ApiOperation("查询订单流水全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('acceptance:history:list')")
    @GetMapping("/allList")
    public R<List<ToOrderHistoryVo>> allList(ToOrderHistoryVo toOrderHistoryVo){
        return R.ok(toOrderHistoryManager.list(toOrderHistoryVo));
    }

    /**
     * 导出订单流水列表
     */
    @ApiOperation("导出订单流水列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('acceptance:history:export')")
    @Log(title = "订单流水", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToOrderHistoryVo toOrderHistoryVo, Query query)
    {
        List<ToOrderHistoryVo> list = toOrderHistoryManager.page(toOrderHistoryVo, query).getRecords();
        ExcelUtil<ToOrderHistoryVo> util = new ExcelUtil<ToOrderHistoryVo>(ToOrderHistoryVo.class);
        util.exportExcel(response, list, "订单流水数据");
    }

    /**
     * 获取订单流水详细信息
     */
    @ApiOperation("获取订单流水详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:history:query')")
    @GetMapping(value = "/{id}")
    public R<ToOrderHistoryVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toOrderHistoryManager.getInfo(id));
    }

    /**
     * 新增订单流水
     */
    @ApiOperation("新增订单流水")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('acceptance:history:add')")
    @Log(title = "订单流水", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToOrderHistoryVo toOrderHistoryVo)
    {
        return toOrderHistoryManager.add(toOrderHistoryVo) ? R.ok() : R.fail();
    }

    /**
     * 修改订单流水
     */
    @ApiOperation("修改订单流水")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('acceptance:history:edit')")
    @Log(title = "订单流水", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToOrderHistoryVo toOrderHistoryVo)
    {
        return toOrderHistoryManager.edit(toOrderHistoryVo) ? R.ok() : R.fail();
    }

    /**
     * 删除订单流水
     */
    @ApiOperation("删除订单流水")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:history:remove')")
    @Log(title = "订单流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toOrderHistoryManager.remove(ids) ? R.ok() : R.fail();
    }

    /**
     * 移动端查询流水报表
     */
    @ApiOperation("分销报表")
    @GetMapping("/distributionReport/{date}")
    public R<?> distributionReport(@PathVariable("date")  String date){
        return toOrderHistoryManager.distributionReport(date);
    }

    /**
     * 邀请人员排行榜
     */
    @ApiOperation("邀请人员排行榜")
    @GetMapping("/invitationRanking")
    public R<?> invitationRanking(){
        return toOrderHistoryManager.invitationRanking();
    }

    /**
     * 佣金排行榜
     */
    @ApiOperation("佣金排行榜")
    @GetMapping("/commissionRanking")
    public R<?> commissionRanking(){
        return toOrderHistoryManager.commissionRanking();
    }

    /**
     * 佣金排行榜
     */
    @ApiOperation("交易单量列表")
    @GetMapping("/transactionList/{date}")
    public R<?> transactionList(@PathVariable("date") String date){
        return toOrderHistoryManager.transactionList(date);
    }

}
