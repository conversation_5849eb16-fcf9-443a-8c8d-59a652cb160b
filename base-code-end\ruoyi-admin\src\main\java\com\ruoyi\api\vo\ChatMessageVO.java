package com.ruoyi.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ChatMessageVO {
    private String from;    // 发送者ID
    private String to;      // 接收者ID/群组ID
    private String content; // 消息内容
    private String type;    // single/group

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time = LocalDateTime.now();
}
