package com.ruoyi.web.vo.convert;

import com.ruoyi.common.core.domain.convert.BeanDtoVoMapper;
import com.ruoyi.system.dto.CommonPromptDTO;
import com.ruoyi.web.vo.CommonPromptVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 提示词PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CommonPromptConvert extends BeanDtoVoMapper<CommonPromptDTO, CommonPromptVo> {

    CommonPromptConvert INSTANCE = Mappers.getMapper(CommonPromptConvert.class);

}
