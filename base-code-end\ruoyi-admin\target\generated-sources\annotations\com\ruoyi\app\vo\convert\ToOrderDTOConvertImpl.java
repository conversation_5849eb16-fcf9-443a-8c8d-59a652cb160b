package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.dto.ToOrderDTO;
import com.ruoyi.acceptance.vo.ToOrderVo;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToOrderDTOConvertImpl implements ToOrderDTOConvert {

    @Override
    public ToOrderVo poToVo(ToOrderDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderVo toOrderVo = new ToOrderVo();

        if ( arg0.getAdvancePrice() != null ) {
            toOrderVo.setAdvancePrice( arg0.getAdvancePrice().toString() );
        }
        toOrderVo.setApplyRefund( arg0.getApplyRefund() );
        if ( arg0.getBalancePayment() != null ) {
            toOrderVo.setBalancePayment( arg0.getBalancePayment().toString() );
        }
        toOrderVo.setBalancePaymentTime( arg0.getBalancePaymentTime() );
        if ( arg0.getDepositPaymentTime() != null ) {
            toOrderVo.setDepositPaymentTime( new SimpleDateFormat().format( arg0.getDepositPaymentTime() ) );
        }
        toOrderVo.setGroupPurchase( arg0.getGroupPurchase() );
        toOrderVo.setId( arg0.getId() );
        toOrderVo.setOrderPayStatus( arg0.getOrderPayStatus() );
        toOrderVo.setOutTradeNo( arg0.getOutTradeNo() );
        toOrderVo.setSettlement( arg0.getSettlement() );
        if ( arg0.getTotalPrice() != null ) {
            toOrderVo.setTotalPrice( arg0.getTotalPrice().toString() );
        }
        toOrderVo.setTransactionId( arg0.getTransactionId() );

        return toOrderVo;
    }

    @Override
    public List<ToOrderVo> poToVoList(List<ToOrderDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderVo> list = new ArrayList<ToOrderVo>( arg0.size() );
        for ( ToOrderDTO toOrderDTO : arg0 ) {
            list.add( poToVo( toOrderDTO ) );
        }

        return list;
    }

    @Override
    public ToOrderDTO voToPo(ToOrderVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderDTO toOrderDTO = new ToOrderDTO();

        if ( arg0.getAdvancePrice() != null ) {
            toOrderDTO.setAdvancePrice( new BigDecimal( arg0.getAdvancePrice() ) );
        }
        toOrderDTO.setApplyRefund( arg0.getApplyRefund() );
        if ( arg0.getBalancePayment() != null ) {
            toOrderDTO.setBalancePayment( new BigDecimal( arg0.getBalancePayment() ) );
        }
        toOrderDTO.setBalancePaymentTime( arg0.getBalancePaymentTime() );
        try {
            if ( arg0.getDepositPaymentTime() != null ) {
                toOrderDTO.setDepositPaymentTime( new SimpleDateFormat().parse( arg0.getDepositPaymentTime() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        toOrderDTO.setGroupPurchase( arg0.getGroupPurchase() );
        toOrderDTO.setId( arg0.getId() );
        toOrderDTO.setOrderPayStatus( arg0.getOrderPayStatus() );
        toOrderDTO.setOutTradeNo( arg0.getOutTradeNo() );
        toOrderDTO.setSettlement( arg0.getSettlement() );
        if ( arg0.getTotalPrice() != null ) {
            toOrderDTO.setTotalPrice( new BigDecimal( arg0.getTotalPrice() ) );
        }
        toOrderDTO.setTransactionId( arg0.getTransactionId() );

        return toOrderDTO;
    }

    @Override
    public List<ToOrderDTO> voToPoList(List<ToOrderVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderDTO> list = new ArrayList<ToOrderDTO>( arg0.size() );
        for ( ToOrderVo toOrderVo : arg0 ) {
            list.add( voToPo( toOrderVo ) );
        }

        return list;
    }
}
