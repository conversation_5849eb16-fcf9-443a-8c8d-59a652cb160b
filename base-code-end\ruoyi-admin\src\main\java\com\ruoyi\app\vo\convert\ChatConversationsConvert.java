package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatConversationsVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.dispatch.domain.ChatConversations;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 会话PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ChatConversationsConvert extends BeanPoVoMapper<ChatConversations, ChatConversationsVo> {

        ChatConversationsConvert INSTANCE = Mappers.getMapper(ChatConversationsConvert.class);

}
