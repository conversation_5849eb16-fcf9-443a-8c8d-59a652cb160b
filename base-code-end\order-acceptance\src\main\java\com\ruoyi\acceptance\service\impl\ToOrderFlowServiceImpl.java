package com.ruoyi.acceptance.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import com.ruoyi.acceptance.domain.ToOrderFlow;
import com.ruoyi.acceptance.mapper.ToOrderFlowMapper;
import com.ruoyi.acceptance.service.IToOrderFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 订单流水Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Service
public class ToOrderFlowServiceImpl extends ServiceImpl<ToOrderFlowMapper, ToOrderFlow> implements IToOrderFlowService {

    @Autowired
    private ToOrderFlowMapper toOrderFlowMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToOrderFlow> queryWrapper(ToOrderFlow toOrderFlow) {
        LambdaQueryWrapper<ToOrderFlow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderFlow.getOrderId()), ToOrderFlow::getOrderId, toOrderFlow.getOrderId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderFlow.getFlowType()), ToOrderFlow::getFlowType, toOrderFlow.getFlowType());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderFlow.getOutTradeNo()), ToOrderFlow::getOutTradeNo, toOrderFlow.getOutTradeNo());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderFlow.getTransactionId()), ToOrderFlow::getTransactionId, toOrderFlow.getTransactionId());
        return queryWrapper;
    }

    /**
     * 查询订单流水分页
     *
     * @param toOrderFlow 订单流水
     * @return 订单流水
     */
    @Override
    public IPage<ToOrderFlow> pages(ToOrderFlow toOrderFlow, IPage<ToOrderFlow> page)
    {
        return toOrderFlowMapper.selectPage(page, this.queryWrapper(toOrderFlow));
    }

    /**
     * 查询订单流水列表
     * 
     * @param toOrderFlow 订单流水
     * @return 订单流水
     */
    @Override
    public List<ToOrderFlow> selectList(ToOrderFlow toOrderFlow)
    {
        return toOrderFlowMapper.selectList(this.queryWrapper(toOrderFlow));
    }

}
