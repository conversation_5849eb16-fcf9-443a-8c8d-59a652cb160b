package com.ruoyi.acceptance.service.impl;

import java.math.BigDecimal;
import java.util.List;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.acceptance.domain.ToWallet;
import com.ruoyi.acceptance.domain.ToWalletHistory;
import com.ruoyi.acceptance.mapper.ToWalletHistoryMapper;
import com.ruoyi.acceptance.service.IToWalletService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.acceptance.mapper.ToWalletMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 钱包Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
@Slf4j
@Service
public class ToWalletServiceImpl extends ServiceImpl<ToWalletMapper, ToWallet> implements IToWalletService {

    @Autowired
    private ToWalletMapper toWalletMapper;
    @Autowired
    private ToWalletHistoryMapper toWalletHistoryMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToWallet> queryWrapper(ToWallet toWallet) {
        LambdaQueryWrapper<ToWallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toWallet.getBalance()), ToWallet::getBalance, toWallet.getBalance());
        queryWrapper.eq(ObjUtil.isNotEmpty(toWallet.getOpenId()), ToWallet::getOpenId, toWallet.getOpenId());
        return queryWrapper;
    }

    /**
     * 查询钱包分页
     *
     * @param toWallet 钱包
     * @return 钱包
     */
    @Override
    public IPage<ToWallet> pages(ToWallet toWallet, IPage<ToWallet> page)
    {
        return toWalletMapper.selectPage(page, this.queryWrapper(toWallet));
    }

    /**
     * 查询钱包列表
     * 
     * @param toWallet 钱包
     * @return 钱包
     */
    @Override
    public List<ToWallet> selectList(ToWallet toWallet)
    {
        return toWalletMapper.selectList(this.queryWrapper(toWallet));
    }

    @Override
    public ToWallet getInfoByOpenId() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        log.info("getInfoByOpenId===>{}",openId);
        QueryWrapper<ToWallet> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("open_id",openId);
        ToWallet toWallet = toWalletMapper.selectOne(queryWrapper);
        toWallet.setOpenId(null);
        return toWallet;
    }

    @Override
    public List<ToWallet> selectToWalletsByOpenId(List<String> openIds) {
        LambdaQueryWrapper<ToWallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ToWallet::getOpenId, openIds);
        return this.list(queryWrapper);
    }

    @Override
    public R<?> withdrawal(BigDecimal withdrawalAmount) {
        String openId = SecurityUtils.getWxLoginUser().getOpenid();
        ToWallet toWallet = toWalletMapper.selectOne(Wrappers.lambdaQuery(ToWallet.class).eq(ToWallet::getOpenId, openId));
        toWallet.setBalance(toWallet.getBalance().subtract(withdrawalAmount));
        toWalletMapper.updateById(toWallet);

        ToWalletHistory toWalletHistory = new ToWalletHistory();
        toWalletHistory.setOpenId(openId);
        toWalletHistory.setWithdrawnAmount(withdrawalAmount);
        toWalletHistoryMapper.insert(toWalletHistory);
        return R.ok("提现成功");
    }

}
