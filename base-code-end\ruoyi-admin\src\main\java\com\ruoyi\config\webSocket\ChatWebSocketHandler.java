package com.ruoyi.config.webSocket;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.dispatch.domain.ChatMessages;
import com.ruoyi.dispatch.mapper.ChatMessagesMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class ChatWebSocketHandler extends TextWebSocketHandler  {
    private static final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final ChatMessagesMapper chatMessagesMapper;

    public ChatWebSocketHandler(ChatMessagesMapper chatMessagesMapper) {
        this.chatMessagesMapper = chatMessagesMapper;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String userId = session.getHandshakeHeaders().getFirst("user-id");
        sessions.put(userId, session);
//        broadcast("用户" + userId + "加入聊天室");
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        String userId = session.getHandshakeHeaders().getFirst("user-id");
        String payload = message.getPayload();
        log.info("用户{}发送消息：{}", userId, payload);


        JSONObject msgObj = JSON.parseObject(message.getPayload());
        String string = msgObj.getString("msg");
        if("ping".equals(string)) {
            return;
        }

        String type = msgObj.getString("type"); // 消息类型：single/group
        log.info("接收到消息：{}", message.getPayload());
        if(!StringUtils.isEmpty(type) && "single".equals(type)) {
            sendToUser(msgObj.getString("to"), message.getPayload());
        } else {
            broadcast(message.getPayload());
        }

        ChatMessages chatMessages = new ChatMessages();
        chatMessages.setConversationId(Long.valueOf(msgObj.getString("conversationId")));
        chatMessages.setSenderId(msgObj.getString("senderId"));
        chatMessages.setContentType(1);
        chatMessages.setContent(msgObj.getString("msg"));
//        chatMessages.setFileUrl();
//        chatMessages.setFileSize();
//        chatMessages.setIsRecalled();
        chatMessagesMapper.insert(chatMessages);

    }

    @SneakyThrows
    private void sendToUser(String userId, String message) {
        WebSocketSession target = sessions.get(userId);
        if(target != null && target.isOpen()) {
            target.sendMessage(new TextMessage(message));
        }
    }

    private void broadcast(String message) {
        sessions.values().stream()
                .filter(WebSocketSession::isOpen)
                .forEach(session -> {
                    try {
                        session.sendMessage(new TextMessage(message));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
    }

}
