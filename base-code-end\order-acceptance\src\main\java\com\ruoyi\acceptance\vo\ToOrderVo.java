package com.ruoyi.acceptance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 接单订单对象 ToOrderVo
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ApiModel(value = " ToOrderVo", description = "接单订单对象VO")
public class ToOrderVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    @Excel(name = "客户")
    @ApiModelProperty(value = "客户", name="custom_openid")
    private String customOpenId;
    private String projectId;
    /** 预付款价 */
    @Excel(name = "预付款价")
    @ApiModelProperty(value = "预付款价", name="advancePrice")
    private String advancePrice;
    /** 尾款 */
    @Excel(name = "尾款")
    @ApiModelProperty(value = "尾款", name="balancePayment")
    private String balancePayment;
    /** 定金支付时间 */
    @Excel(name = "定金支付时间")
    @ApiModelProperty(value = "定金支付时间", name="depositPaymentTime")
    private String depositPaymentTime;
    /** 全额价格 */
    @Excel(name = "全额价格")
    @ApiModelProperty(value = "全额价格", name="totalPrice")
    private String totalPrice;

    /** 是否申请退款 */
    @Excel(name = "是否申请退款")
    @ApiModelProperty(value = "是否申请退款", name="applyRefund")
    private Integer applyRefund;
    /** 是否团购 */
    @Excel(name = "是否团购")
    @ApiModelProperty(value = "是否团购", name="groupPurchase")
    private Integer groupPurchase;
    /** 是否结清尾款 */
    @Excel(name = "是否结清尾款")
    @ApiModelProperty(value = "是否结清尾款", name="settlement")
    private Integer settlement;
    private Integer orderPayStatus;
    private String outTradeNo;
    private String transactionId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date balancePaymentTime;


}
