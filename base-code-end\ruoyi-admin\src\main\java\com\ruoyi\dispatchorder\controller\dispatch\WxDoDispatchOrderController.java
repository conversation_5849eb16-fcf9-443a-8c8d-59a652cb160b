package com.ruoyi.dispatchorder.controller.dispatch;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dispatchorder.manager.DoDispatchOrderManager;
import com.ruoyi.dispatchorder.vo.DoDispatchOrderVo;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 派单Controller
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
@Api(tags = "微信派单管理")
@RestController
@RequestMapping("/wx/dispatch/dispatch")
public class WxDoDispatchOrderController {

    @Autowired
    private DoDispatchOrderManager doDispatchOrderManager;

    /**
     * 分页查询派单
     */
    @ApiOperation("分页查询派单")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public R<IPage<DoDispatchOrderVo>> list(DoDispatchOrderVo doDispatchOrderVo, Query query)
    {
        return R.ok(doDispatchOrderManager.page(doDispatchOrderVo, query));
    }

    /**
    * 查询派单全部列表
    */
    @ApiOperation("查询派单全部列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/allList")
    public R<List<DoDispatchOrderVo>> allList(DoDispatchOrderVo doDispatchOrderVo){
        return R.ok(doDispatchOrderManager.list(doDispatchOrderVo));
    }

    /**
     * 导出派单列表
     */
    @ApiOperation("导出派单列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DoDispatchOrderVo doDispatchOrderVo, Query query)
    {
        List<DoDispatchOrderVo> list = doDispatchOrderManager.page(doDispatchOrderVo, query).getRecords();
        ExcelUtil<DoDispatchOrderVo> util = new ExcelUtil<DoDispatchOrderVo>(DoDispatchOrderVo.class);
        util.exportExcel(response, list, "派单数据");
    }

    /**
     * 获取派单详细信息
     */
    @ApiOperation("获取派单详细")
    @ApiOperationSupport(order = 3)
    @GetMapping(value = "/{id}")
    public R<DoDispatchOrderVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(doDispatchOrderManager.getInfo(id));
    }

    /**
     * 新增派单
     */
    @ApiOperation("新增派单")
    @ApiOperationSupport(order = 4)
    @PostMapping
    public R<?> add(@Valid @RequestBody DoDispatchOrderVo doDispatchOrderVo)
    {
        return doDispatchOrderManager.add(doDispatchOrderVo) ? R.ok() : R.fail();
    }

    /**
     * 修改派单
     */
    @ApiOperation("修改派单")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public R<?> edit(@Valid @RequestBody DoDispatchOrderVo doDispatchOrderVo)
    {
        return doDispatchOrderManager.edit(doDispatchOrderVo) ? R.ok() : R.fail();
    }

    /**
     * 删除派单
     */
    @ApiOperation("删除派单")
    @ApiOperationSupport(order = 6)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return doDispatchOrderManager.remove(ids) ? R.ok() : R.fail();
    }
}
