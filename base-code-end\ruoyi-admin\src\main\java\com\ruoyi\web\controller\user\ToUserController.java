package com.ruoyi.web.controller.user;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.ToUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.acceptance.service.IToUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Api(tags = "微信用户信息")
@RestController
@RequestMapping("/user")
public class ToUserController extends BaseController
{
    @Autowired
    private IToUserService toUserService;

    /**
     * 查询用户信息列表
     */
    @ApiOperation("查询用户信息列表")
    @PreAuthorize("@ss.hasPermi('acceptance:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(ToUser toUser)
    {
        startPage();
        List<ToUser> list = toUserService.selectToUserList(toUser);
        return getDataTable(list);
    }

    @ApiOperation("下拉框查询用户列表")
    @GetMapping("/getUserList/{phoneNumber}")
    public List<ToUser> getUserList(@PathVariable String phoneNumber)
    {
        List<ToUser> list = toUserService.getUserList(phoneNumber);
        return list;
    }

    /**
     * 导出用户信息列表
     */
    @ApiOperation("web端导出用户信息excel")
    @PreAuthorize("@ss.hasPermi('acceptance:user:export')")
    @Log(title = "用户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToUser toUser)
    {
        List<ToUser> list = toUserService.selectToUserList(toUser);
        ExcelUtil<ToUser> util = new ExcelUtil<ToUser>(ToUser.class);
        util.exportExcel(response, list, "用户信息数据");
    }

    /**
     * 获取用户信息详细信息
     */
    @ApiOperation("web端")
    @PreAuthorize("@ss.hasPermi('acceptance:user:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(toUserService.selectToUserById(id));
    }

    @ApiOperation("通过openid获取用户信息")
    @PreAuthorize("@ss.hasPermi('acceptance:user:query')")
    @GetMapping(value = "/getInfoByOpenId")
    public AjaxResult getInfoByOpenId()
    {
        return success(toUserService.selectToUserByOpenId());
    }

    /**
     * 修改用户信息
     */
    @ApiOperation("web端修改用户信息")
    @PreAuthorize("@ss.hasPermi('acceptance:user:edit')")
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@RequestBody ToUser toUser)
    {
        return toUserService.updateToUser(toUser) == 0 ? R.fail() : R.ok();
    }

    /**
     * 删除用户信息
     */
    @ApiOperation("")
    @PreAuthorize("@ss.hasPermi('acceptance:user:remove')")
    @Log(title = "用户信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(toUserService.deleteToUserByIds(ids));
    }
}
