package com.ruoyi.acceptance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.domain.ToProject;
import com.ruoyi.acceptance.dto.ToProjectTwoDTO;
import com.ruoyi.acceptance.vo.*;

import java.util.List;

/**
 * 项目基本信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
public interface IToProjectService extends IService<ToProject> {

    /**
     * 分页查询项目基本信息
     * @param toProject 项目基本信息
     * @param page 分页条件
     * @return 项目基本信息集合
     */
    IPage<ToProject> pages(ToProject toProject, IPage<ToProject> page);

    /**
     * 查询项目基本信息列表
     * 
     * @param toProject 项目基本信息
     * @return 项目基本信息集合
     */
     List<ToProject> selectList(ToProject toProject);

    ToProject saveProjectFromWx(ToProject toProject);

    List<ToProject> selectListByUserOpenId(String openId);

    List<ToProject> selectListByMerchantOpenId(String openId);


    List<ToProject> selectOneByUserOpenId(String openId);

    List<ToProjectThreeVO> projectList(ToProjectTwoVO toProjectTwoVO);

    ToProjectFourVO projectDetail(String projectId);

    List<ToProjectSixVO> myProjectList(ToProjectTwoDTO toProjectTwoDTO);

    ToProjectFiveVO myProjectDetail(String projectId);
}
