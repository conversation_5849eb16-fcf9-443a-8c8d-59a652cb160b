package com.ruoyi.app.controller.flow;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ToOrderFlowVo;
import com.ruoyi.app.manager.ToOrderFlowManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 订单流水Controller
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Api(tags = "订单流水管理")
@RestController
@RequestMapping("/acceptance/flow")
public class ToOrderFlowController {

    @Autowired
    private ToOrderFlowManager toOrderFlowManager;

    /**
     * 分页查询订单流水
     */
    @ApiOperation("分页查询订单流水")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('system:flow:list')")
    @GetMapping("/list")
    public R<IPage<ToOrderFlowVo>> list(ToOrderFlowVo toOrderFlowVo, Query query)
    {
        return R.ok(toOrderFlowManager.page(toOrderFlowVo, query));
    }

    /**
    * 查询订单流水全部列表
    */
    @ApiOperation("查询订单流水全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('system:flow:list')")
    @GetMapping("/allList")
    public R<List<ToOrderFlowVo>> allList(ToOrderFlowVo toOrderFlowVo){
        return R.ok(toOrderFlowManager.list(toOrderFlowVo));
    }

    /**
     * 导出订单流水列表
     */
    @ApiOperation("导出订单流水列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('system:flow:export')")
    @Log(title = "订单流水", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToOrderFlowVo toOrderFlowVo, Query query)
    {
        List<ToOrderFlowVo> list = toOrderFlowManager.page(toOrderFlowVo, query).getRecords();
        ExcelUtil<ToOrderFlowVo> util = new ExcelUtil<ToOrderFlowVo>(ToOrderFlowVo.class);
        util.exportExcel(response, list, "订单流水数据");
    }

    /**
     * 获取订单流水详细信息
     */
    @ApiOperation("获取订单流水详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:flow:query')")
    @GetMapping(value = "/{id}")
    public R<ToOrderFlowVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toOrderFlowManager.getInfo(id));
    }

    /**
     * 新增订单流水
     */
    @ApiOperation("新增订单流水")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('system:flow:add')")
    @Log(title = "订单流水", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToOrderFlowVo toOrderFlowVo)
    {
        return toOrderFlowManager.add(toOrderFlowVo) ? R.ok() : R.fail();
    }

    /**
     * 修改订单流水
     */
    @ApiOperation("修改订单流水")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('system:flow:edit')")
    @Log(title = "订单流水", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToOrderFlowVo toOrderFlowVo)
    {
        return toOrderFlowManager.edit(toOrderFlowVo) ? R.ok() : R.fail();
    }

    /**
     * 删除订单流水
     */
    @ApiOperation("删除订单流水")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('system:flow:remove')")
    @Log(title = "订单流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toOrderFlowManager.remove(ids) ? R.ok() : R.fail();
    }
}
