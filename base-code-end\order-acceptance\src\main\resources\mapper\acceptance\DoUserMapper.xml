<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.DoUserMapper">
    
    <resultMap type="com.ruoyi.common.core.domain.entity.DoUser" id="DoUserResult">
        <result property="id"    column="id"    />
        <result property="openId"    column="open_id"    />
        <result property="nickname"    column="nickname"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="wechatId"    column="wechat_id"    />
        <result property="headSculpture"    column="head_sculpture"    />
        <result property="creditScore"    column="credit_score"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDoUserVo">
        select id, open_id, nickname, phone_number, wechat_id, head_sculpture, credit_score, create_time, create_by, update_time, update_by from do_user
    </sql>

    <select id="selectDoUserList" parameterType="com.ruoyi.common.core.domain.entity.DoUser" resultMap="DoUserResult">
        <include refid="selectDoUserVo"/>
        <where>  
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="wechatId != null  and wechatId != ''"> and wechat_id like concat('%', #{wechatId}, '%')</if>
        </where>
    </select>

    <select id="selectToUserByOpenId1" parameterType="String" resultMap="DoUserResult">
        <include refid="selectDoUserVo"/>
        where open_id = #{openId}
    </select>
    
    <select id="selectDoUserById" parameterType="Long" resultMap="DoUserResult">
        <include refid="selectDoUserVo"/>
        where id = #{id}
    </select>
    <select id="selectToUserByOpenId" resultType="com.ruoyi.common.core.domain.vo.DoUserVO">
        select du.id, du.nickname, du.phone_number, du.wechat_id, du.open_id, du.head_sculpture,du.credit_score,du.sex,du.birthday,du.order_category_one,du.order_category_two,du.merchant_type, du.create_time, du.create_by, du.update_time, du.update_by,sr.role_key  from do_user du
        left join sys_user_role sur on du.id = sur.user_id
        left join sys_role sr on sur.role_id = sr.role_id
        where du.open_id = #{openId}
    </select>

    <insert id="insertDoUser" parameterType="com.ruoyi.common.core.domain.entity.DoUser">
        insert into do_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="openId != null">open_id,</if>
            <if test="nickname != null">nickname,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="wechatId != null">wechat_id,</if>
            <if test="headSculpture != null">head_sculpture,</if>
            <if test="creditScore != null">credit_score,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="openId != null">#{openId},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="wechatId != null">#{wechatId},</if>
            <if test="headSculpture != null">#{headSculpture},</if>
            <if test="creditScore != null">#{creditScore},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>
    <insert id="save">


    </insert>

    <update id="updateDoUser" parameterType="com.ruoyi.common.core.domain.entity.DoUser">
        update do_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="openId != null">open_id = #{openId},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="wechatId != null">wechat_id = #{wechatId},</if>
            <if test="headSculpture != null">head_sculpture = #{headSculpture},</if>
            <if test="creditScore != null">credit_score = #{creditScore},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDoUserById" parameterType="Long">
        delete from do_user where id = #{id}
    </delete>

    <delete id="deleteDoUserByIds" parameterType="String">
        delete from do_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>