package com.ruoyi.acceptance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class ToOrderDTO {
    private static final long serialVersionUID = 1L;
    /** ID */
    private Long id;
    /** 论文ID */
    private Long paperId;
    /** 预付款价 */
    private BigDecimal advancePrice;
    /** 尾款 */
    private BigDecimal balancePayment;
    /** 定金支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date depositPaymentTime;
    /** 全额价格 */
    private BigDecimal totalPrice;
    /** 交付时间 */
    private Date deliveryTime;
    /** 是否申请退款 */
    private Integer applyRefund;
    /** 是否团购 */
    private Integer groupPurchase;
    /** 是否结清尾款 */
    private Integer settlement;
    /** 订单付款状态 */
    private Integer orderPayStatus;
    /** 微信支付商户订单号 */
    private String outTradeNo;
    /** 微信订单号 */
    private String transactionId;
    /** 尾款支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date balancePaymentTime;
    /** 客户 */
    private String custom;
    /** 论文标题 */
    private String title;
}
