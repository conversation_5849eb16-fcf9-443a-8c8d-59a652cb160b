import request from '@/utils/request'
import { getToken } from '@/utils/auth'

// 生成项目需求（流式响应）
export function generateProjectRequirements(projectName, categoryCode, signal) {
  const baseURL = process.env.VUE_APP_BASE_API
  const url = `${baseURL}/ai/agent/generate-requirements?projectName=${encodeURIComponent(projectName)}&categoryCode=${encodeURIComponent(categoryCode)}`
  
  const headers = {
    'Accept': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Content-Type': 'application/json'
  }
  
  // 添加认证token
  const token = getToken()
  if (token) {
    headers['Authorization'] = 'Bearer ' + token
  }
  
  return fetch(url, {
    method: 'POST',
    headers: headers,
    signal: signal
  })
}

// 生成项目需求（完整响应）
export function generateProjectRequirementsSync(projectName, categoryCode) {
  return request({
    url: '/ai/agent/generate-requirements-sync',
    method: 'post',
    params: {
      projectName,
      categoryCode
    }
  })
} 