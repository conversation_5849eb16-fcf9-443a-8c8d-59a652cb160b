package com.ruoyi.framework.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.NoHttpResponseException;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.UnsupportedSchemeException;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLException;
import java.io.InterruptedIOException;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.concurrent.TimeUnit;

@Configuration
@Slf4j
public class RestTemplateConfig {

    @Bean
    public ClientHttpRequestFactory apacheHttpRequestFactory() {
        // 重试配置
        HttpRequestRetryHandler retryHandler = (e, count, context) -> {
            log.info("连接失败次数|" + count);
            log.info("请求异常", e);

            if (count >= 5) {       // 假设已经重试了5次，就放弃
                return false;
            }
            // 返回true需要重试
            if (e instanceof NoHttpResponseException            // 请求无响应就重试
                    || e instanceof ConnectTimeoutException     // 连接超时
                    || e instanceof SocketException             // 连接异常
                    || e instanceof SocketTimeoutException      // socket超时
            ) {
                try {
                    // 重试时间间隔：1s
                    TimeUnit.MILLISECONDS.sleep(1000);
                } catch (InterruptedException ex) {
                    log.info("请求异常",  ex);
                }
                return true;
            }
            // 返回false不需要重试
            if (e instanceof SSLException                       // SSL握手异常不要重试
                    || e instanceof InterruptedIOException      // 中断
                    || e instanceof UnknownHostException        // 目标server不可达
                    || e instanceof UnsupportedSchemeException  // 协议不支持
            ) {
                return false;
            }

            HttpClientContext clientContext = HttpClientContext.adapt(context);
            HttpRequest request = clientContext.getRequest();
            // 假设请求是幂等的，就再次尝试
            return !(request instanceof HttpEntityEnclosingRequest);
        };

        Registry<ConnectionSocketFactory> registry = RegistryBuilder
                .<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", SSLConnectionSocketFactory.getSocketFactory())
                .build();

        // 连接池配置
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);
        // 最大连接数
        connManager.setMaxTotal(1000);
        // 每个路由最大连接数
        connManager.setDefaultMaxPerRoute(200);

        // 超时配置：都为5s
        RequestConfig reqConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(5000)      // 从连接池中获取连接超时时间
                .setConnectTimeout(5000)                // 连接建立超时时间，也就是三次握手完成时间
                .setSocketTimeout(5000)                 // 等待服务器响应超时时间
                .build();

        // 构建httpclient
        CloseableHttpClient client = HttpClients.custom()
                .setConnectionManager(connManager)
                .setDefaultRequestConfig(reqConfig)
                .setRetryHandler(retryHandler)
                .build();

        return new HttpComponentsClientHttpRequestFactory(client);
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate(apacheHttpRequestFactory());
    }
}
