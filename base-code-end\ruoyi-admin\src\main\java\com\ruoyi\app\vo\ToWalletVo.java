package com.ruoyi.app.vo;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 钱包对象 ToWalletVo
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
@Data
@ApiModel(value = " ToWalletVo", description = "钱包对象VO")
public class ToWalletVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 钱包id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 账户名称 */
    @Excel(name = "账户名称")
    @ApiModelProperty(value = "账户名称", name="accountName")
    private String accountName;

    /** 账户余额 */
    @Excel(name = "账户余额")
    @ApiModelProperty(value = "账户余额", name="balance")
    private BigDecimal balance;

    /** openid */
    @Excel(name = "openid")
    @ApiModelProperty(value = "openid", name="openId")
    private String openId;

}
