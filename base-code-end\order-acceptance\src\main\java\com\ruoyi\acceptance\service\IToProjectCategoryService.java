package com.ruoyi.acceptance.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.domain.ToProjectCategory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.vo.CategoryTreeVo;

/**
 * 项目类目信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
public interface IToProjectCategoryService extends IService<ToProjectCategory> {

    /**
     * 分页查询项目类目信息
     * @param toProjectCategory 项目类目信息
     * @param page 分页条件
     * @return 项目类目信息集合
     */
    IPage<ToProjectCategory> pages(ToProjectCategory toProjectCategory, IPage<ToProjectCategory> page);

    /**
     * 查询项目类目信息列表
     * 
     * @param toProjectCategory 项目类目信息
     * @return 项目类目信息集合
     */
     List<ToProjectCategory> selectList(ToProjectCategory toProjectCategory);

    ToProjectCategory selectOneByCateGoryCode(String categorySelectionOne);

    List<ToProjectCategory> selectByCateGoryCodes(List<String> categoryCodeList);

    List<CategoryTreeVo> getCategoryTree();
}
