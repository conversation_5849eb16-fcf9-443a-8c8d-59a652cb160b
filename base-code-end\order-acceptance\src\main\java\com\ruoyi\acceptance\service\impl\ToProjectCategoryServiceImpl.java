package com.ruoyi.acceptance.service.impl;

import java.util.*;

import cn.hutool.core.util.ObjUtil;
import com.ruoyi.acceptance.vo.CategoryTreeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.mapper.ToProjectCategoryMapper;
import com.ruoyi.acceptance.domain.ToProjectCategory;
import com.ruoyi.acceptance.service.IToProjectCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 项目类目信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Service
public class ToProjectCategoryServiceImpl extends ServiceImpl<ToProjectCategoryMapper,ToProjectCategory> implements IToProjectCategoryService {

    @Autowired
    private ToProjectCategoryMapper toProjectCategoryMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToProjectCategory> queryWrapper(ToProjectCategory toProjectCategory) {
        LambdaQueryWrapper<ToProjectCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toProjectCategory.getCategoryCode()), ToProjectCategory::getCategoryCode, toProjectCategory.getCategoryCode());
        queryWrapper.eq(ObjUtil.isNotEmpty(toProjectCategory.getParentCode()), ToProjectCategory::getParentCode, toProjectCategory.getParentCode());
        queryWrapper.eq(ObjUtil.isNotEmpty(toProjectCategory.getCategoryNo()), ToProjectCategory::getCategoryNo, toProjectCategory.getCategoryNo());
        queryWrapper.eq(ObjUtil.isNotEmpty(toProjectCategory.getAncestors()), ToProjectCategory::getAncestors, toProjectCategory.getAncestors());
        return queryWrapper;
    }

    /**
     * 查询项目类目信息分页
     *
     * @param toProjectCategory 项目类目信息
     * @return 项目类目信息
     */
    @Override
    public IPage<ToProjectCategory> pages(ToProjectCategory toProjectCategory, IPage<ToProjectCategory> page)
    {
        return toProjectCategoryMapper.selectPage(page, this.queryWrapper(toProjectCategory));
    }

    /**
     * 查询项目类目信息列表
     * 
     * @param toProjectCategory 项目类目信息
     * @return 项目类目信息
     */
    @Override
    public List<ToProjectCategory> selectList(ToProjectCategory toProjectCategory)
    {
        return toProjectCategoryMapper.selectList(this.queryWrapper(toProjectCategory));
    }

    @Override
    public ToProjectCategory selectOneByCateGoryCode(String categorySelectionCode) {
        LambdaQueryWrapper<ToProjectCategory> queryWrapper = new LambdaQueryWrapper<>() ;
        queryWrapper.eq(ToProjectCategory::getCategoryCode,categorySelectionCode);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<ToProjectCategory> selectByCateGoryCodes(List<String> categoryCodeList) {
        LambdaQueryWrapper<ToProjectCategory> queryWrapper = new LambdaQueryWrapper<>() ;
        queryWrapper.in(ToProjectCategory::getCategoryCode,categoryCodeList);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<CategoryTreeVo> getCategoryTree() {
        // 获取所有分类
        List<ToProjectCategory> allCategories = baseMapper.selectList(null);

        // 转换为树结构
        List<CategoryTreeVo> rootCategories = new ArrayList<>();
        Map<String, CategoryTreeVo> categoryMap = new HashMap<>();

        // 第一次遍历，创建所有节点
        for (ToProjectCategory category : allCategories) {
            CategoryTreeVo vo = new CategoryTreeVo();
            vo.setCode(category.getCategoryCode());
            vo.setName(category.getCategoryName());
            vo.setSort(category.getCategoryNo());
            vo.setFullPath(category.getAncestors());
            categoryMap.put(category.getCategoryCode(), vo);
        }

        // 第二次遍历，构建树结构
        for (ToProjectCategory category : allCategories) {
            if (category.getParentCode() == null || category.getParentCode().isEmpty()) {
                // 根节点
                rootCategories.add(categoryMap.get(category.getCategoryCode()));
            } else {
                // 子节点，添加到父节点的children列表
                //拿到父节点的对象
                CategoryTreeVo parentVo = categoryMap.get(category.getParentCode());
                if (parentVo != null) {
                    parentVo.getChildren().add(categoryMap.get(category.getCategoryCode()));
                }
            }
        }

        // 对每层节点按排序号排序
        sortCategoryTree(rootCategories);

        return rootCategories;
    }

    private void sortCategoryTree(List<CategoryTreeVo> categories) {
        if (categories == null || categories.isEmpty()) {
            return;
        }

        // 按排序号排序
        categories.sort(Comparator.comparing(CategoryTreeVo::getSort));

        // 递归排序子节点
        for (CategoryTreeVo category : categories) {
            sortCategoryTree(category.getChildren());
        }
    }

}
