package com.ruoyi.system.dto.convert;

import com.ruoyi.system.domain.CommonPrompt;
import com.ruoyi.system.dto.CommonPromptDTO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:33+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CommonPromptDTOConvertImpl implements CommonPromptDTOConvert {

    @Override
    public CommonPrompt dtoToPo(CommonPromptDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommonPrompt commonPrompt = new CommonPrompt();

        commonPrompt.setAiType( arg0.getAiType() );
        commonPrompt.setContent( arg0.getContent() );
        commonPrompt.setEnableSearch( arg0.getEnableSearch() );
        commonPrompt.setEnableThinking( arg0.getEnableThinking() );
        commonPrompt.setIsActive( arg0.getIsActive() );
        commonPrompt.setPromptId( arg0.getPromptId() );
        commonPrompt.setPromptTitle( arg0.getPromptTitle() );
        commonPrompt.setSystemPrompt( arg0.getSystemPrompt() );

        return commonPrompt;
    }

    @Override
    public List<CommonPrompt> dtoToPoList(List<CommonPromptDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<CommonPrompt> list = new ArrayList<CommonPrompt>( arg0.size() );
        for ( CommonPromptDTO commonPromptDTO : arg0 ) {
            list.add( dtoToPo( commonPromptDTO ) );
        }

        return list;
    }

    @Override
    public CommonPromptDTO poToDto(CommonPrompt arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommonPromptDTO commonPromptDTO = new CommonPromptDTO();

        commonPromptDTO.setAiType( arg0.getAiType() );
        commonPromptDTO.setContent( arg0.getContent() );
        commonPromptDTO.setEnableSearch( arg0.getEnableSearch() );
        commonPromptDTO.setEnableThinking( arg0.getEnableThinking() );
        commonPromptDTO.setIsActive( arg0.getIsActive() );
        commonPromptDTO.setPromptId( arg0.getPromptId() );
        commonPromptDTO.setPromptTitle( arg0.getPromptTitle() );
        commonPromptDTO.setSystemPrompt( arg0.getSystemPrompt() );

        return commonPromptDTO;
    }

    @Override
    public List<CommonPromptDTO> poToDtoList(List<CommonPrompt> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<CommonPromptDTO> list = new ArrayList<CommonPromptDTO>( arg0.size() );
        for ( CommonPrompt commonPrompt : arg0 ) {
            list.add( poToDto( commonPrompt ) );
        }

        return list;
    }

    @Override
    public CommonPromptDTO poToDto(CommonPrompt commonPrompt, List<Long> projectTypes) {
        if ( commonPrompt == null && projectTypes == null ) {
            return null;
        }

        CommonPromptDTO commonPromptDTO = new CommonPromptDTO();

        if ( commonPrompt != null ) {
            commonPromptDTO.setAiType( commonPrompt.getAiType() );
            commonPromptDTO.setContent( commonPrompt.getContent() );
            commonPromptDTO.setEnableSearch( commonPrompt.getEnableSearch() );
            commonPromptDTO.setEnableThinking( commonPrompt.getEnableThinking() );
            commonPromptDTO.setIsActive( commonPrompt.getIsActive() );
            commonPromptDTO.setPromptId( commonPrompt.getPromptId() );
            commonPromptDTO.setPromptTitle( commonPrompt.getPromptTitle() );
            commonPromptDTO.setSystemPrompt( commonPrompt.getSystemPrompt() );
        }
        List<Long> list = projectTypes;
        if ( list != null ) {
            commonPromptDTO.setCommonPromptProjectTypes( new ArrayList<Long>( list ) );
        }

        return commonPromptDTO;
    }
}
