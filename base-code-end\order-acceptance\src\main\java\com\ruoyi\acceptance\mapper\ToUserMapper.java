package com.ruoyi.acceptance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.ToUserVO;
import com.ruoyi.common.core.domain.vo.TransactionUserVO;
import com.ruoyi.common.core.domain.entity.ToUser;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
public interface ToUserMapper extends BaseMapper<ToUser>
{
    /**
     * 查询用户信息
     * 
     * @param id 用户信息主键
     * @return 用户信息
     */
    public ToUser selectToUserById(Long id);
    public ToUserVO selectToUserByOpenId(String openId);
    public ToUser selectToUserByOpenId1(String openId);

    /**
     * 查询用户信息列表
     * 
     * @param toUser 用户信息
     * @return 用户信息集合
     */
    public List<ToUser> selectToUserList(ToUser toUser);

    public List<ToUser> inviteCustomerList(String openId);

    /**
     * 新增用户信息
     * 
     * @param toUser 用户信息
     * @return 结果
     */
    public int insertToUser(ToUser toUser);

    /**
     * 修改用户信息
     * 
     * @param toUser 用户信息
     * @return 结果
     */
    public R<?> updateToUser(ToUser toUser);

    public int updateToUserByOpenId(ToUser toUser);

    /**
     * 删除用户信息
     * 
     * @param id 用户信息主键
     * @return 结果
     */
    public int deleteToUserById(Integer id);

    /**
     * 批量删除用户信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteToUserByIds(Integer[] ids);

    List<TransactionUserVO> transactionCustomerList(String openId);

    List<ToUser> getUserList(String phoneNumber);

    List<String> getHeadsForGroupInfo(String groupId);

    /**
     * 根据用户openId获取当前用户邀请其他用户的数量
     * @param openId 用户openId
     * @return 邀请人数
     */
    BigDecimal getInviteUserCountByOpenId(@Param("openId")String openId);

    ToUser selectShareUserByInvitedUserOpenId(@Param("openId")String openId);
}
