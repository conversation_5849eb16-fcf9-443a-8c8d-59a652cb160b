package com.ruoyi.dispatch.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 消息对象 chat_messages
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@TableName("chat_messages")
public class ChatMessages extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 消息ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long messageId;

    /** 会话ID */
    @TableField(value = "conversation_id")
    private Long conversationId;

    /** 发送者ID */
    @TableField(value = "sender_id")
    private String senderId;

    /** 内容类型(1-文本,2-图片,3-视频,4-文件,5-语音) */
    @TableField(value = "content_type")
    private Integer contentType;

    /** 消息内容 */
    @TableField(value = "content")
    private String content;

    /** 文件URL */
    @TableField(value = "file_url")
    private String fileUrl;

    /** 文件大小(字节) */
    @TableField(value = "file_size")
    private Long fileSize;

    /** 是否撤回(0-否,1-是) */
    @TableField(value = "is_recalled")
    private Integer isRecalled;

}
