package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.${ClassName}Vo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import ${packageName}.domain.${ClassName};
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * ${functionName}PoVo转换器
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ${ClassName}Convert extends BeanPoVoMapper<${ClassName}, ${ClassName}Vo> {

        ${ClassName}Convert INSTANCE = Mappers.getMapper(${ClassName}Convert.class);

}
