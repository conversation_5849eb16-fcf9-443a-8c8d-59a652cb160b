package com.ruoyi.api.controller;


import com.ruoyi.acceptance.service.WxPayService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Api(tags = "微信支付控制器")
@RestController
@RequestMapping("/pay")
@Scope("prototype")
public class WxPayController {
    @Autowired
    private WxPayService wxPayService;

    /**
     * 微信支付
     * @return
     */
    @ApiOperation("支付接口")
    @GetMapping("/wx/withdrawal/{amount}")
    @ResponseBody
    public R withdrawal(@PathVariable Integer amount) {
        return wxPayService.withdrawalToSmallChange(amount);
    }

    /**
     * 微信支付
     * @return
     */
    @ApiOperation("支付接口")
    @GetMapping("/wx/jsApiPay/{orderId}")
    @ResponseBody
    public R jsApiPay(@PathVariable String orderId) {
        return wxPayService.jsApiPay(orderId);
    }

    /**
     * 微信支付回调
     * @param request
     * @param response
     */
    @ApiOperation("支付回调接口")
    @RequestMapping(value = "/payNotify", method = {org.springframework.web.bind.annotation.RequestMethod.POST, org.springframework.web.bind.annotation.RequestMethod.GET})
    public void payNotify(HttpServletRequest request, HttpServletResponse response) {
         wxPayService.payNotify(request,response);
    }

    /**
     * 微信退款
     * 2个参数只传1个即可
     * transactionId: 原支付交易对应的微信订单号
     * outTradeNo: 原支付交易对应的商户订单号
     */
    @ApiOperation("退款接口")
    @GetMapping(value = "/wx/payRefund/{orderId}")
    public R payRefund(@PathVariable String orderId) {
        return wxPayService.payRefund(orderId);
    }

    @RequestMapping("/get")
    @ResponseBody
    public String v3Get() {
        return wxPayService.v3Get();
    }
}
