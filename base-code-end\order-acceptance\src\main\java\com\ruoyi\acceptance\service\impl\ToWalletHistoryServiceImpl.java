package com.ruoyi.acceptance.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import com.ruoyi.acceptance.domain.ToWalletHistory;
import com.ruoyi.acceptance.dto.WithdrawalListDTO;
import com.ruoyi.acceptance.mapper.ToWalletHistoryMapper;
import com.ruoyi.acceptance.service.IToWalletHistoryService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 钱包流水Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class ToWalletHistoryServiceImpl extends ServiceImpl<ToWalletHistoryMapper, ToWalletHistory> implements IToWalletHistoryService {

    @Autowired
    private ToWalletHistoryMapper toWalletHistoryMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToWalletHistory> queryWrapper(ToWalletHistory toWalletHistory) {
        LambdaQueryWrapper<ToWalletHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toWalletHistory.getOpenId()), ToWalletHistory::getOpenId, toWalletHistory.getOpenId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toWalletHistory.getFrozenAmount()), ToWalletHistory::getFrozenAmount, toWalletHistory.getFrozenAmount());
        queryWrapper.eq(ObjUtil.isNotEmpty(toWalletHistory.getNotWithdrawAmount()), ToWalletHistory::getNotWithdrawAmount, toWalletHistory.getNotWithdrawAmount());
        queryWrapper.eq(ObjUtil.isNotEmpty(toWalletHistory.getWithdrawnAmount()), ToWalletHistory::getWithdrawnAmount, toWalletHistory.getWithdrawnAmount());
        queryWrapper.eq(ObjUtil.isNotEmpty(toWalletHistory.getTotalAmount()), ToWalletHistory::getTotalAmount, toWalletHistory.getTotalAmount());
        return queryWrapper;
    }

    /**
     * 查询钱包流水分页
     *
     * @param toWalletHistory 钱包流水
     * @return 钱包流水
     */
    @Override
    public IPage<ToWalletHistory> pages(ToWalletHistory toWalletHistory, IPage<ToWalletHistory> page)
    {
        return toWalletHistoryMapper.selectPage(page, this.queryWrapper(toWalletHistory));
    }

    /**
     * 查询钱包流水列表
     * 
     * @param toWalletHistory 钱包流水
     * @return 钱包流水
     */
    @Override
    public List<ToWalletHistory> selectList(ToWalletHistory toWalletHistory)
    {
        return toWalletHistoryMapper.selectList(this.queryWrapper(toWalletHistory));
    }

    @Override
    public R<?> withdrawalList(String yearMonth) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        List<WithdrawalListDTO > toWalletHistories = toWalletHistoryMapper.withdrawalList(openId,yearMonth);
        return R.ok(toWalletHistories);
    }

}
