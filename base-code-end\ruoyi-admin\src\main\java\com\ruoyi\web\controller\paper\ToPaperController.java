package com.ruoyi.web.controller.paper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.app.manager.ToPaperManager;
import com.ruoyi.acceptance.vo.ToPaperVo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 论文管理Controller
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
@Api(tags = "论文管理管理")
@RestController
@RequestMapping("/acceptance/paper")
public class ToPaperController {

    @Autowired
    private ToPaperManager toPaperManager;

    /**
     * 分页查询论文管理
     */
    @ApiOperation("分页查询论文管理")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('acceptance:paper:list')")
    @GetMapping("/list")
    public R<IPage<ToPaperVo>> list(ToPaperVo toPaperVo, Query query)
    {
//        return R.ok(toPaperManager.page(toPaperVo, query));
        return R.ok(toPaperManager.selectToPapers(toPaperVo, query));
    }

    /**
    * 查询论文管理全部列表
    */
    @ApiOperation("查询论文管理全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('acceptance:paper:list')")
    @GetMapping("/allList")
    public R<List<ToPaperVo>> allList(ToPaperVo toPaperVo){
        return R.ok(toPaperManager.list(toPaperVo));
    }

    /**
     * 导出论文管理列表
     */
    @ApiOperation("导出论文管理列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('acceptance:paper:export')")
    @Log(title = "论文管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToPaperVo toPaperVo, Query query)
    {
        List<ToPaperVo> list = toPaperManager.page(toPaperVo, query).getRecords();
        ExcelUtil<ToPaperVo> util = new ExcelUtil<ToPaperVo>(ToPaperVo.class);
        util.exportExcel(response, list, "论文管理数据");
    }

    /**
     * 获取论文管理详细信息
     */
    @ApiOperation("获取论文管理详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:paper:query')")
    @GetMapping(value = "/{id}")
    public R<ToPaperVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toPaperManager.getInfoForPc(id));
    }

    /**
     * 新增论文管理
     */
    @ApiOperation("新增论文管理")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('acceptance:paper:add')")
    @Log(title = "论文管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToPaperVo toPaperVo)
    {
        return toPaperManager.savePaperFromWeb(toPaperVo);
    }

    /**
     * 修改论文管理
     */
    @ApiOperation("修改论文管理")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('acceptance:paper:edit')")
    @Log(title = "论文管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToPaperVo toPaperVo)
    {
        return toPaperManager.editForWeb(toPaperVo);
    }

    /**
     * 删除论文管理
     */
    @ApiOperation("删除论文管理")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:paper:remove')")
    @Log(title = "论文管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}/{orderIds}")
    public R<?> remove(@PathVariable Long[] ids,@PathVariable Long[] orderIds)
    {
        return toPaperManager.removeForWeb(ids,orderIds);
    }
}