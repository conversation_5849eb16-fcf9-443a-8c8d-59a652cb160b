package com.ruoyi.web.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import com.ruoyi.system.dto.CommonPromptDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.system.service.ICommonPromptService;
import com.ruoyi.web.vo.CommonPromptVo;
import com.ruoyi.web.vo.convert.CommonPromptConvert;

import java.util.List;

/**
 * 提示词Manager
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Component
public class CommonPromptManager {

    @Autowired
    private ICommonPromptService commonPromptService;

    @Autowired
    private CommonPromptConvert commonPromptConvert;

    /**
    * 分页查询提示词
    */
    public IPage<CommonPromptVo> page(CommonPromptVo commonPromptVo, Query query){
        IPage<CommonPromptDTO> page = commonPromptService.pages(commonPromptConvert.voToDto(commonPromptVo), Condition.getPage(query));
        return (IPage<CommonPromptVo>) IPageConvert.instance().IPageDtoToIPageVo(page, new Page<CommonPromptVo>(), CommonPromptConvert.INSTANCE);
    }

    /**
    * 查询提示词列表
    */
    public List<CommonPromptVo> list(CommonPromptVo commonPromptVo) {
        List<CommonPromptDTO> commonPromptList = commonPromptService.selectList(commonPromptConvert.voToDto(commonPromptVo));
        return commonPromptConvert.dtoToVoList(commonPromptList);
    }

    /**
     * 查询提示词详细信息
     */
    public CommonPromptVo getInfo(Long promptId) {
        CommonPromptDTO commonPromptDTO = commonPromptService.getById(promptId);
        return commonPromptConvert.dtoToVo(commonPromptDTO);
    }

    /**
     * 新增提示词
     */
    public boolean add(CommonPromptVo commonPromptVo) {
        return commonPromptService.save(commonPromptConvert.voToDto(commonPromptVo));
    }

    /**
     * 修改提示词
     */
    public boolean edit(CommonPromptVo commonPromptVo) {
        return commonPromptService.updateById(commonPromptConvert.voToDto(commonPromptVo));
    }

    /**
     * 批量删除提示词
     */
    public boolean remove(Long[] promptIds) {
        return commonPromptService.removeByIds(CollUtil.toList(promptIds));
    }

}
