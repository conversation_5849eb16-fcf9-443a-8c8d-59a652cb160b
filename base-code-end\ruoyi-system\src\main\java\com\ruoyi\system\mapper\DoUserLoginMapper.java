package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.DoUserLogin;

import java.util.List;

/**
 * 派单小程序用户登录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
public interface DoUserLoginMapper 
{

    /**
     * 新增微信登录用户
     *
     * @param doUserLogin 用户信息
     */
    void save(DoUserLogin doUserLogin);

    /**
     * 更新微信登录用户
     *
     * @param doUserLogin 用户信息
     */
    void updateById(DoUserLogin doUserLogin);

    /**
     * 获取微信登录用户
     *
     * @param openid 用户标识
     * @return 用户信息
     */
    DoUserLogin getById(String openid);
    /**
     * 查询派单小程序用户登录
     * 
     * @param openid 派单小程序用户登录主键
     * @return 派单小程序用户登录
     */
    public DoUserLogin selectDoUserLoginByOpenid(String openid);

    /**
     * 查询派单小程序用户登录列表
     * 
     * @param doUserLogin 派单小程序用户登录
     * @return 派单小程序用户登录集合
     */
    public List<DoUserLogin> selectDoUserLoginList(DoUserLogin doUserLogin);

    /**
     * 新增派单小程序用户登录
     * 
     * @param doUserLogin 派单小程序用户登录
     * @return 结果
     */
    public int insertDoUserLogin(DoUserLogin doUserLogin);

    /**
     * 修改派单小程序用户登录
     * 
     * @param doUserLogin 派单小程序用户登录
     * @return 结果
     */
    public int updateDoUserLogin(DoUserLogin doUserLogin);

    /**
     * 删除派单小程序用户登录
     * 
     * @param openid 派单小程序用户登录主键
     * @return 结果
     */
    public int deleteDoUserLoginByOpenid(String openid);

    /**
     * 批量删除派单小程序用户登录
     * 
     * @param openids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDoUserLoginByOpenids(String[] openids);
}
