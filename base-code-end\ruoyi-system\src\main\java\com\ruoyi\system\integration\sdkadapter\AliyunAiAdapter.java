package com.ruoyi.system.integration.sdkadapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.utils.JsonUtils;
import io.reactivex.Flowable;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.system.dto.CommonPromptDTO;

import java.util.List;
import java.util.ArrayList;

/**
 * 阿里云AI适配器
 * 封装所有AI调用相关的具体实现方法
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
public class AliyunAiAdapter {

    private static final Logger logger = LoggerFactory.getLogger(AliyunAiAdapter.class);

    // API密钥，建议从配置文件读取
    private static final String API_KEY = "sk-0bef520215f94ba9badb5ce186c74c51";
    
    /**
     * 流式调用AI接口（使用系统消息和用户消息，通过DTO传递参数）
     *
     * @param systemPrompt 系统提示词
     * @param userPrompt   用户提示词
     * @param promptDTO    提示词配置DTO
     * @return 流式响应
     * @throws NoApiKeyException      API密钥异常
     * @throws ApiException           API调用异常
     * @throws InputRequiredException 输入参数异常
     */
    public static Flowable<String> streamCallWithMessages(String systemPrompt, String userPrompt,
                                                       CommonPromptDTO promptDTO)
            throws NoApiKeyException, ApiException, InputRequiredException {

        List<Message> messages = buildMessages(systemPrompt, userPrompt);
        return streamCallWithMessages(messages, promptDTO);
    }

    /**
     * 同步调用AI接口（使用系统消息和用户消息，通过DTO传递参数）
     *
     * @param systemPrompt 系统提示词
     * @param userPrompt   用户提示词
     * @param promptDTO    提示词配置DTO
     * @return 完整响应内容
     * @throws NoApiKeyException      API密钥异常
     * @throws ApiException           API调用异常
     * @throws InputRequiredException 输入参数异常
     */
    public static String syncCallWithMessages(String systemPrompt, String userPrompt,
                                           CommonPromptDTO promptDTO)
            throws NoApiKeyException, ApiException, InputRequiredException {

        List<Message> messages = buildMessages(systemPrompt, userPrompt);
        return syncCallWithMessages(messages, promptDTO);
    }


    /**
     * 流式调用AI接口（使用消息列表，通过DTO传递参数）
     *
     * @param messages 消息列表
     * @param promptDTO 提示词配置DTO
     * @return 流式响应
     * @throws NoApiKeyException      API密钥异常
     * @throws ApiException           API调用异常
     * @throws InputRequiredException 输入参数异常
     */
    public static Flowable<String> streamCallWithMessages(List<Message> messages, CommonPromptDTO promptDTO)
            throws NoApiKeyException, ApiException, InputRequiredException {

        Generation gen = new Generation();
        GenerationParam param = buildGenerationParamWithMessages(messages, promptDTO);
        Flowable<GenerationResult> result = gen.streamCall(param);

        return result.map(AliyunAiAdapter::extractContent)
                .filter(content -> content != null);
    }

    /**
     * 同步调用AI接口（使用消息列表，通过DTO传递参数）
     *
     * @param messages 消息列表
     * @param promptDTO 提示词配置DTO
     * @return 完整响应内容
     * @throws NoApiKeyException      API密钥异常
     * @throws ApiException           API调用异常
     * @throws InputRequiredException 输入参数异常
     */
    public static String syncCallWithMessages(List<Message> messages, CommonPromptDTO promptDTO)
            throws NoApiKeyException, ApiException, InputRequiredException {

        StringBuilder result = new StringBuilder();

        streamCallWithMessages(messages, promptDTO)
                .blockingForEach(result::append);

        return result.toString();
    }

    /**
     * 处理生成结果
     *
     * @param message 生成结果消息
     * @return 提取的内容
     */
    private static String extractContent(GenerationResult message) {
        try {
            // 解析返回的JSON字符串
            JSONObject jsonObject = JSON.parseObject(JsonUtils.toJson(message));

            // 提取content字段内容
            String content = jsonObject.getJSONObject("output")
                    .getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message")
                    .getString("content");

            return content;
        } catch (Exception e) {
            logger.error("解析AI响应内容失败", e);
            return null;
        }
    }

    /**
     * 构建用户消息
     *
     * @param content 消息内容
     * @return 用户消息
     */
    public static Message buildUserMessage(String content) {
        return Message.builder()
                .role(Role.USER.getValue())
                .content(content)
                .build();
    }

    /**
     * 构建系统消息
     *
     * @param content 消息内容
     * @return 系统消息
     */
    public static Message buildSystemMessage(String content) {
        return Message.builder()
                .role(Role.SYSTEM.getValue())
                .content(content)
                .build();
    }

    /**
     * 构建消息列表
     *
     * @param systemPrompt 系统提示词
     * @param userPrompt   用户提示词
     * @return 消息列表
     */
    private static List<Message> buildMessages(String systemPrompt, String userPrompt) {
        List<Message> messages = new ArrayList<>();

        // 添加系统消息（如果存在）
        if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
            messages.add(buildSystemMessage(systemPrompt));
        }

        // 添加用户消息
        if (userPrompt != null && !userPrompt.trim().isEmpty()) {
            messages.add(buildUserMessage(userPrompt));
        }

        return messages;
    }

    /**
     * 构建生成参数（使用消息列表，通过DTO传递参数）
     *
     * @param messages 消息列表
     * @param promptDTO 提示词配置DTO
     * @return 生成参数
     */
    private static GenerationParam buildGenerationParamWithMessages(List<Message> messages, CommonPromptDTO promptDTO) {
        return GenerationParam.builder()
                .apiKey(API_KEY)
                .model(promptDTO.getAiType())
                .enableSearch(promptDTO.getEnableSearch() != null && promptDTO.getEnableSearch() == 1)
                .enableThinking(promptDTO.getEnableThinking() != null && promptDTO.getEnableThinking() == 1)
                .messages(messages)
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .incrementalOutput(true)
                .build();
    }
}