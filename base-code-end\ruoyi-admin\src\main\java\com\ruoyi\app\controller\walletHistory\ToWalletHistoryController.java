package com.ruoyi.app.controller.walletHistory;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ToWalletHistoryVo;
import com.ruoyi.app.manager.ToWalletHistoryManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 钱包流水Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Api(tags = "钱包流水管理")
@RestController
@RequestMapping("/acceptance/walletHistory")
public class ToWalletHistoryController {

    @Autowired
    private ToWalletHistoryManager toWalletHistoryManager;

    /**
     * 分页查询钱包流水
     */
    @ApiOperation("分页查询钱包流水")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('acceptance:history:list')")
    @GetMapping("/list")
    public R<IPage<ToWalletHistoryVo>> list(ToWalletHistoryVo toWalletHistoryVo, Query query)
    {
        return R.ok(toWalletHistoryManager.page(toWalletHistoryVo, query));
    }

    /**
    * 查询钱包流水全部列表
    */
    @ApiOperation("查询钱包流水全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('acceptance:history:list')")
    @GetMapping("/allList")
    public R<List<ToWalletHistoryVo>> allList(ToWalletHistoryVo toWalletHistoryVo){
        return R.ok(toWalletHistoryManager.list(toWalletHistoryVo));
    }

    /**
     * 导出钱包流水列表
     */
    @ApiOperation("导出钱包流水列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('acceptance:history:export')")
    @Log(title = "钱包流水", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToWalletHistoryVo toWalletHistoryVo, Query query)
    {
        List<ToWalletHistoryVo> list = toWalletHistoryManager.page(toWalletHistoryVo, query).getRecords();
        ExcelUtil<ToWalletHistoryVo> util = new ExcelUtil<ToWalletHistoryVo>(ToWalletHistoryVo.class);
        util.exportExcel(response, list, "钱包流水数据");
    }

    /**
     * 获取钱包流水详细信息
     */
    @ApiOperation("获取钱包流水详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:history:query')")
    @GetMapping(value = "/{id}")
    public R<ToWalletHistoryVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toWalletHistoryManager.getInfo(id));
    }

    /**
     * 新增钱包流水
     */
    @ApiOperation("新增钱包流水")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('acceptance:history:add')")
    @Log(title = "钱包流水", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToWalletHistoryVo toWalletHistoryVo)
    {
        return toWalletHistoryManager.add(toWalletHistoryVo) ? R.ok() : R.fail();
    }

    /**
     * 修改钱包流水
     */
    @ApiOperation("修改钱包流水")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('acceptance:history:edit')")
    @Log(title = "钱包流水", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToWalletHistoryVo toWalletHistoryVo)
    {
        return toWalletHistoryManager.edit(toWalletHistoryVo) ? R.ok() : R.fail();
    }

    /**
     * 删除钱包流水
     */
    @ApiOperation("删除钱包流水")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:history:remove')")
    @Log(title = "钱包流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toWalletHistoryManager.remove(ids) ? R.ok() : R.fail();
    }

    /**
     * 提现列表
     */
    @ApiOperation("提现列表")
    @GetMapping("/withdrawalList/{yearMonth}")
    public R<?> withdrawalList(@PathVariable("yearMonth") String yearMonth){
        return toWalletHistoryManager.withdrawalList(yearMonth);
    }

}
