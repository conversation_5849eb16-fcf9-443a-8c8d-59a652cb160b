package com.ruoyi.dispatchorder.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 派单对象 DoDispatchOrderVo
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
@Data
@ApiModel(value = " DoDispatchOrderVo", description = "派单对象VO")
public class DoDispatchOrderVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 派单id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 论文id */
    @Excel(name = "项目id")
    @ApiModelProperty(value = "项目id", name="projectId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectId;

    /** 派单人id */
    @Excel(name = "接单商户id")
    @ApiModelProperty(value = "派单人id", name="takerId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long takerId;

    /** 派单状态（1接单开始2接单开发完成3管理员确认订单完成） */
    @Excel(name = "派单状态", readConverterExp = "1=接单开始2接单开发完成3管理员确认订单完成")
    @ApiModelProperty(value = "派单状态", name="status")
    private Integer status;

}
