package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatMessageStatusVo;
import com.ruoyi.dispatch.domain.ChatMessageStatus;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatMessageStatusConvertImpl implements ChatMessageStatusConvert {

    @Override
    public ChatMessageStatusVo poToVo(ChatMessageStatus arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatMessageStatusVo chatMessageStatusVo = new ChatMessageStatusVo();

        chatMessageStatusVo.setId( arg0.getId() );
        chatMessageStatusVo.setIsRead( arg0.getIsRead() );
        chatMessageStatusVo.setMessageId( arg0.getMessageId() );
        chatMessageStatusVo.setReadTime( arg0.getReadTime() );
        chatMessageStatusVo.setUserId( arg0.getUserId() );

        return chatMessageStatusVo;
    }

    @Override
    public List<ChatMessageStatusVo> poToVoList(List<ChatMessageStatus> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatMessageStatusVo> list = new ArrayList<ChatMessageStatusVo>( arg0.size() );
        for ( ChatMessageStatus chatMessageStatus : arg0 ) {
            list.add( poToVo( chatMessageStatus ) );
        }

        return list;
    }

    @Override
    public ChatMessageStatus voToPo(ChatMessageStatusVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatMessageStatus chatMessageStatus = new ChatMessageStatus();

        chatMessageStatus.setId( arg0.getId() );
        chatMessageStatus.setIsRead( arg0.getIsRead() );
        chatMessageStatus.setMessageId( arg0.getMessageId() );
        chatMessageStatus.setReadTime( arg0.getReadTime() );
        chatMessageStatus.setUserId( arg0.getUserId() );

        return chatMessageStatus;
    }

    @Override
    public List<ChatMessageStatus> voToPoList(List<ChatMessageStatusVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatMessageStatus> list = new ArrayList<ChatMessageStatus>( arg0.size() );
        for ( ChatMessageStatusVo chatMessageStatusVo : arg0 ) {
            list.add( voToPo( chatMessageStatusVo ) );
        }

        return list;
    }
}
