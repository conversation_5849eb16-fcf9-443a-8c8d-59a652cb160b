package com.ruoyi.framework.security.filter;

import com.ruoyi.common.config.TokenConfig;
import com.ruoyi.common.core.domain.model.DispatchLoginUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * token过滤器 验证token有效性
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    @Autowired
    private TokenService tokenService;

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        // 排除 WebSocket 握手请求（HTTP 协议路径）
        return request.getRequestURI().equals("/ws")
                || request.getHeader("Upgrade") != null
                && request.getHeader("Upgrade").equalsIgnoreCase("websocket");
    }


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        String string = request.getRequestURI().toString();
        log.info("URL:",string);
        //请求数据来源
        String sourceType = request.getHeader(TokenConfig.SOURCE);
        //微信请求入口
        if (TokenConfig.DISPATCH_APPLET.equals(sourceType)){
            DispatchLoginUser dispatchLoginUser = tokenService.getDispatchLoginUser(request);
            if (StringUtils.isNotNull(dispatchLoginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
                tokenService.verifyDispatchToken(dispatchLoginUser);
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(dispatchLoginUser, sourceType, null);
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
            chain.doFilter(request, response);
        } else if (TokenConfig.WX_APPLET.equals(sourceType)) {
            WxLoginUser wxLoginUser = tokenService.getWxLoginUser(request);
            if (StringUtils.isNotNull(wxLoginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
                tokenService.verifyWxToken(wxLoginUser);
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(wxLoginUser, sourceType, null);
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
            chain.doFilter(request, response);
        } else {
            //web端入口
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (StringUtils.isNotNull(loginUser) && StringUtils.isNull(SecurityUtils.getAuthentication())) {
                tokenService.verifyToken(loginUser);
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
            chain.doFilter(request, response);
        }
    }
}
