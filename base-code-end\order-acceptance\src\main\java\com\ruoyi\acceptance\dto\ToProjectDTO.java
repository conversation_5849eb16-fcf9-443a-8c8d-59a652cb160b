package com.ruoyi.acceptance.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ToProjectDTO {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 订单id */
    private Long orderId;

    /** 标题 */
    private String title;

    /** 选择类目，1级*/
    private String categorySelectionOne;

    /** 选择类目，按逗号分隔 */
    private String categorySelectionTwo;

    /** 需求描述 */
    private String requirementDescription;

    /** 追加服务 */
    private String additionalServices;

    /** 团购成员（用户信息表id关联） */
    private String groupMembers;

    /** 客户openId */
    private String customerOpenid;

    /** 商户openId */
    private String merchantOpenid;

    /** 是否非卖品（1为是0为否） */
    private Integer isNotForSale;

    /** 开始时间，双方确认交易时间 */
    private Date startTime;

    /** 期望交付时间 */
    private Date expectedDeliveryTime;

    /** 实际交付时间，项目完成时间 */
    private Date actualDeliveryTime;

    /** 项目结束时间 */
    private Date projectEndTime;

    /** 附件 */
    private String attachments;


    private BigDecimal totalPrice;

    private Integer orderPayStatus;
}
