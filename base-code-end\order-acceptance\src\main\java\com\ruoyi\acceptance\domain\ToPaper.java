package com.ruoyi.acceptance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.PoBaseEntity;
import lombok.Data;

/**
 * 论文管理对象 to_paper
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
@Data
@TableName("to_paper")
public class ToPaper extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 订单id */
    @TableField(value = "order_id")
    private Long orderId;

    /** 标题 */
    @TableField(value = "title")
    private String title;

    /** 技术选型 */
    @TableField(value = "technical_selection")
    private String technicalSelection;

    /** 包含服务 */
    @TableField(value = "including_services")
    private String includingServices;

    /** 简介 */
    @TableField(value = "introduction")
    private String introduction;

    /** 团购成员（用户信息表id关联） */
    @TableField(value = "buy_members")
    private String buyMembers;

    @TableField(value = "custom")
    private String custom;
}
