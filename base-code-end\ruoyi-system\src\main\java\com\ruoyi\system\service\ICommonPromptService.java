package com.ruoyi.system.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.system.domain.CommonPrompt;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.dto.CommonPromptDTO;

/**
 * 提示词Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ICommonPromptService extends IService<CommonPrompt> {

    /**
     * 分页查询提示词
     * @param commonPromptDTO 提示词
     * @param page 分页条件
     * @return 提示词集合
     */
    IPage<CommonPromptDTO> pages(CommonPromptDTO commonPromptDTO, IPage<CommonPrompt> page);

    /**
     * 查询提示词列表
     * 
     * @param commonPromptDTO 提示词
     * @return 提示词集合
     */
     List<CommonPromptDTO> selectList(CommonPromptDTO commonPromptDTO);
     
    /**
     * 根据ID获取提示词信息
     *
     * @param promptId 提示词ID
     * @return 提示词信息
     */
    CommonPromptDTO getById(Long promptId);
    
    /**
     * 保存提示词信息
     *
     * @param commonPromptDTO 提示词信息
     * @return 结果
     */
    boolean save(CommonPromptDTO commonPromptDTO);
    
    /**
     * 更新提示词信息
     *
     * @param commonPromptDTO 提示词信息
     * @return 结果
     */
    boolean updateById(CommonPromptDTO commonPromptDTO);
    
    /**
     * 批量删除提示词
     *
     * @param ids 需要删除的提示词ID集合
     * @return 结果
     */
    boolean removeByIds(List<Long> ids);
}
