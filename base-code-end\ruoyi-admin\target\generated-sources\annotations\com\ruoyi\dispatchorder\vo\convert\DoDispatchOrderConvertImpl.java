package com.ruoyi.dispatchorder.vo.convert;

import com.ruoyi.dispatch.domain.DoDispatchOrder;
import com.ruoyi.dispatchorder.vo.DoDispatchOrderVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DoDispatchOrderConvertImpl implements DoDispatchOrderConvert {

    @Override
    public DoDispatchOrderVo poToVo(DoDispatchOrder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DoDispatchOrderVo doDispatchOrderVo = new DoDispatchOrderVo();

        doDispatchOrderVo.setId( arg0.getId() );
        doDispatchOrderVo.setProjectId( arg0.getProjectId() );
        doDispatchOrderVo.setTakerId( arg0.getTakerId() );

        return doDispatchOrderVo;
    }

    @Override
    public List<DoDispatchOrderVo> poToVoList(List<DoDispatchOrder> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<DoDispatchOrderVo> list = new ArrayList<DoDispatchOrderVo>( arg0.size() );
        for ( DoDispatchOrder doDispatchOrder : arg0 ) {
            list.add( poToVo( doDispatchOrder ) );
        }

        return list;
    }

    @Override
    public DoDispatchOrder voToPo(DoDispatchOrderVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DoDispatchOrder doDispatchOrder = new DoDispatchOrder();

        doDispatchOrder.setId( arg0.getId() );
        doDispatchOrder.setProjectId( arg0.getProjectId() );
        doDispatchOrder.setTakerId( arg0.getTakerId() );

        return doDispatchOrder;
    }

    @Override
    public List<DoDispatchOrder> voToPoList(List<DoDispatchOrderVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<DoDispatchOrder> list = new ArrayList<DoDispatchOrder>( arg0.size() );
        for ( DoDispatchOrderVo doDispatchOrderVo : arg0 ) {
            list.add( voToPo( doDispatchOrderVo ) );
        }

        return list;
    }
}
