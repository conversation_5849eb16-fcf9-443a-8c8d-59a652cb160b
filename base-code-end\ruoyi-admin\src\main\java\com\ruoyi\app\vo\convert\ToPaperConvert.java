package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.vo.ToPaperVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.acceptance.domain.ToPaper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 论文管理PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToPaperConvert extends BeanPoVoMapper<ToPaper, ToPaperVo> {

        ToPaperConvert INSTANCE = Mappers.getMapper(ToPaperConvert.class);

}
