package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToGroupBuying;
import com.ruoyi.app.vo.ToGroupBuyingVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToGroupBuyingConvertImpl implements ToGroupBuyingConvert {

    @Override
    public ToGroupBuyingVo poToVo(ToGroupBuying arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToGroupBuyingVo toGroupBuyingVo = new ToGroupBuyingVo();

        toGroupBuyingVo.setGroupId( arg0.getGroupId() );
        toGroupBuyingVo.setId( arg0.getId() );
        toGroupBuyingVo.setOpenId( arg0.getOpenId() );
        toGroupBuyingVo.setOrderId( arg0.getOrderId() );

        return toGroupBuyingVo;
    }

    @Override
    public List<ToGroupBuyingVo> poToVoList(List<ToGroupBuying> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToGroupBuyingVo> list = new ArrayList<ToGroupBuyingVo>( arg0.size() );
        for ( ToGroupBuying toGroupBuying : arg0 ) {
            list.add( poToVo( toGroupBuying ) );
        }

        return list;
    }

    @Override
    public ToGroupBuying voToPo(ToGroupBuyingVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToGroupBuying toGroupBuying = new ToGroupBuying();

        toGroupBuying.setGroupId( arg0.getGroupId() );
        toGroupBuying.setId( arg0.getId() );
        toGroupBuying.setOpenId( arg0.getOpenId() );
        toGroupBuying.setOrderId( arg0.getOrderId() );

        return toGroupBuying;
    }

    @Override
    public List<ToGroupBuying> voToPoList(List<ToGroupBuyingVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToGroupBuying> list = new ArrayList<ToGroupBuying>( arg0.size() );
        for ( ToGroupBuyingVo toGroupBuyingVo : arg0 ) {
            list.add( voToPo( toGroupBuyingVo ) );
        }

        return list;
    }
}
