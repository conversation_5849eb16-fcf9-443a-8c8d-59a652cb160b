package com.ruoyi.dispatch.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.vo.ChatMessagesTwoVO;
import com.ruoyi.dispatch.domain.ChatMessages;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 消息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IChatMessagesService extends IService<ChatMessages> {

    /**
     * 分页查询消息
     * @param chatMessages 消息
     * @param page 分页条件
     * @return 消息集合
     */
    IPage<ChatMessages> pages(ChatMessages chatMessages, IPage<ChatMessages> page);

    /**
     * 查询消息列表
     * 
     * @param chatMessages 消息
     * @return 消息集合
     */
     List<ChatMessages> selectList(ChatMessages chatMessages);

    List<ChatMessagesTwoVO> merchantMessageList();

    List<ChatMessagesTwoVO> customerMessageList();
}
