import request from '@/utils/request'

// 查询接单订单列表
export function listOrder(query) {
  return request({
    url: '/acceptance/order/list',
    method: 'get',
    params: query
  })
}

// 查询接单订单详细
export function getOrder(id) {
  return request({
    url: '/acceptance/order/' + id,
    method: 'get'
  })
}

// 新增接单订单
export function addOrder(data) {
  return request({
    url: '/acceptance/order',
    method: 'post',
    data: data
  })
}

// 修改接单订单
export function updateOrder(data) {
  return request({
    url: '/acceptance/order',
    method: 'put',
    data: data
  })
}

// 删除接单订单
export function delOrder(id) {
  return request({
    url: '/acceptance/order/' + id,
    method: 'delete'
  })
}
