package com.ruoyi.app.controller.paper;

import com.ruoyi.acceptance.dto.ToPaperDTO;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.acceptance.vo.ToPaperVo;
import com.ruoyi.app.manager.ToPaperManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 论文管理Controller
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
@Api(tags = "移动端论文管理管理")
@RestController
@RequestMapping("/wx/acceptance/paper")
public class WxToPaperController {

    @Autowired
    private ToPaperManager toPaperManager;

    /**
     * 分页查询论文管理
     */
    @ApiOperation("分页查询论文管理")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public R<IPage<ToPaperVo>> list(ToPaperVo toPaperVo, Query query)
    {
//        return R.ok(toPaperManager.page(toPaperVo, query));
        return R.ok(toPaperManager.selectToPapers(toPaperVo, query));
    }

    @ApiOperation("已交易列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/tradedList")
    public R<List<ToPaperDTO>> tradedList()
    {
        return R.ok(toPaperManager.tradedList());
    }

    @ApiOperation("待付款列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/obligationList")
    public R<List<ToPaperDTO>> obligationList()
    {
        return R.ok(toPaperManager.obligationList());
    }

    @ApiOperation("根据订单状态查询列表 1待付款   3已交易  4结尾款")
    @ApiOperationSupport(order = 1)
    @GetMapping("/orderStatusList")
    public R<List<ToPaperDTO>> orderStatusList(@RequestParam("status") String status, @RequestParam("paperName") String paperName)
    {
        // status 1待付款   3已交易  4结尾款
        return R.ok(toPaperManager.orderStatusList(status,paperName));
    }

    @ApiOperation("订单状态查询列表返回Map")
    @GetMapping("/orderStatusListByMap")
    public R<?> orderStatusListByMap(@RequestParam("paperName") String paperName)
    {
        return R.ok(toPaperManager.orderStatusListByMap(paperName));
    }

    @ApiOperation("非卖品列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/notSaleList")
    public R<List<ToPaperDTO>> notSaleList()
    {
        // status 1待付款   3已交易  4结尾款
        return R.ok(toPaperManager.notSaleList());
    }


    /**
    * 查询论文管理全部列表
    */
    @ApiOperation("查询论文管理全部列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/allList")
    public R<List<ToPaperVo>> allList(ToPaperVo toPaperVo){
        return R.ok(toPaperManager.list(toPaperVo));
    }

    /**
     * 导出论文管理列表
     */
    @ApiOperation("导出论文管理列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToPaperVo toPaperVo, Query query)
    {
        List<ToPaperVo> list = toPaperManager.page(toPaperVo, query).getRecords();
        ExcelUtil<ToPaperVo> util = new ExcelUtil<ToPaperVo>(ToPaperVo.class);
        util.exportExcel(response, list, "论文管理数据");
    }

    /**
     * 获取论文管理详细信息
     */
    @ApiOperation("获取论文管理详细")
    @ApiOperationSupport(order = 3)
    @GetMapping(value = "/{id}")
    public R<ToPaperVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toPaperManager.getInfo(id));
    }

    /**
     * 新增论文管理
     */
    @ApiOperation("新增论文管理")
    @ApiOperationSupport(order = 4)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToPaperVo toPaperVo)
    {
        return toPaperManager.savePaperFromWx(toPaperVo);
    }

    /**
     * 修改论文管理
     */
    @ApiOperation("修改论文管理")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToPaperVo toPaperVo)
    {
        return toPaperManager.edit(toPaperVo) ? R.ok() : R.fail();
    }

}
