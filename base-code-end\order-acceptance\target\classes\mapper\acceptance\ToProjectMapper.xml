<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToProjectMapper">

    <select id="projectList" parameterType="com.ruoyi.acceptance.vo.ToProjectTwoVO" resultType="com.ruoyi.acceptance.vo.ToProjectThreeVO">
        SELECT
        a.id,
        a.title,
        a.category_selection_one,
        a.category_selection_two,
        a.requirement_description,
        a.expected_delivery_time,
        a.additional_services,
        b.total_price
        from to_project a
        LEFT JOIN to_order b on  a.id = b.project_id
        where
        1=1
        <if test="lastId != null and lastId != ''">
            and a.id &lt; #{lastId}
        </if>
        <if test="categorySelectionTwo != null and categorySelectionTwo != ''">
            and a.category_selection_two = #{categorySelectionTwo}
        </if>
        order by a.id desc
        <if test="size != null and size != ''">
            limit #{size}
        </if>
    </select>

    <select id="projectDetail" resultType="com.ruoyi.acceptance.vo.ToProjectFourVO">
        SELECT
        a.id,
        a.title,
        a.category_selection_one,
        a.category_selection_two,
        a.expected_delivery_time,
        b.total_price,
        b.advance_price,
        a.additional_services,
        a.requirement_description,
        a.attachments,
        b.order_pay_status,
        a.customer_openid,
        c.head_sculpture
        from to_project a
        left join to_order b on a.id = b.project_id
        left join to_user c on a.customer_openid = c.open_id
        where a.id = #{projectId}
    </select>

    <select id="myProjectList" parameterType="com.ruoyi.acceptance.dto.ToProjectTwoDTO" resultType="com.ruoyi.acceptance.vo.ToProjectSixVO">
        SELECT
        a.id,
        a.title,
        a.category_selection_one,
        a.category_selection_two,
        a.requirement_description,
        a.start_time,
        a.additional_services,
        b.total_price,
        b.order_pay_status
        from to_project a
        LEFT JOIN to_order b on  a.id = b.project_id
        where
        1=1
        and a.merchant_openid = #{merchantOpenid}
        <if test="status != null and status != '' and status == 0">
            and b.order_pay_status in (0,1,2,3,4,5)
        </if>
        <if test="status != null and status != '' and status == 1">
            and b.order_pay_status in (1,2,3,4)
        </if>
        <if test="status != null and status != '' and status == 2">
            and b.order_pay_status = 0
        </if>
        <if test="status != null and status != '' and status == 3">
            and b.order_pay_status = 5
        </if>
        order by a.id desc
    </select>

    <select id="myProjectDetail" resultType="com.ruoyi.acceptance.vo.ToProjectFiveVO">
        SELECT
        a.id,
        a.title,
        b.total_price,
        b.advance_price,
        b.deposit_payment_time,
        b.balance_payment,
        b.balance_payment_time,
        b.total_price * 0.01 group_price,
        a.project_end_time,
        b.order_pay_status
        from to_project a
        left join to_order b on a.id = b.project_id
        where a.id = #{projectId}
    </select>

</mapper>