package com.ruoyi.app.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
@Api(tags = "移动端字典管理")
@RestController
@RequestMapping("/wx/system/dict/data")
public class WxSysDictDataController extends BaseController
{

    @Autowired
    private ISysDictTypeService dictTypeService;

    @ApiOperation("字典列表查询")
    @GetMapping("/list/{dictType}")
    public TableDataInfo list(@PathVariable String dictType)
    {
        List<SysDictData> list = dictTypeService.selectDictDataByType(dictType);
        return getDataTable(list);
    }

}
