package com.ruoyi.acceptance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ToOrderSubDTO {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 主项目ID */
    @Excel(name = "主项目ID")

    private Long projectId;

    /** 标题 */

    private String title;

    /** 需求描述 */

    private String requirementDescription;

    /** 全额价格 */

    private BigDecimal totalPrice;

    /** 是否申请退款（0否1是） */

    private Integer applyRefund;

    /** 订单付款状态（0已退款1待支付2已支付3交易结束） */

    private Integer orderPayStatus;

    /** 商户单号（微信支付交易标识） */

    private String outTradeNo;

    /** 交易单号 */

    private String transactionId;

    /** 支付时间 */
    @JsonFormat( pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date depositPaymentTime;

    /**
     * 所属项目的标题
     */
    private String projectTitle;
}
