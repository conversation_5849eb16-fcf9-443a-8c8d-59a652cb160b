package com.ruoyi.api.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.api.manager.OssFileManager;
import com.ruoyi.api.vo.OssFileVo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.integration.dto.OssResultDto;
import com.ruoyi.system.integration.sdkadapter.OssAdapter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 阿里云OSS接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oss")
@Api(tags = "文件管理")
@RequiredArgsConstructor
public class OssController {

    private final OssFileManager ossFileManager;

    /**
     * -上传文件
     * -自定义OSS存储路径
     */
    @ApiOperation("上传文件-自定义存储路径")
    @ApiOperationSupport(order = 1)
    @PostMapping("/uploadFile")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "MultipartFile", paramType = "form", dataTypeClass = MultipartFile.class),
            @ApiImplicitParam(name = "path", value = "自定义目录", dataType = "String", paramType = "query", dataTypeClass = String.class)
    })
    public R<OssFileVo> uploadFile(@RequestPart(value = "file") MultipartFile file,
                        @RequestParam(value = "path", defaultValue = "example") String path) {
        OssResultDto OssResultDto = OssAdapter.uploadFile(file, path);
        return R.ok(ossFileManager.getResult(OssResultDto));
    }

    /**
     * 上传文件-指定目录
     */
    @ApiOperation("上传文件-指定目录")
    @ApiOperationSupport(order = 2)
    @ApiImplicitParam(name = "file", value = "文件", required = true, dataType = "MultipartFile", paramType = "form", dataTypeClass = MultipartFile.class)
    @PostMapping("/uploadFixed")
    public R<OssFileVo> uploadFixed(@RequestPart(value = "file") MultipartFile file) {
        OssResultDto OssResultDto = OssAdapter.uploadFile(file);
        return R.ok(ossFileManager.getResult(OssResultDto));
    }

    /**
     * 查询文件详细信息
     */
    @ApiOperation("查询文件详细信息")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "fileName", value = "文件路径全称", dataType = "String", paramType = "query", dataTypeClass = String.class)
    @GetMapping("/getFileDetail")
    public R<OssFileVo> getFileDetail(@RequestParam String fileName) {
        return R.ok(ossFileManager.getInfo(fileName));
    }

    /**
     * 删除Oss文件
     */
    @ApiOperation("删除Oss文件")
    @ApiOperationSupport(order = 4)
    @ApiImplicitParam(name = "fileName", value = "文件路径全称", dataType = "String", paramType = "query", dataTypeClass = String.class)
    @GetMapping("/deleteOssFile")
    public R<String> deleteOssFile(@RequestParam String fileName) {
        if (OssAdapter.deleteFile(fileName)) {
            ossFileManager.remove(fileName);
            return R.ok("删除成功");
        }
        return R.fail("删除失败");
    }

    /**
     * 标记删除文件
     */
    @ApiOperation("标记删除文件")
    @ApiOperationSupport(order = 4)
    @ApiImplicitParam(name = "fileName", value = "文件路径全称", dataType = "String", paramType = "query", dataTypeClass = String.class)
    @GetMapping("/deleteFile")
    public R<String> deleteFile(@RequestParam String fileName) {
        ossFileManager.removeFlag(fileName);
        return R.ok("删除成功");
    }

    /**
     * oss路径下获取文件带有效期的url，获取的url可下载
     */
    @ApiOperation("获取的url可下载")
    @ApiOperationSupport(order = 5)
    @ApiImplicitParam(name = "fileName", value = "文件路径全称", dataType = "String", paramType = "query", dataTypeClass = String.class)
    @GetMapping("/getOssUrl")
    public R<String> getOssUrl(@RequestParam String fileName) {
        return R.ok(OssAdapter.getOssUrl(fileName));
    }

    /**
     * 预览PDF或图片
     */
    @ApiOperation("预览PDF或图片")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "fileName", value = "文件路径全称", dataType = "String", paramType = "query", dataTypeClass = String.class)
    @GetMapping("/preview")
    public ResponseEntity<InputStreamResource> previewFile(@RequestParam String fileName) {
        // 获取文件类型
        String contentType = FileUtil.extName(fileName);
        // 获取文件流
        InputStream inputStream = OssAdapter.getInputStream(fileName);
        // 预览
        HttpHeaders httpHeaders = new HttpHeaders();
        if ("pdf".equalsIgnoreCase(contentType)) {
            httpHeaders.add("Content-Type", MediaType.APPLICATION_PDF_VALUE);
        } else if ("jpg".equalsIgnoreCase(contentType) || "png".equalsIgnoreCase(contentType) || "jpeg".equalsIgnoreCase(contentType)) {
            httpHeaders.add("Content-Type", MediaType.IMAGE_JPEG_VALUE);
        }
        InputStreamResource inputStreamResource = new InputStreamResource(inputStream);
        return new ResponseEntity<>(inputStreamResource, httpHeaders, HttpStatus.OK);
    }

    /**
     * 列举所有的文件url
     */
    @ApiOperation("列举所有的文件url")
    @ApiOperationSupport(order = 7)
    @GetMapping("/urlList")
    public R<List<String>> urlList() {
        return R.ok(OssAdapter.urlList());
    }

    /**
     * 图片转base64
     */
    @ApiOperation("图片转base64")
    @ApiOperationSupport(order = 8)
    @ApiImplicitParam(name = "fileName", value = "文件路径全称", dataType = "String", paramType = "query", dataTypeClass = String.class)
    @GetMapping("/base64")
    public R<String> base64(@RequestParam String fileName) {
        return R.ok(Base64.encode(OssAdapter.getInputStream(fileName)));
    }

    /**
     * 文件转byte[]
     */
    @ApiOperation("文件转byte[]")
    @ApiOperationSupport(order = 9)
    @ApiImplicitParam(name = "fileName", value = "文件路径全称", dataType = "String", paramType = "query", dataTypeClass = String.class)
    @GetMapping("/bytes")
    public R<byte[]> bytes(@RequestParam String fileName) {
        return R.ok(OssAdapter.getBytes(fileName));
    }

}
