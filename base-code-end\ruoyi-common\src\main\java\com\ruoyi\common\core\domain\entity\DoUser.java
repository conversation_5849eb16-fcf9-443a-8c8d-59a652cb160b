package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.PoBaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 派单用户对象 do_user
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Data
@TableName("do_user")
public class DoUser extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** openid */
    @Excel(name = "openid")
    private String openId;

    /** 商户名称/个人名称 */
    @Excel(name = "商户名称/个人名称")
    private String nickname;

    /** 商户/个人手机号 */
    @Excel(name = "商户/个人手机号")
    private String phoneNumber;

    /** 商户/个人微信号 */
    @Excel(name = "商户/个人微信号")
    private String wechatId;

    /** 头像 */
    @Excel(name = "头像")
    private String headSculpture;

    /** 商户信誉分 */
    @Excel(name = "商户信誉分")
    private Integer creditScore;

    /** 商户信誉分 */
    @Excel(name = "性别")
    private String sex;

    /** 商户信誉分 */
    @Excel(name = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /** 商户信誉分 */
    @Excel(name = "接单一级类目")
    private String orderCategoryOne;

    /** 商户信誉分 */
    @Excel(name = "接单二级类目")
    private String orderCategoryTwo;

}
