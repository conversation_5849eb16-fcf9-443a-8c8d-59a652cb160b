package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToOrderFlow;
import com.ruoyi.app.vo.ToOrderFlowVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToOrderFlowConvertImpl implements ToOrderFlowConvert {

    @Override
    public ToOrderFlowVo poToVo(ToOrderFlow arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderFlowVo toOrderFlowVo = new ToOrderFlowVo();

        toOrderFlowVo.setFlowType( arg0.getFlowType() );
        toOrderFlowVo.setId( arg0.getId() );
        toOrderFlowVo.setOrderId( arg0.getOrderId() );
        toOrderFlowVo.setOutTradeNo( arg0.getOutTradeNo() );
        toOrderFlowVo.setTransactionId( arg0.getTransactionId() );

        return toOrderFlowVo;
    }

    @Override
    public List<ToOrderFlowVo> poToVoList(List<ToOrderFlow> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderFlowVo> list = new ArrayList<ToOrderFlowVo>( arg0.size() );
        for ( ToOrderFlow toOrderFlow : arg0 ) {
            list.add( poToVo( toOrderFlow ) );
        }

        return list;
    }

    @Override
    public ToOrderFlow voToPo(ToOrderFlowVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderFlow toOrderFlow = new ToOrderFlow();

        toOrderFlow.setFlowType( arg0.getFlowType() );
        toOrderFlow.setId( arg0.getId() );
        toOrderFlow.setOrderId( arg0.getOrderId() );
        toOrderFlow.setOutTradeNo( arg0.getOutTradeNo() );
        toOrderFlow.setTransactionId( arg0.getTransactionId() );

        return toOrderFlow;
    }

    @Override
    public List<ToOrderFlow> voToPoList(List<ToOrderFlowVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderFlow> list = new ArrayList<ToOrderFlow>( arg0.size() );
        for ( ToOrderFlowVo toOrderFlowVo : arg0 ) {
            list.add( voToPo( toOrderFlowVo ) );
        }

        return list;
    }
}
