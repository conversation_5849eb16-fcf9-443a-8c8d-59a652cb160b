package com.ruoyi.app.controller.orderSub;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.app.manager.ToOrderSubManager;
import com.ruoyi.app.vo.ToOrderSubVo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 接单订单子订单Controller
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Api(tags = "接单订单子订单管理")
@RestController
@RequestMapping("/wx/acceptance/orderSub")
public class WxToOrderSubController {

    @Autowired
    private ToOrderSubManager toOrderSubManager;

    /**
     * 分页查询接单订单子订单
     */
    @ApiOperation("分页查询接单订单子订单")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public R<IPage<ToOrderSubVo>> list(ToOrderSubVo toOrderSubVo, Query query)
    {
        return R.ok(toOrderSubManager.page(toOrderSubVo, query));
    }

    /**
    * 查询接单订单子订单全部列表
    */
    @ApiOperation("查询接单订单子订单全部列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/allList")
    public R<List<ToOrderSubVo>> allList(){
        return R.ok(toOrderSubManager.currentWxUserOrderSublist());
    }

    /**
     * 导出接单订单子订单列表
     */
    @ApiOperation("导出接单订单子订单列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToOrderSubVo toOrderSubVo, Query query)
    {
        List<ToOrderSubVo> list = toOrderSubManager.page(toOrderSubVo, query).getRecords();
        ExcelUtil<ToOrderSubVo> util = new ExcelUtil<ToOrderSubVo>(ToOrderSubVo.class);
        util.exportExcel(response, list, "接单订单子订单数据");
    }

    /**
     * 获取接单订单子订单详细信息
     */
    @ApiOperation("获取接单订单子订单详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @GetMapping(value = "/{id}")
    public R<ToOrderSubVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toOrderSubManager.getInfo(id));
    }

    /**
     * 新增接单订单子订单
     */
    @ApiOperation("新增接单订单子订单")
    @ApiOperationSupport(order = 4)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToOrderSubVo toOrderSubVo)
    {
        return toOrderSubManager.addOrderSubFromWx(toOrderSubVo) ? R.ok() : R.fail();
    }

    /**
     * 修改接单订单子订单
     */
    @ApiOperation("修改接单订单子订单")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToOrderSubVo toOrderSubVo)
    {
        return toOrderSubManager.edit(toOrderSubVo) ? R.ok() : R.fail();
    }

    /**
     * 删除接单订单子订单
     */
    @ApiOperation("删除接单订单子订单")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toOrderSubManager.remove(ids) ? R.ok() : R.fail();
    }


}
