package com.ruoyi.app.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ChatConversationMembersVo;
import com.ruoyi.app.manager.ChatConversationMembersManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 会话成员Controller
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Api(tags = "会话成员管理")
@RestController
@RequestMapping("/dispatch/members")
public class ChatConversationMembersController {

    @Autowired
    private ChatConversationMembersManager chatConversationMembersManager;

    /**
     * 分页查询会话成员
     */
    @ApiOperation("分页查询会话成员")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('dispatch:members:list')")
    @GetMapping("/list")
    public R<IPage<ChatConversationMembersVo>> list(ChatConversationMembersVo chatConversationMembersVo, Query query)
    {
        return R.ok(chatConversationMembersManager.page(chatConversationMembersVo, query));
    }

    /**
    * 查询会话成员全部列表
    */
    @ApiOperation("查询会话成员全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('dispatch:members:list')")
    @GetMapping("/allList")
    public R<List<ChatConversationMembersVo>> allList(ChatConversationMembersVo chatConversationMembersVo){
        return R.ok(chatConversationMembersManager.list(chatConversationMembersVo));
    }

    /**
     * 导出会话成员列表
     */
    @ApiOperation("导出会话成员列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('dispatch:members:export')")
    @Log(title = "会话成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChatConversationMembersVo chatConversationMembersVo, Query query)
    {
        List<ChatConversationMembersVo> list = chatConversationMembersManager.page(chatConversationMembersVo, query).getRecords();
        ExcelUtil<ChatConversationMembersVo> util = new ExcelUtil<ChatConversationMembersVo>(ChatConversationMembersVo.class);
        util.exportExcel(response, list, "会话成员数据");
    }

    /**
     * 获取会话成员详细信息
     */
    @ApiOperation("获取会话成员详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:members:query')")
    @GetMapping(value = "/{id}")
    public R<ChatConversationMembersVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(chatConversationMembersManager.getInfo(id));
    }

    /**
     * 新增会话成员
     */
    @ApiOperation("新增会话成员")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('dispatch:members:add')")
    @Log(title = "会话成员", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ChatConversationMembersVo chatConversationMembersVo)
    {
        return chatConversationMembersManager.add(chatConversationMembersVo) ? R.ok() : R.fail();
    }

    /**
     * 修改会话成员
     */
    @ApiOperation("修改会话成员")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('dispatch:members:edit')")
    @Log(title = "会话成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ChatConversationMembersVo chatConversationMembersVo)
    {
        return chatConversationMembersManager.edit(chatConversationMembersVo) ? R.ok() : R.fail();
    }

    /**
     * 删除会话成员
     */
    @ApiOperation("删除会话成员")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:members:remove')")
    @Log(title = "会话成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return chatConversationMembersManager.remove(ids) ? R.ok() : R.fail();
    }
}
