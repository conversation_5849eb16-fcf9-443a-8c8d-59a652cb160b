package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 提示词-项目类型关联对象 common_prompt_project_type
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@TableName("common_prompt_project_type")
public class CommonPromptProjectType{

    /** 提示词ID */
    private Long promptId;

    /** 类目类型ID */
    private Long categoryTypeId;

    /** 输出类型 */
    private String outputType;

}
