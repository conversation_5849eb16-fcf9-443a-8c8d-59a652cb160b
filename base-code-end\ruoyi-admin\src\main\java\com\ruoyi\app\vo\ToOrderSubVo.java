package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 接单订单子订单对象 ToOrderSubVo
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@ApiModel(value = " ToOrderSubVo", description = "接单订单子订单对象VO")
public class ToOrderSubVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 主项目ID */
    @Excel(name = "主项目ID")
    @ApiModelProperty(value = "主项目ID", name="projectId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectId;

    /** 标题 */
    @Excel(name = "标题")
    @ApiModelProperty(value = "标题", name="title")
    private String title;

    /** 需求描述 */
    @Excel(name = "需求描述")
    @ApiModelProperty(value = "需求描述", name="requirementDescription")
    private String requirementDescription;

    /** 全额价格 */
    @Excel(name = "全额价格")
    @ApiModelProperty(value = "全额价格", name="totalPrice")
    private BigDecimal totalPrice;

    /** 是否申请退款（0否1是） */
    @Excel(name = "是否申请退款", readConverterExp = "0=否1是")
    @ApiModelProperty(value = "是否申请退款", name="applyRefund")
    private Integer applyRefund;

    /** 订单付款状态（0已退款1待支付2已支付3交易结束） */
    @Excel(name = "订单付款状态", readConverterExp = "0=已退款1待支付2已支付3交易结束")
    @ApiModelProperty(value = "订单付款状态", name="orderPayStatus")
    private Integer orderPayStatus;

    /** 商户单号（微信支付交易标识） */
    @Excel(name = "商户单号", readConverterExp = "微=信支付交易标识")
    @ApiModelProperty(value = "商户单号", name="outTradeNo")
    private String outTradeNo;

    /** 交易单号 */
    @Excel(name = "交易单号")
    @ApiModelProperty(value = "交易单号", name="transactionId")
    private String transactionId;

    /** 支付时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间", name="depositPaymentTime")
    private Date depositPaymentTime;

    /**
     * 所属项目的标题
     */
    private String projectTitle;


}
