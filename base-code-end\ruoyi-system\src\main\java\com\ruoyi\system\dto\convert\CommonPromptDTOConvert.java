package com.ruoyi.system.dto.convert;

import com.ruoyi.common.core.domain.convert.BeanPoDtoMapper;
import com.ruoyi.system.domain.CommonPrompt;
import com.ruoyi.system.domain.CommonPromptProjectType;
import com.ruoyi.system.dto.CommonPromptDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 提示词PoDTO转换器
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CommonPromptDTOConvert extends BeanPoDtoMapper<CommonPrompt, CommonPromptDTO> {

    CommonPromptDTOConvert INSTANCE = Mappers.getMapper(CommonPromptDTOConvert.class);
    
    /**
     * PO转DTO，并设置关联的项目类型列表
     *
     * @param commonPrompt PO对象
     * @param projectTypes 关联的项目类型列表
     * @return DTO对象
     */
    @Mapping(target = "commonPromptProjectTypes", source = "projectTypes")
    CommonPromptDTO poToDto(CommonPrompt commonPrompt, List<Long> projectTypes);


}
