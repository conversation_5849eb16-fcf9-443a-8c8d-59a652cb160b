package com.ruoyi.acceptance.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 团购信息对象 to_group_buying
 * 
 * <AUTHOR>
 * @date 2024-08-11
 */
@Data
@TableName("to_group_buying")
public class ToGroupBuying extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 团购id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 订单id */
    @TableField(value = "order_id")
    private Long orderId;

    /** 团购组别id */
    @TableField(value = "group_id")
    private String groupId;

    /** openid */
    @TableField(value = "open_id")
    private String openId;

    /** 主团购人 */
    @TableField(value = "group_main_order")
    private Integer groupMainOrder;

    /** 主团购人 */
    @TableField(value = "whether_out_group")
    private Integer whetherOutGroup;
}
