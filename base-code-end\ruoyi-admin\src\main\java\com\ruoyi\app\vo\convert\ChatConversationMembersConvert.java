package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatConversationMembersVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.dispatch.domain.ChatConversationMembers;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 会话成员PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ChatConversationMembersConvert extends BeanPoVoMapper<ChatConversationMembers, ChatConversationMembersVo> {

        ChatConversationMembersConvert INSTANCE = Mappers.getMapper(ChatConversationMembersConvert.class);

}
