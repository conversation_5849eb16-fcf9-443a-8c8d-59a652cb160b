package com.ruoyi.acceptance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.acceptance.domain.*;
import com.ruoyi.acceptance.dto.HeadSculptureDTO;
import com.ruoyi.acceptance.dto.ToPaperDTO;
import com.ruoyi.acceptance.mapper.*;
import com.ruoyi.acceptance.service.*;
import com.ruoyi.acceptance.vo.ToPaperVo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.ToUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.acceptance.domain.ToWallet;
import com.ruoyi.acceptance.mapper.ToWalletMapper;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.ruoyi.common.constant.AcceptanceConstants.*;
import static com.ruoyi.common.constant.OrderAcceptanceConstant.*;


/**
 * 论文管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
@Service
public class ToPaperServiceImpl extends ServiceImpl<ToPaperMapper,ToPaper> implements IToPaperService {

    @Autowired
    private ToPaperMapper toPaperMapper;
    @Autowired
    private ToOrderMapper toOrderMapper;
    @Autowired
    private ToOrderHistoryMapper toOrderHistoryMapper;
    @Autowired
    private IToOrderHistoryService toOrderHistoryService;
    @Autowired
    private ToGroupBuyingMapper toGroupBuyingMapper;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ToWalletMapper toWalletMapper;
    @Autowired
    private IToWalletService toWalletService;
    @Autowired
    private ToWalletHistoryMapper toWalletHistoryMapper;
    @Autowired
    private IToWalletHistoryService toWalletHistoryService;
    @Autowired
    private IToPaperService toPaperService;
    @Autowired
    private IToOrderService toOrderService;
    @Autowired
    private IToUserService toUserService;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToPaper> queryWrapper(ToPaper toPaper) {
        LambdaQueryWrapper<ToPaper> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toPaper.getOrderId()), ToPaper::getOrderId, toPaper.getOrderId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toPaper.getTitle()), ToPaper::getTitle, toPaper.getTitle());
        queryWrapper.eq(ObjUtil.isNotEmpty(toPaper.getTechnicalSelection()), ToPaper::getTechnicalSelection, toPaper.getTechnicalSelection());
        queryWrapper.eq(ObjUtil.isNotEmpty(toPaper.getIncludingServices()), ToPaper::getIncludingServices, toPaper.getIncludingServices());
        queryWrapper.eq(ObjUtil.isNotEmpty(toPaper.getIntroduction()), ToPaper::getIntroduction, toPaper.getIntroduction());
        queryWrapper.eq(ObjUtil.isNotEmpty(toPaper.getBuyMembers()), ToPaper::getBuyMembers, toPaper.getBuyMembers());
        return queryWrapper;
    }

    /**
     * 查询论文管理分页
     *
     * @param toPaper 论文管理
     * @return 论文管理
     */
    @Override
    public IPage<ToPaper> pages(ToPaper toPaper, IPage<ToPaper> page)
    {
        return toPaperMapper.selectPage(page, this.queryWrapper(toPaper));
    }

    @Override
    public IPage<ToPaperDTO> selectToPapers(ToPaperVo toPaperVo, IPage<ToPaperDTO> page) {
        return toPaperMapper.selectToPapers(toPaperVo,page);
    }

    /**
     * 查询论文管理列表
     * 
     * @param toPaper 论文管理
     * @return 论文管理
     */
    @Override
    public List<ToPaper> selectList(ToPaper toPaper)
    {
        return toPaperMapper.selectList(this.queryWrapper(toPaper));
    }

    @Override
    public ToPaperDTO getToPaperDtoById(Long id) {
        return toPaperMapper.getToPaperDtoById(id);
    }

    @Override
    public List<ToPaperDTO> tradedList() {
        return toPaperMapper.tradedList();
    }

    @Override
    public List<ToPaperDTO> obligationList() {
        return toPaperMapper.obligationList();
    }

    @Override
    public List<ToPaperDTO> orderStatusList(String status,String paperName) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        return toPaperMapper.orderStatusList(status,openId,paperName);
    }

    @Override
    public HashMap orderStatusListByMap(String paperName) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        HashMap<Object,Object> result = new HashMap<>();
        List<ToPaperDTO> statusAll = toPaperMapper.orderStatusList("status_all", openId, paperName);
        List<ToPaperDTO> statusWaitPay = toPaperMapper.orderStatusList("status_wait_pay", openId, paperName);
        List<ToPaperDTO> statusWaitDeliver = toPaperMapper.orderStatusList("status_wait_deliver", openId, paperName);
        List<ToPaperDTO> statusEnd = toPaperMapper.orderStatusList("status_end", openId, paperName);
        result.put("statusAll",statusAll);
        result.put("statusWaitPay",statusWaitPay);
        result.put("statusWaitDeliver",statusWaitDeliver);
        result.put("statusEnd",statusEnd);
        return result;
    }

    @Override
    public List<ToPaperDTO> notSaleList() {
        return toPaperMapper.notSaleList();
    }

    @Override
    public ToPaperDTO getInfoById(Long id) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        ToPaperDTO toPaperDTO = toPaperMapper.getToPaperDtoById(id);
        //判断前端页面退款按钮是否可用  超过两天不可退款
        if (toPaperDTO.getOrderPayStatus()==2){
            Date depositPaymentTime = toPaperDTO.getDepositPaymentTime();
            Date currentDate = new Date();
            long diffInMillies = currentDate.getTime() - depositPaymentTime.getTime();
            if (diffInMillies >= 2 * 24 * 60 * 60 * 1000) {
                toPaperDTO.setRefundFlag(0);
            } else {
                toPaperDTO.setRefundFlag(1);
            }
        }
        //判断团购状态
        List<HeadSculptureDTO> headSculptureDTOS = toGroupBuyingMapper.selectHeadSculpture(toPaperDTO.getOrderId());
        toPaperDTO.setHeadSculptureDTOS(headSculptureDTOS);
        LambdaQueryWrapper<ToGroupBuying> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ToGroupBuying::getWhetherOutGroup,0).eq(ToGroupBuying::getOpenId,openId).eq(ToGroupBuying::getOrderId,toPaperDTO.getOrderId());
        ToGroupBuying toGroupBuyingFromDb = toGroupBuyingMapper.selectOne(queryWrapper);
        if (StringUtils.isNull(toGroupBuyingFromDb)){//未团购
            toPaperDTO.setGroupStatus(GROUP_PURCHASE_NOT_START);
            toPaperDTO.setGroupEndFlag(NO);
            toPaperDTO.setGroupDaysRemaining(-1l);
        }else {
            toPaperDTO.setGroupId(toGroupBuyingFromDb.getGroupId());
            Date createTimeByMainOrder = toGroupBuyingMapper.getCreateTimeByMainOrder(toGroupBuyingFromDb.getGroupId());
            Calendar now1 = Calendar.getInstance();
            Date currentDate = now1.getTime();
            long groupDaysHavePassed = currentDate.getTime() - createTimeByMainOrder.getTime();
            String temp1 = sysConfigService.selectConfigByKey(GROUP_DAYS);
            long groupDays = Integer.parseInt(temp1)*24*60*60;
            long groupDaysRemaining = groupDays-groupDaysHavePassed / 1000;
            if (groupDaysRemaining<ZERO){
                toPaperDTO.setGroupEndFlag(YES);
                Integer groupSuccessPeopleNum = Integer.parseInt(sysConfigService.selectConfigByKey(GROUP_SUCCESS_PEOPLE_NUM));
                List<Integer> integers = toGroupBuyingMapper.selectOrderStatusByCroupByGroupId(toGroupBuyingFromDb.getGroupId());
                if (integers.contains(REFUNDABLE_DEPOSIT)){//5团购倒计时结束
                    toPaperDTO.setGroupStatus(GROUP_PURCHASE_END);
                    toPaperDTO.setGroupDaysRemaining(-1l);
                }else {
                    if (integers.size() >= groupSuccessPeopleNum) {
                        if (integers.contains(REFUNDED)||integers.contains(DEPOSIT_TO_PAID)){//团购失败
                            toPaperDTO.setGroupStatus(GROUP_PURCHASE_FAIL);
                            toPaperDTO.setGroupDaysRemaining(-1l);
                        }else {//团购成功
                            toPaperDTO.setGroupStatus(GROUP_PURCHASE_SUCCESSFUL);
                            toPaperDTO.setGroupDaysRemaining(-1l);
                        }
                    }else {//团购失败
                        toPaperDTO.setGroupStatus(GROUP_PURCHASE_FAIL);
                        toPaperDTO.setGroupDaysRemaining(-1l);
                    }
                }
            }else {//团购中
                toPaperDTO.setGroupStatus(GROUP_BUYING);
                toPaperDTO.setGroupEndFlag(NO);
                toPaperDTO.setGroupDaysRemaining(groupDaysRemaining);
            }
        }
        return toPaperDTO;
    }

    @Override
    public ToPaperDTO getInfoForPcById(Long id) {
        return toPaperMapper.getToPaperDtoById(id);
    }

    @Override
    public R<?> savePaperFromWeb(ToPaperVo toPaperVo) {
        ToPaper toPaper = new ToPaper();
        BeanUtils.copyProperties(toPaperVo,toPaper);
        // 订单论文
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        ToOrder toOrder = new ToOrder();
        BeanUtils.copyProperties(toPaperVo,toOrder);
        toOrder.setOrderPayStatus(1);
        toOrderMapper.insert(toOrder);
        toPaper.setOrderId(toOrder.getId());
        int insert = toPaperMapper.insert(toPaper);

        // 订单流水
        String customerCashback = sysConfigService.selectConfigByKey(CUSTOMER_CASHBACK);
        BigDecimal bigDecimal = new BigDecimal(customerCashback);

        //2025-6-16,查询下单用户的信息
//        ToUser customUser = toUserService.selectToUserByOpenId(toPaperVo.getCustom());
        ToUser shareUser = toUserService.selectShareUserByInvitedUserOpenId(toPaperVo.getCustom());
        ToOrderHistory toOrderHistory = new ToOrderHistory();
        toOrderHistory.setUserId(userId.toString());
        toOrderHistory.setOrderId(toOrder.getId());
        toOrderHistory.setFrozenAmount(bigDecimal);
        toOrderHistory.setNotWithdrawAmount(bigDecimal);
        toOrderHistory.setTotalAmount(bigDecimal);
        //2025-6-16,新增订单流水信息的用户id和销售id
        toOrderHistory.setCustomerOpenId(toPaperVo.getCustom());
        //查询用户所在代理的openid

        toOrderHistory.setAgentOpenId(shareUser.getOpenId());
        //查询用户订单
        toOrderHistoryMapper.insert(toOrderHistory);

        // 参加团购 钱包增加相应返现金额
        Integer groupPurchase = toPaperVo.getGroupPurchase();
        if ( groupPurchase == 1 ){
            LambdaQueryWrapper<ToWallet> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ToWallet::getOpenId,toPaper.getCustom());
            ToWallet toWallet = toWalletMapper.selectOne(lambdaQueryWrapper);
            BigDecimal currentBalance = toWallet.getBalance();
            BigDecimal newBalance = currentBalance.add(bigDecimal);
            toWallet.setBalance(newBalance);
            toWalletMapper.update(toWallet,lambdaQueryWrapper);
            // 钱包流水
            ToWalletHistory toWalletHistory = new ToWalletHistory();
            toWalletHistory.setOpenId(toPaper.getCustom());
            toWalletHistory.setFrozenAmount(bigDecimal);
            toWalletHistory.setNotWithdrawAmount(bigDecimal);
            toWalletHistory.setTotalAmount(bigDecimal);
            toWalletHistoryMapper.insert(toWalletHistory);
        }

        if (insert>0){
            return R.ok("新增成功");
        }else {
            return R.fail("新增失败");
        }

    }

    @Override
    public R<?> savePaperFromWx(ToPaper paper) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        ToOrder toOrder = new ToOrder();
        BeanUtils.copyProperties(paper,toOrder);
        toOrder.setOrderPayStatus(1);
        toOrderMapper.insert(toOrder);
        paper.setOrderId(toOrder.getId());
        int insert = toPaperMapper.insert(paper);
        //流水
        ToOrderHistory toOrderHistory = new ToOrderHistory();
        toOrderHistory.setUserId(openId);
        toOrderHistory.setOrderId(toOrder.getId());
        String customerCashback = sysConfigService.selectConfigByKey(CUSTOMER_CASHBACK);
        BigDecimal bigDecimal = new BigDecimal(customerCashback);
        toOrderHistory.setFrozenAmount(bigDecimal);
        toOrderHistory.setNotWithdrawAmount(bigDecimal);
        toOrderHistory.setTotalAmount(bigDecimal);
        toOrderHistoryMapper.insert(toOrderHistory);

        if (insert>0){
            return R.ok("新增成功");
        }else {
            return R.fail("新增失败");
        }

    }

    @Override
    public R<?> removeForWeb(Long[] ids, Long[] orderIds) {
        // 删除流水
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        // 订单流水
        String customerCashback = sysConfigService.selectConfigByKey(CUSTOMER_CASHBACK);
        BigDecimal bigDecimal = new BigDecimal(customerCashback);

        List<ToPaper> toPapers = toPaperService.listByIds(CollUtil.toList(ids));
        ArrayList<ToOrderHistory> toOrderHistorys = new ArrayList<>();
        ArrayList<String> openIds = new ArrayList<>();
        for (ToPaper paper : toPapers){
            ToOrderHistory toOrderHistory = new ToOrderHistory();
            toOrderHistory.setUserId(userId.toString());
            toOrderHistory.setOrderId(paper.getOrderId());
            toOrderHistory.setCustomerOpenId(paper.getCustom());
            toOrderHistory.setFrozenAmount(bigDecimal);
            toOrderHistory.setNotWithdrawAmount(bigDecimal);
            toOrderHistory.setTotalAmount(bigDecimal);
            toOrderHistorys.add(toOrderHistory);
            openIds.add(paper.getCustom());
        }
        toOrderHistoryService.saveBatch(toOrderHistorys);

        List<ToWallet> toWallets = toWalletService.selectToWalletsByOpenId(openIds);
        ArrayList<ToWalletHistory> toWalletHistories = new ArrayList<>();
        for (ToWallet toWallet : toWallets){
            toWallet.setBalance(BigDecimal.ZERO);

            ToWalletHistory toWalletHistory = new ToWalletHistory();
            toWalletHistory.setOpenId(toWallet.getOpenId());
            toWalletHistory.setFrozenAmount(bigDecimal);
            toWalletHistory.setNotWithdrawAmount(bigDecimal);
            toWalletHistory.setTotalAmount(bigDecimal);
            toWalletHistories.add(toWalletHistory);
        }
        toWalletService.updateBatchById(toWallets);
        toWalletHistoryService.saveBatch(toWalletHistories);

        toOrderService.removeByIds(CollUtil.toList(orderIds));
        toPaperService.removeByIds(CollUtil.toList(ids));
        return R.ok();
    }

    @Override
    public R<?> editForWeb(ToPaperVo toPaperVo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = loginUser.getUserId();
        String customerCashback = sysConfigService.selectConfigByKey(CUSTOMER_CASHBACK);
        BigDecimal bigDecimal = new BigDecimal(customerCashback);
//        // 参加团购 钱包增加相应返现金额
//        Integer groupPurchase = toPaperVo.getGroupPurchase();
//        if ( groupPurchase == 1 ){
//            ToWallet toWallet= new ToWallet();
//            toWallet.setBalance(bigDecimal);
//            LambdaQueryWrapper<ToWallet> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            lambdaQueryWrapper.eq(ToWallet::getOpenId,toPaperVo.getCustom());
//            toWalletMapper.update(toWallet,lambdaQueryWrapper);
//            // 钱包流水
//            ToWalletHistory toWalletHistory = new ToWalletHistory();
//            toWalletHistory.setOpenId(userId.toString());
//            toWalletHistory.setFrozenAmount(bigDecimal);
//            toWalletHistory.setNotWithdrawAmount(bigDecimal);
//            toWalletHistory.setTotalAmount(bigDecimal);
//            toWalletHistoryMapper.insert(toWalletHistory);
//        }
        // 更新
        ToPaper toPaper = new ToPaper();
        BeanUtils.copyProperties(toPaperVo,toPaper);
        ToOrder toOrder = new ToOrder();
        BeanUtils.copyProperties(toPaperVo,toOrder);
        toOrder.setId(toPaperVo.getOrderId());
        toOrderService.updateById(toOrder);
        toPaperService.updateById(toPaper);
        return R.ok();
    }

}
