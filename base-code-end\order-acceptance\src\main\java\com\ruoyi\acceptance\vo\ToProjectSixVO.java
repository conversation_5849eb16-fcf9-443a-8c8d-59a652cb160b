package com.ruoyi.acceptance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目基本信息对象 ToProjectVo
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@ApiModel(value = " ToProjectThreeVO", description = "项目基本信息对象VO")
public class ToProjectSixVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    @ApiModelProperty(value = "标题", name="title")
    private String title;

    /** 选择类目， */
    @Excel(name = "选择类目，1级")
    @ApiModelProperty(value = "选择类目，按逗号分隔", name="categorySelectionOne")
    private String categorySelectionOne;

    /** 选择类目 */
    @Excel(name = "选择类目，2级")
    @ApiModelProperty(value = "选择类目，按逗号分隔", name="categorySelectionTwo")
    private String categorySelectionTwo;

    /** 需求描述 */
    @Excel(name = "需求描述")
    @ApiModelProperty(value = "需求描述", name="requirementDescription")
    private String requirementDescription;

    /** 追加服务 */
    @Excel(name = "追加服务")
    @ApiModelProperty(value = "追加服务", name="additionalServices")
    private String additionalServices;

    /** 期望交付时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    private BigDecimal totalPrice;
    private Integer orderPayStatus;

}
