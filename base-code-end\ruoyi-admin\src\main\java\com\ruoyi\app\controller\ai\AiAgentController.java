package com.ruoyi.app.controller.ai;

import com.ruoyi.app.manager.AiAgentManager;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * AI助手Controller
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Api(tags = "AI助手管理")
@RestController
@RequestMapping("/ai/agent")
public class AiAgentController {

    @Autowired
    private AiAgentManager aiAgentManager;

    /**
     * 生成项目需求（流式响应）
     * 
     * @param projectName 项目名称
     * @param categoryCode 类目代码
     * @return 流式响应
     */
    @ApiOperation("生成项目需求")
    @PostMapping(value = "/generate-requirements", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> generateProjectRequirements(
            @ApiParam(value = "项目名称", required = true) @RequestParam String projectName,
            @ApiParam(value = "类目代码", required = true) @RequestParam String categoryCode) {
        return Flux.from(aiAgentManager.generateProjectRequirements(projectName, categoryCode))
                .map(content -> content)
                .doOnComplete(() -> System.out.println("需求生成完成"));
    }
    /**
     * 生成项目需求（非流式响应）
     * 
     * @param projectName 项目名称
     * @param categoryCode 类目代码
     * @return 响应结果
     */
    @ApiOperation("生成项目需求（完整响应）")
    @PostMapping("/generate-requirements-sync")
    public R<String> generateProjectRequirementsSync(
            @ApiParam(value = "项目名称", required = true) @RequestParam String projectName,
            @ApiParam(value = "类目代码", required = true) @RequestParam String categoryCode) {
        
        try {
            String result = aiAgentManager.generateProjectRequirementsSync(projectName, categoryCode);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("生成需求失败：" + e.getMessage());
        }
    }
}
