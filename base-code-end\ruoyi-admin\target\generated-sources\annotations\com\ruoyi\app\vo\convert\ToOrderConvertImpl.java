package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToOrder;
import com.ruoyi.acceptance.vo.ToOrderVo;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToOrderConvertImpl implements ToOrderConvert {

    @Override
    public ToOrderVo poToVo(ToOrder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderVo toOrderVo = new ToOrderVo();

        if ( arg0.getAdvancePrice() != null ) {
            toOrderVo.setAdvancePrice( arg0.getAdvancePrice().toString() );
        }
        toOrderVo.setApplyRefund( arg0.getApplyRefund() );
        if ( arg0.getBalancePayment() != null ) {
            toOrderVo.setBalancePayment( arg0.getBalancePayment().toString() );
        }
        toOrderVo.setBalancePaymentTime( arg0.getBalancePaymentTime() );
        toOrderVo.setCustomOpenId( arg0.getCustomOpenId() );
        if ( arg0.getDepositPaymentTime() != null ) {
            toOrderVo.setDepositPaymentTime( new SimpleDateFormat().format( arg0.getDepositPaymentTime() ) );
        }
        toOrderVo.setGroupPurchase( arg0.getGroupPurchase() );
        toOrderVo.setId( arg0.getId() );
        toOrderVo.setOrderPayStatus( arg0.getOrderPayStatus() );
        toOrderVo.setOutTradeNo( arg0.getOutTradeNo() );
        toOrderVo.setProjectId( arg0.getProjectId() );
        toOrderVo.setSettlement( arg0.getSettlement() );
        if ( arg0.getTotalPrice() != null ) {
            toOrderVo.setTotalPrice( arg0.getTotalPrice().toString() );
        }
        toOrderVo.setTransactionId( arg0.getTransactionId() );

        return toOrderVo;
    }

    @Override
    public List<ToOrderVo> poToVoList(List<ToOrder> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderVo> list = new ArrayList<ToOrderVo>( arg0.size() );
        for ( ToOrder toOrder : arg0 ) {
            list.add( poToVo( toOrder ) );
        }

        return list;
    }

    @Override
    public ToOrder voToPo(ToOrderVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrder toOrder = new ToOrder();

        if ( arg0.getAdvancePrice() != null ) {
            toOrder.setAdvancePrice( new BigDecimal( arg0.getAdvancePrice() ) );
        }
        toOrder.setApplyRefund( arg0.getApplyRefund() );
        if ( arg0.getBalancePayment() != null ) {
            toOrder.setBalancePayment( new BigDecimal( arg0.getBalancePayment() ) );
        }
        toOrder.setBalancePaymentTime( arg0.getBalancePaymentTime() );
        toOrder.setCustomOpenId( arg0.getCustomOpenId() );
        try {
            if ( arg0.getDepositPaymentTime() != null ) {
                toOrder.setDepositPaymentTime( new SimpleDateFormat().parse( arg0.getDepositPaymentTime() ) );
            }
        }
        catch ( ParseException e ) {
            throw new RuntimeException( e );
        }
        toOrder.setGroupPurchase( arg0.getGroupPurchase() );
        toOrder.setId( arg0.getId() );
        toOrder.setOrderPayStatus( arg0.getOrderPayStatus() );
        toOrder.setOutTradeNo( arg0.getOutTradeNo() );
        toOrder.setProjectId( arg0.getProjectId() );
        toOrder.setSettlement( arg0.getSettlement() );
        if ( arg0.getTotalPrice() != null ) {
            toOrder.setTotalPrice( new BigDecimal( arg0.getTotalPrice() ) );
        }
        toOrder.setTransactionId( arg0.getTransactionId() );

        return toOrder;
    }

    @Override
    public List<ToOrder> voToPoList(List<ToOrderVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrder> list = new ArrayList<ToOrder>( arg0.size() );
        for ( ToOrderVo toOrderVo : arg0 ) {
            list.add( voToPo( toOrderVo ) );
        }

        return list;
    }
}
