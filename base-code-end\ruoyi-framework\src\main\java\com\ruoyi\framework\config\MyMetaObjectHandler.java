package com.ruoyi.framework.config;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruoyi.common.config.TokenConfig;
import com.ruoyi.common.constant.DbConstant;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

@Slf4j
public class MyMetaObjectHandler implements MetaObjectHandler {


    /**
     * 插入操作，自动填充
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        String userId = getUserId();
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "delFlag", Integer.class, DbConstant.DB_STATUS_0);
        Object createBy = this.getFieldValByName("createBy", metaObject);
        Object updateBy = this.getFieldValByName("updateBy", metaObject);
        if (null == createBy) {
            this.strictInsertFill(metaObject, "createBy", String.class, userId);
        }
        if (null == updateBy) {
            this.strictInsertFill(metaObject, "updateBy", String.class, userId);
        }
    }

    /**
     * 更新操作，自动填充
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        String userId = getUserId();
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        Object updateBy = this.getFieldValByName("updateBy", metaObject);
        if (null == updateBy) {
            this.strictInsertFill(metaObject, "updateBy", String.class, userId);
        }
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    private String getUserId() {
        String userId = null;
        try {
            String sourceType = SecurityUtils.getSourceType();
            if (StrUtil.isNotBlank(sourceType)&&StrUtil.equals(sourceType, TokenConfig.WX_APPLET)) {
                userId = SecurityUtils.getWxUserOpenid();
            } else {
                userId = SecurityUtils.getUsername();
            }
        } catch (Exception e) {
            log.error("获取用户名失败", e);
        }
        return userId;
    }

}