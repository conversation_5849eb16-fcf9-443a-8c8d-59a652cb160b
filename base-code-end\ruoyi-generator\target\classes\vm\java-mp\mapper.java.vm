package ${packageName}.mapper;
import org.springframework.stereotype.Repository;
import ${packageName}.domain.${ClassName};
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
#if($table.sub)
import com.baomidou.mybatisplus.core.mapper.BaseMapper; ${packageName}.domain.${subClassName};
#end

/**
 * ${functionName}Mapper接口
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Repository
public interface ${ClassName}Mapper extends BaseMapper<${ClassName}> {
#if($table.sub)

    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    public ${ClassName} select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField});

    /**
     * 查询${subTable.functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    public ${subTable} select${subTable}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField});


    /**
     * 批量删除${subTable.functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的数据主键集合
     * @return 结果
     */
    public int delete${subClassName}By${subTableFkClassName}s(${pkColumn.javaType}[] ${pkColumn.javaField}s);

    /**
     * 批量新增${subTable.functionName}
     *
     * @param ${subclassName}List ${subTable.functionName}列表
     * @return 结果
     */
    public int batch${subClassName}(List<${subClassName}> ${subclassName}List);


    /**
     * 通过${functionName}主键删除${subTable.functionName}信息
     *
     * @param ${pkColumn.javaField} ${functionName}ID
     * @return 结果
     */
    public int delete${subClassName}By${subTableFkClassName}(${pkColumn.javaType} ${pkColumn.javaField});
#end
}
