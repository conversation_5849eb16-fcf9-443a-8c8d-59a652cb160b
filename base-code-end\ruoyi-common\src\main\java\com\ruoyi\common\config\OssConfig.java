package com.ruoyi.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "oss")
public class OssConfig implements InitializingBean {


    /**
     * 阿里云 oss 站点
     */
    private String endpoint;

    /**
     * 阿里云 oss 自定义域名
     */
    private String dns;

    /**
     * 阿里云 oss 公钥
     */
    private String accessKeyId;

    /**
     * 阿里云 oss 私钥
     */
    private String accessKeySecret;

    /**
     * 阿里云 oss 文件根目录
     */
    private String bucketName;

    /**
     * 阿里云 oss 文件推荐路径
     */
    private String filePath;


    /**
     * 阿里云 oss 文件推荐路径
     */
    private String fileName;

    /**
     * url有效期(S)
     */
    private Long policyExpire;

    /**
     * 上传文件大小(M)
     */
    private Long maxSize;

    @Override
    public String toString() {
        return "OssConfig{" +
                "endpoint='" + endpoint + '\'' +
                ", dns='" + dns + '\'' +
                ", accessKeyId='" + accessKeyId + '\'' +
                ", accessKeySecret='" + accessKeySecret + '\'' +
                ", bucketName='" + bucketName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileName='" + fileName + '\'' +
                ", policyExpire=" + policyExpire +
                ", maxSize=" + maxSize +
                '}';
    }

    // 静态获取
    public static String OSS_END_POINT;
    public static String OSS_DNS;
    public static String OSS_ACCESS_KEY_ID;
    public static String OSS_ACCESS_KEY_SECRET;
    public static String OSS_BUCKET_NAME;
    public static String OSS_FILE_PATH;
    public static String OSS_FILE_NAME;
    public static Long OSS_POLICY_EXPIRE;
    public static Long OSS_MAX_SIZE;

    @Override
    public void afterPropertiesSet() {
        OSS_END_POINT = endpoint;
        OSS_DNS=dns;
        OSS_ACCESS_KEY_ID = accessKeyId;
        OSS_ACCESS_KEY_SECRET = accessKeySecret;
        OSS_BUCKET_NAME = bucketName;
        OSS_FILE_PATH=filePath;
        OSS_FILE_NAME=fileName;
        OSS_POLICY_EXPIRE = policyExpire;
        OSS_MAX_SIZE = maxSize;
    }

}
