package com.ruoyi.system.integration.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 阿里云OSS上传结果
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class OssResultDto {
    /**
     * 上传是否成功
     */
    private boolean success;

    /**
     * 上传的文件名（如果使用自定义文件路径，会返回完整的路径+文件名，例：cf/abc.png）
     */
    private String fileName;

    /**
     * 原文件名
     */
    private String oldFileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 上传成功的返回url（带过期时间）
     */
    private String url;

    /**
     * 提示信息
     */
    private String msg;

    public OssResultDto() {
    }

    public OssResultDto(boolean success, String fileName, String url, String msg) {
        this.success = success;
        this.fileName = fileName;
        this.url = url;
        this.msg = msg;
    }

    public OssResultDto(boolean success, String fileName, String oldFileName, Long fileSize, String url, String msg) {
        this.success = success;
        this.fileName = fileName;
        this.oldFileName = oldFileName;
        this.fileSize = fileSize;
        this.url = url;
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "OssResultDto{" +
                "success=" + success +
                ", fileName='" + fileName + '\'' +
                ", oldFileName" + oldFileName + '\'' +
                ", fileSize" + fileSize + '\'' +
                ", url='" + url + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}