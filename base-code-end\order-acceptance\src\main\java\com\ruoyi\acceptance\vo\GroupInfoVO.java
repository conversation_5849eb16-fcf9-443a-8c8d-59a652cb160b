package com.ruoyi.acceptance.vo;

import com.ruoyi.acceptance.dto.HeadSculptureDTO;
import com.ruoyi.acceptance.dto.ToPaperDTO;
import lombok.Data;

import java.util.List;

@Data
public class GroupInfoVO {
    // 团长属性
    private String groupLeader;

    // 团购订单号属性
    private String groupId;

    //团长头像
    private String groupLeaderHeadSculpture;
    //是否空团 1是0否
    private Integer whetherEmptyGroup;

    //1未团购 2团购中 3团购成功 4团购失败 5团购倒计时结束
    private Integer groupStatus;

    //团购倒计时时间差
    private Long groupDaysRemaining;

    //1为结束 0为未结束
    private Integer groupEndFlag;

    // 团购成员头像属性，这里使用String来表示头像的URL或路径
    private List<HeadSculptureDTO> headSculptureDTOS;

    private List<ToPaperDTO> paperList;

}