# knife4j配置
knife4j:
    enable: true
    # 开启生产环境屏蔽 production: true时就访问不了swagger了
    production: false

# 阿里云测试环境
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: **********************************************************************************************************************************************************************************
                username: root
                password: mZN5i9OG7pWTAhdOh/o6Lw0/ZAzfxnQX0H+FT/9+XOZgzO9erwt+6H01GC4nw3hEvYbV2AcB4Odse1L03et9sw==
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            connectionProperties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJrda7jrWV6YDE6krojjBXBIi/22uU2yJZisu/fmqAhq0e13E167Gr4qjffyL9p5EbKEgCxx+u48cxsZIVRm8C8CAwEAAQ==
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                config:
                    # 是否配置加密
                    enabled: true
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
wx:
    #微信小程序appid
    appId: wxa90c54608cdf7f93
    #商户号
    mchId: 1680962099
    #证书序列号
    mchSerialNo: 2EAF75DB4318C0260559DAE7AA5B4C2661C6E383
    #api密钥
    apiKey: H1XuwXEFbrUXn6Q4VIW5NQJGjo8WFeCx
    #微信地址
    domain:
    #回调接口地址
    notifyUrl: https://o325h82634.zicp.fun/pay/payNotify
#    notifyUrl: http://************/prod-api/pay/payNotify
    #证书密钥地址
    apiClientKeyPath: D:\MyCode\dev_entrepreneurship\wxpay\apiclient_key.pem
#    apiClientKeyPath: /root/lemaCode/wxPay/apiclient_key.pem
    #商户证书地址
    apiClientCertPath: D:\MyCode\dev_entrepreneurship\wxpay\apiclient_cert.pem
#    apiClientCertPath: /root/lemaCode/wxPay/apiclient_cert.pem
    #平台证书地址
    platformCertPath: D:\MyCode\dev_entrepreneurship\wxpay\platForm.pem
#    platformCertPath: /root/lemaCode/wxPay/platForm.pem