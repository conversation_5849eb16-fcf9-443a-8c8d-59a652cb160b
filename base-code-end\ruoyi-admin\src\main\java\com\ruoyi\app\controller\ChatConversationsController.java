package com.ruoyi.app.controller;

import com.ruoyi.acceptance.vo.ChatConversationsTwoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ChatConversationsVo;
import com.ruoyi.app.manager.ChatConversationsManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 会话Controller
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Api(tags = "会话管理")
@RestController
@RequestMapping("/wx/dispatch/conversations")
public class ChatConversationsController {

    @Autowired
    private ChatConversationsManager chatConversationsManager;

    /**
     * 分页查询会话
     */
    @ApiOperation("分页查询会话")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('dispatch:conversations:list')")
    @GetMapping("/list")
    public R<IPage<ChatConversationsVo>> list(ChatConversationsVo chatConversationsVo, Query query)
    {
        return R.ok(chatConversationsManager.page(chatConversationsVo, query));
    }

    /**
    * 查询会话全部列表
    */
    @ApiOperation("查询会话全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('dispatch:conversations:list')")
    @GetMapping("/allList")
    public R<List<ChatConversationsVo>> allList(ChatConversationsVo chatConversationsVo){
        return R.ok(chatConversationsManager.list(chatConversationsVo));
    }

    /**
     * 导出会话列表
     */
    @ApiOperation("导出会话列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('dispatch:conversations:export')")
    @Log(title = "会话", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChatConversationsVo chatConversationsVo, Query query)
    {
        List<ChatConversationsVo> list = chatConversationsManager.page(chatConversationsVo, query).getRecords();
        ExcelUtil<ChatConversationsVo> util = new ExcelUtil<ChatConversationsVo>(ChatConversationsVo.class);
        util.exportExcel(response, list, "会话数据");
    }

    /**
     * 获取会话详细信息
     */
    @ApiOperation("获取会话详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "conversationId", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:conversations:query')")
    @GetMapping(value = "/{conversationId}")
    public R<ChatConversationsVo> getInfo(@PathVariable("conversationId") Long conversationId)
    {
        return R.ok(chatConversationsManager.getInfo(conversationId));
    }

    /**
     * 新增会话
     */
    @ApiOperation("新增会话")
    @PostMapping
    public R<?> add(@RequestBody ChatConversationsVo chatConversationsVo)
    {
        return chatConversationsManager.add(chatConversationsVo) ? R.ok() : R.fail();
    }

    @ApiOperation("新增会话")
    @PostMapping("/saveConversation")
    public R<?> saveConversation(@RequestBody ChatConversationsTwoVO chatConversationsTwoVO)
    {
        return chatConversationsManager.saveConversation(chatConversationsTwoVO);
    }

    /**
     * 修改会话
     */
    @ApiOperation("修改会话")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('dispatch:conversations:edit')")
    @Log(title = "会话", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ChatConversationsVo chatConversationsVo)
    {
        return chatConversationsManager.edit(chatConversationsVo) ? R.ok() : R.fail();
    }

    /**
     * 删除会话
     */
    @ApiOperation("删除会话")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "conversationIds", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:conversations:remove')")
    @Log(title = "会话", businessType = BusinessType.DELETE)
    @DeleteMapping("/{conversationIds}")
    public R<?> remove(@PathVariable Long[] conversationIds)
    {
        return chatConversationsManager.remove(conversationIds) ? R.ok() : R.fail();
    }
}
