package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToPaper;
import com.ruoyi.acceptance.vo.ToPaperVo;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToPaperConvertImpl implements ToPaperConvert {

    @Override
    public ToPaperVo poToVo(ToPaper arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToPaperVo toPaperVo = new ToPaperVo();

        toPaperVo.setBuyMembers( arg0.getBuyMembers() );
        if ( arg0.getCreateTime() != null ) {
            toPaperVo.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        toPaperVo.setCustom( arg0.getCustom() );
        toPaperVo.setId( arg0.getId() );
        toPaperVo.setIncludingServices( arg0.getIncludingServices() );
        toPaperVo.setIntroduction( arg0.getIntroduction() );
        toPaperVo.setOrderId( arg0.getOrderId() );
        toPaperVo.setTechnicalSelection( arg0.getTechnicalSelection() );
        toPaperVo.setTitle( arg0.getTitle() );

        return toPaperVo;
    }

    @Override
    public List<ToPaperVo> poToVoList(List<ToPaper> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToPaperVo> list = new ArrayList<ToPaperVo>( arg0.size() );
        for ( ToPaper toPaper : arg0 ) {
            list.add( poToVo( toPaper ) );
        }

        return list;
    }

    @Override
    public ToPaper voToPo(ToPaperVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToPaper toPaper = new ToPaper();

        if ( arg0.getCreateTime() != null ) {
            toPaper.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        toPaper.setBuyMembers( arg0.getBuyMembers() );
        toPaper.setCustom( arg0.getCustom() );
        toPaper.setId( arg0.getId() );
        toPaper.setIncludingServices( arg0.getIncludingServices() );
        toPaper.setIntroduction( arg0.getIntroduction() );
        toPaper.setOrderId( arg0.getOrderId() );
        toPaper.setTechnicalSelection( arg0.getTechnicalSelection() );
        toPaper.setTitle( arg0.getTitle() );

        return toPaper;
    }

    @Override
    public List<ToPaper> voToPoList(List<ToPaperVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToPaper> list = new ArrayList<ToPaper>( arg0.size() );
        for ( ToPaperVo toPaperVo : arg0 ) {
            list.add( voToPo( toPaperVo ) );
        }

        return list;
    }
}
