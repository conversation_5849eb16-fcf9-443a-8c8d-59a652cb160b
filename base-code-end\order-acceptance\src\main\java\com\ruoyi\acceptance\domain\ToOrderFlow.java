package com.ruoyi.acceptance.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 订单流水对象 to_order_flow
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@TableName("to_order_flow")
public class ToOrderFlow extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 订单ID */
    @TableField(value = "order_id")
    private Long orderId;

    /** 流水类型（first_pay定金final_pay尾款） */
    @TableField(value = "flow_type")
    private String flowType;

    /** 商户单号 */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /** 交易单号 */
    @TableField(value = "transaction_id")
    private String transactionId;

}
