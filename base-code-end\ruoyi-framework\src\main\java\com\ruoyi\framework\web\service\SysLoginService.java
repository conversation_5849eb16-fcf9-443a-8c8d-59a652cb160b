package com.ruoyi.framework.web.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.acceptance.service.IDoUserService;
import com.ruoyi.acceptance.service.IToUserService;
import com.ruoyi.common.config.TokenConfig;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.DbConstant;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.core.domain.model.*;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.user.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.system.integration.dto.WeChatUserInfoDto;
import com.ruoyi.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import static com.ruoyi.common.constant.WxUrl.WX_URL_ONE;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IToUserLoginService toUserLoginService;

    @Autowired
    private IDoUserLoginService doUserLoginService;

    @Autowired
    private IToUserService toUserService;

    @Autowired
    private IDoUserService doUserService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ISysMenuService menuService;


    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid) {
        // 验证码校验
        validateCaptcha(username, code, uuid);
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 登录验证
     *
     * @param wxLoginBody 微信登录入参
     * @return 结果
     */
    public String wxLogin(WxLoginBody wxLoginBody) {
        log.debug("wxLogin===>" + JSON.toJSONString(wxLoginBody));
        // code换openid
        // https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(WX_URL_ONE)
                .queryParam("appid", "wxa90c54608cdf7f93") // 添加第一个参数
                .queryParam("secret", "c16796cb9baef95da4abebf3c863b976") // 添加第二个参数
                .queryParam("js_code", wxLoginBody.getCode()) // 添加第二个参数
                .queryParam("grant_type", "authorization_code"); // 添加第二个参数
        String finalUrl = builder.toUriString(); // 生成最终的 URL
        ResponseEntity<String> forEntity = restTemplate.getForEntity(finalUrl, String.class);
        String body = forEntity.getBody();
        log.debug("wxLogin===>", body);
        WeChatUserInfoDto weChatUserInfo = JSON.parseObject(body, WeChatUserInfoDto.class);
        if (weChatUserInfo == null) {
            throw new RuntimeException("请求微信服务获取用户openid失败！");
        }
        ToUserLogin toUserLogin = toUserLoginService.getById(weChatUserInfo.getOpenid());
        ToUser toUser = null;
        Set<String> perms = new HashSet<>();
        if (StringUtils.isNull(toUserLogin)) {
            ToUserLogin toUserLogin1 = new ToUserLogin();
            toUserLogin1.setOpenid(weChatUserInfo.getOpenid());
            toUserLogin1.setUnionid(weChatUserInfo.getUnionid());
            toUserLogin1.setStatus(DbConstant.DB_STATUS_0);
            toUserLogin1.setEmpowerInfo(DbConstant.DB_STATUS_0);
            toUserLogin1.setCreateTime(LocalDateTimeUtil.now());
            toUserLogin1.setLoginDate(LocalDateTimeUtil.now());
            toUserLoginService.save(toUserLogin1);
        } else {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(toUser, TokenConfig.WX_APPLET);
            AuthenticationContextHolder.setContext(authenticationToken);
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            //修改登录时间
            ToUserLogin toUserLogin1 = new ToUserLogin();
            toUserLogin1.setOpenid(weChatUserInfo.getOpenid());
            toUserLogin1.setLoginDate(LocalDateTimeUtil.now());
            toUserLoginService.updateById(toUserLogin1);
            toUser = toUserService.selectToUserByOpenId(weChatUserInfo.getOpenid());
            perms.addAll(menuService.selectMenuPermsByOpenId(weChatUserInfo.getOpenid()));
        }
        WxLoginUser wxLoginUser = new WxLoginUser(weChatUserInfo.getOpenid(), toUser, perms);
        //记录登录信息
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(weChatUserInfo.getOpenid(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        return tokenService.createWxToken(wxLoginUser);

    }

    /**
     * 登录验证
     *
     * @param dispatchLoginBody 微信登录入参
     * @return 结果
     */
    public String dispatchLogin(DispatchLoginBody dispatchLoginBody) {
        log.debug("wxLogin===>" + JSON.toJSONString(dispatchLoginBody));
        // code换openid
        // https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(WX_URL_ONE)
                .queryParam("appid", "wx38984d71a8680632") // 添加第一个参数
                .queryParam("secret", "d6d2c45f3e4409139e9681baf73128ca") // 添加第二个参数
                .queryParam("js_code", dispatchLoginBody.getCode()) // 添加第二个参数
                .queryParam("grant_type", "authorization_code"); // 添加第二个参数
        String finalUrl = builder.toUriString(); // 生成最终的 URL
        ResponseEntity<String> forEntity = restTemplate.getForEntity(finalUrl, String.class);
        String body = forEntity.getBody();
        log.debug("dispatchLogin===>", body);
        WeChatUserInfoDto weChatUserInfo = JSON.parseObject(body, WeChatUserInfoDto.class);
        if (weChatUserInfo == null) {
            throw new RuntimeException("请求微信服务获取用户openid失败！");
        }
        DoUserLogin doUserLogin = doUserLoginService.getById(weChatUserInfo.getOpenid());
        DoUser doUser = null;
        Set<String> perms = new HashSet<>();
        if (StringUtils.isNull(doUserLogin)) {
            DoUserLogin doUserLogin1 = new DoUserLogin();
            doUserLogin1.setOpenid(weChatUserInfo.getOpenid());
            doUserLogin1.setUnionid(weChatUserInfo.getUnionid());
            doUserLogin1.setStatus(DbConstant.DB_STATUS_0);
            doUserLogin1.setEmpowerInfo(DbConstant.DB_STATUS_0);
            doUserLogin1.setCreateTime(new Date());
            doUserLogin1.setLoginDate(LocalDateTimeUtil.now());
            doUserLoginService.save(doUserLogin1);
        } else {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(doUser, TokenConfig.DISPATCH_APPLET);
            AuthenticationContextHolder.setContext(authenticationToken);
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            //修改登录时间
            DoUserLogin doUserLogin1 = new DoUserLogin();
            doUserLogin1.setOpenid(weChatUserInfo.getOpenid());
            doUserLogin1.setLoginDate(LocalDateTimeUtil.now());
            doUserLoginService.updateById(doUserLogin1);
            doUser = doUserService.selectToUserByOpenId(weChatUserInfo.getOpenid());
            perms.addAll(menuService.selectMenuPermsByOpenId(weChatUserInfo.getOpenid()));
        }
        DispatchLoginUser dispatchLoginUser = new DispatchLoginUser(weChatUserInfo.getOpenid(), doUser, perms);
        //记录登录信息
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(weChatUserInfo.getOpenid(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        return tokenService.createDispatchToken(dispatchLoginUser);
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled) {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            redisCache.deleteObject(verifyKey);
            if (captcha == null) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            if (!code.equalsIgnoreCase(captcha)) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }
}
