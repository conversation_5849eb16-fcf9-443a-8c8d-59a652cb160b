package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.dispatch.service.IChatMessageStatusService;
import com.ruoyi.app.vo.ChatMessageStatusVo;
import com.ruoyi.app.vo.convert.ChatMessageStatusConvert;
import com.ruoyi.dispatch.domain.ChatMessageStatus;

import java.util.List;

/**
 * 消息状态Manager
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Component
public class ChatMessageStatusManager {

    @Autowired
    private IChatMessageStatusService chatMessageStatusService;

    @Autowired
    private ChatMessageStatusConvert chatMessageStatusConvert;

    /**
    * 分页查询消息状态
    */
    public IPage<ChatMessageStatusVo> page(ChatMessageStatusVo chatMessageStatusVo, Query query){
        IPage<ChatMessageStatus> page = chatMessageStatusService.pages(chatMessageStatusConvert.voToPo(chatMessageStatusVo), Condition.getPage(query));
        return (IPage<ChatMessageStatusVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ChatMessageStatusVo>(), ChatMessageStatusConvert.INSTANCE);
    }

    /**
    * 查询消息状态列表
    */
    public List<ChatMessageStatusVo> list(ChatMessageStatusVo chatMessageStatusVo) {
        List<ChatMessageStatus> chatMessageStatusList = chatMessageStatusService.selectList(chatMessageStatusConvert.voToPo(chatMessageStatusVo));
        return chatMessageStatusConvert.poToVoList(chatMessageStatusList);
    }

    /**
     * 查询消息状态详细信息
     */
    public ChatMessageStatusVo getInfo(Long id) {
        ChatMessageStatus chatMessageStatus = chatMessageStatusService.getById(id);
        return chatMessageStatusConvert.poToVo(chatMessageStatus);
    }

    /**
     * 新增消息状态
     */
    public boolean add(ChatMessageStatusVo chatMessageStatusVo) {
        return chatMessageStatusService.save(chatMessageStatusConvert.voToPo(chatMessageStatusVo));
    }

    /**
     * 修改消息状态
     */
    public boolean edit(ChatMessageStatusVo chatMessageStatusVo) {
        return chatMessageStatusService.updateById(chatMessageStatusConvert.voToPo(chatMessageStatusVo));
    }

    /**
     * 批量删除消息状态
     */
    public boolean remove(Long[] ids) {
        return chatMessageStatusService.removeByIds(CollUtil.toList(ids));
    }

}
