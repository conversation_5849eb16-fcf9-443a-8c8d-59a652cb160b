package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.dispatch.service.IChatConversationMembersService;
import com.ruoyi.app.vo.ChatConversationMembersVo;
import com.ruoyi.app.vo.convert.ChatConversationMembersConvert;
import com.ruoyi.dispatch.domain.ChatConversationMembers;

import java.util.List;

/**
 * 会话成员Manager
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Component
public class ChatConversationMembersManager {

    @Autowired
    private IChatConversationMembersService chatConversationMembersService;

    @Autowired
    private ChatConversationMembersConvert chatConversationMembersConvert;

    /**
    * 分页查询会话成员
    */
    public IPage<ChatConversationMembersVo> page(ChatConversationMembersVo chatConversationMembersVo, Query query){
        IPage<ChatConversationMembers> page = chatConversationMembersService.pages(chatConversationMembersConvert.voToPo(chatConversationMembersVo), Condition.getPage(query));
        return (IPage<ChatConversationMembersVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ChatConversationMembersVo>(), ChatConversationMembersConvert.INSTANCE);
    }

    /**
    * 查询会话成员列表
    */
    public List<ChatConversationMembersVo> list(ChatConversationMembersVo chatConversationMembersVo) {
        List<ChatConversationMembers> chatConversationMembersList = chatConversationMembersService.selectList(chatConversationMembersConvert.voToPo(chatConversationMembersVo));
        return chatConversationMembersConvert.poToVoList(chatConversationMembersList);
    }

    /**
     * 查询会话成员详细信息
     */
    public ChatConversationMembersVo getInfo(Long id) {
        ChatConversationMembers chatConversationMembers = chatConversationMembersService.getById(id);
        return chatConversationMembersConvert.poToVo(chatConversationMembers);
    }

    /**
     * 新增会话成员
     */
    public boolean add(ChatConversationMembersVo chatConversationMembersVo) {
        return chatConversationMembersService.save(chatConversationMembersConvert.voToPo(chatConversationMembersVo));
    }

    /**
     * 修改会话成员
     */
    public boolean edit(ChatConversationMembersVo chatConversationMembersVo) {
        return chatConversationMembersService.updateById(chatConversationMembersConvert.voToPo(chatConversationMembersVo));
    }

    /**
     * 批量删除会话成员
     */
    public boolean remove(Long[] ids) {
        return chatConversationMembersService.removeByIds(CollUtil.toList(ids));
    }

}
