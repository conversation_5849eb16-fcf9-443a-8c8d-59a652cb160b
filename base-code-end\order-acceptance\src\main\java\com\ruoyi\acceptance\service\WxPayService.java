package com.ruoyi.acceptance.service;

import com.ruoyi.common.core.domain.R;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface WxPayService {
    R payRefund(String orderId);

    R jsApiPay(String orderId);

    void payNotify(HttpServletRequest request, HttpServletResponse response);

    String v3Get();

    R withdrawalToSmallChange(Integer amount);
}
