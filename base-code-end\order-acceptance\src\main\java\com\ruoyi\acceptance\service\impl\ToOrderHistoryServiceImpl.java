package com.ruoyi.acceptance.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjUtil;
import com.ruoyi.acceptance.domain.ToOrderHistory;
import com.ruoyi.acceptance.dto.CommissionRankingDTO;
import com.ruoyi.acceptance.dto.DistributionReportDTO;
import com.ruoyi.acceptance.dto.InvitationRankingDTO;
import com.ruoyi.acceptance.dto.TransactionListDTO;
import com.ruoyi.acceptance.mapper.ToOrderHistoryMapper;
import com.ruoyi.acceptance.service.IToOrderHistoryService;
import com.ruoyi.acceptance.service.IToUserService;
import com.ruoyi.acceptance.vo.TransactionListVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.ToUser;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.acceptance.domain.ToWallet;
import com.ruoyi.acceptance.mapper.ToWalletMapper;
import com.ruoyi.acceptance.service.IToWalletService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 订单流水Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class ToOrderHistoryServiceImpl extends ServiceImpl<ToOrderHistoryMapper, ToOrderHistory> implements IToOrderHistoryService {

    @Autowired
    private ToOrderHistoryMapper toOrderHistoryMapper;
    @Autowired
    private IToWalletService toWalletService;
    @Autowired
    private ToWalletMapper toWalletMapper;
    @Autowired
    private IToUserService toUserService;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToOrderHistory> queryWrapper(ToOrderHistory toOrderHistory) {
        LambdaQueryWrapper<ToOrderHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderHistory.getOrderId()), ToOrderHistory::getOrderId, toOrderHistory.getOrderId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderHistory.getUserId()), ToOrderHistory::getUserId, toOrderHistory.getUserId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderHistory.getFrozenAmount()), ToOrderHistory::getFrozenAmount, toOrderHistory.getFrozenAmount());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderHistory.getNotWithdrawAmount()), ToOrderHistory::getNotWithdrawAmount, toOrderHistory.getNotWithdrawAmount());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderHistory.getWithdrawnAmount()), ToOrderHistory::getWithdrawnAmount, toOrderHistory.getWithdrawnAmount());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrderHistory.getTotalAmount()), ToOrderHistory::getTotalAmount, toOrderHistory.getTotalAmount());
        return queryWrapper;
    }

    /**
     * 查询订单流水分页
     *
     * @param toOrderHistory 订单流水
     * @return 订单流水
     */
    @Override
    public IPage<ToOrderHistory> pages(ToOrderHistory toOrderHistory, IPage<ToOrderHistory> page)
    {
        return toOrderHistoryMapper.selectPage(page, this.queryWrapper(toOrderHistory));
    }

    /**
     * 查询订单流水列表
     * 
     * @param toOrderHistory 订单流水
     * @return 订单流水
     */
    @Override
    public List<ToOrderHistory> selectList(ToOrderHistory toOrderHistory)
    {
        return toOrderHistoryMapper.selectList(this.queryWrapper(toOrderHistory));
    }

    @Override
    public R<?> distributionReport(String date) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();

        DistributionReportDTO distributionReportDTO = new DistributionReportDTO();
        //获取交易单量
        BigDecimal totalOrderQuantity = toOrderHistoryMapper.select1(date,openId);
//        DistributionReportDTO distributionReportDTO1 = toOrderHistoryMapper.select2(date,openId);
        //2025-6-15,对象copy赋值时，订单数量被空值覆盖导致数据没有正确返回至前端
//        BeanUtils.copyProperties(distributionReportDTO1,distributionReportDTO);
        distributionReportDTO.setTotalOrderQuantity(totalOrderQuantity);
        LambdaQueryWrapper<ToWallet> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ToWallet::getOpenId,openId);
        ToWallet toWallet = toWalletMapper.selectOne(lambdaQueryWrapper);
        distributionReportDTO.setTotalFrozenAmount(toWallet.getFrozenAmount());
        distributionReportDTO.setTotalNotWithdrawAmount(toWallet.getNotWithdrawAmount());
        distributionReportDTO.setTotalWithdrawnAmount(toWallet.getWithdrawnAmount());
        distributionReportDTO.setTotalAmount(toWallet.getTotalAmount());
        //查询邀请人数据量
        BigDecimal inviteUserCountByOpenId = toUserService.getInviteUserCountByOpenId(openId);
        distributionReportDTO.setTotalCustomerQuantity(inviteUserCountByOpenId);
        return R.ok(distributionReportDTO);
    }

    @Override
    public R<?> invitationRanking() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        List<InvitationRankingDTO> results = new ArrayList<>();
        List<InvitationRankingDTO> invitationRankingDTOS = toOrderHistoryMapper.invitationRanking();
        Integer rank =1;
        for (InvitationRankingDTO invitationRankingDTO : invitationRankingDTOS){
            invitationRankingDTO.setRank(rank);
            results.add(invitationRankingDTO);
            rank ++;
        }
        //是否上榜
        boolean isRank = false;
        for (InvitationRankingDTO invitationRankingDTO : invitationRankingDTOS){
            if (openId.equals(invitationRankingDTO.getAgentOpenId())){
                results.add(invitationRankingDTO);
                isRank = true;
            }
        }
        if (!isRank){
            ToUser currentUser = toUserService.selectToUserByOpenId(openId);
            LambdaQueryWrapper<ToUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ToUser::getInviteCode,currentUser.getShareCode());
            long count = toUserService.count(queryWrapper);
            results.add(new InvitationRankingDTO(currentUser.getNickname(),currentUser.getHeadSculpture(),currentUser.getOpenId(), (int) count,null));
        }


        return R.ok(results);
    }

    @Override
    public R<?> commissionRanking() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        List<CommissionRankingDTO> results = new ArrayList<>();
        List<CommissionRankingDTO> commissionRankingDTOS = toOrderHistoryMapper.commissionRanking();
        Integer rank = 1;
        for (CommissionRankingDTO commissionRankingDTO : commissionRankingDTOS){
            commissionRankingDTO.setRank(rank);
            results.add(commissionRankingDTO);
            rank++;
        }
        boolean isRank = false;
        for (CommissionRankingDTO commissionRankingDTO : commissionRankingDTOS){
            if (openId.equals(commissionRankingDTO.getAgentOpenId())){
                results.add(commissionRankingDTO);
                isRank = true;
            }
        }
        if (!isRank){
            ToUser currentUser = toUserService.selectToUserByOpenId(openId);
            LambdaQueryWrapper<ToOrderHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ToOrderHistory::getAgentOpenId, openId)
                    .groupBy(ToOrderHistory::getAgentOpenId)
                    .select(ToOrderHistory::getAgentOpenId, ToOrderHistory::getTotalAmount);
            List<Map<String, Object>> maps = toOrderHistoryMapper.selectMaps(queryWrapper);
            if (!maps.isEmpty()) {
                Map<String, Object> map = maps.get(0);
                BigDecimal totalAmount = (BigDecimal) map.get("totalAmount");
                results.add(new CommissionRankingDTO(currentUser.getNickname(), currentUser.getHeadSculpture(), currentUser.getOpenId(),  totalAmount,null));
            } else {
                results.add(new CommissionRankingDTO(currentUser.getNickname(), currentUser.getHeadSculpture(), currentUser.getOpenId(),  BigDecimal.ZERO,null));
            }
        }
        return R.ok(results);
    }

    @Override
    public R<?> transactionList(String date) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        //通过代理id查询订单
        List<TransactionListDTO> transactionListDTOS = toOrderHistoryMapper.transactionList(date,openId);

        BigDecimal sumFrozenAmount = toOrderHistoryMapper.selectSumFrozenAmount(date,openId);
        TransactionListVO transactionListVO = new TransactionListVO();
        transactionListVO.setTransactionListDTOS(transactionListDTOS);
        transactionListVO.setSelectSumFrozenAmount(sumFrozenAmount);
        return R.ok(transactionListVO);
    }

    @Override
    public void addWxUserToOrderHistory(Long orderId, String customOpenId, String agentOpenId) {
        ToOrderHistory toOrderHistory = new ToOrderHistory();
        toOrderHistory.setOrderId(orderId);
        toOrderHistory.setCustomerOpenId(customOpenId);
        toOrderHistory.setAgentOpenId(agentOpenId);
        baseMapper.insert(toOrderHistory);
    }

}
