<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToGroupBuyingMapper">

    <resultMap type="com.ruoyi.acceptance.dto.GroupInfoDTO" id="getGroupInfoByPaperId">
        <result property="groupId"    column="group_id"    />
        <result property="groupLeader"    column="group_leader"    />
    </resultMap>

    <select id="getGroupInfoByGroupId" parameterType="String" resultType="com.ruoyi.acceptance.vo.GroupInfoVO">
        select
            tgb.group_id groupId,
            tgb.create_time createTime,
            tu.nickname groupLeader,
            tu.head_sculpture groupLeaderHeadSculpture
        from to_group_buying tgb
        left join to_user tu on tu.open_id = tgb.open_id
        where tgb.whether_out_group = 0 and  tgb.group_id = #{groupId}
        ORDER BY tgb.create_time asc
    </select>

    <select id="getCreateTimeByMainOrder" parameterType="String" resultType="Date">
        select
        tgb.create_time createTime
        from to_group_buying tgb
        where tgb.group_main_order = 1 and  tgb.group_id = #{groupId}
    </select>

    <resultMap type="com.ruoyi.acceptance.dto.GroupInfoTwoDTO" id="selectGroupCountdownMap">
        <result property="createTime"    column="create_time"    />
        <result property="groupId"    column="group_id"    />
    </resultMap>
    <select id="selectGroupCountdown" parameterType="long" resultMap="selectGroupCountdownMap">
        SELECT create_time,group_id  from to_group_buying tgb
        where whether_out_group  = 1 and group_id = (
            SELECT group_id from to_group_buying tgb2
            WHERE order_id = #{orderId}
        )
    </select>

    <select id="selectGroupCountdownByOpenId" parameterType="String" resultType="com.ruoyi.acceptance.dto.GroupInfoTwoDTO">
        SELECT create_time,group_id  from to_group_buying tgb
        where open_id = #{openId}  and group_id = #{groupId}
    </select>

    <select id="selectOrderStatusByCroup" resultType="java.lang.Integer">
        SELECT order_pay_status from to_order to2
        WHERE id in (
            SELECT order_id  from to_group_buying tgb
            where group_id = (
                SELECT group_id from to_group_buying tgb2
                WHERE order_id = #{orderId}
            )
        )
    </select>

    <select id="selectOrderStatusByCroupByGroupId" resultType="java.lang.Integer">
        SELECT order_pay_status from to_order to2
            WHERE id in (
                SELECT order_id  from to_group_buying tgb
                where group_id = #{groupId}
        )
    </select>

    <select id="selectHeadSculpture" resultType="com.ruoyi.acceptance.dto.HeadSculptureDTO"  parameterType="Long">
        SELECT tu.head_sculpture src  from to_group_buying tgb
        LEFT JOIN to_user tu ON tgb.open_id = tu.open_id
        where  tgb.whether_out_group = 0 and group_id = (
            SELECT group_id from to_group_buying tgb2
            WHERE order_id = #{orderId}
        )
    </select>

    <select id="selectHeadSculptureByGroupId" resultType="com.ruoyi.acceptance.dto.HeadSculptureDTO"  parameterType="String">
        SELECT tu.head_sculpture src  from to_group_buying tgb
        LEFT JOIN to_user tu ON tgb.open_id = tu.open_id
        WHERE tgb.whether_out_group = 0 and tgb.group_id = #{groupId}
    </select>

    <select id="myGroupOrderList" resultType="com.ruoyi.acceptance.vo.MyGroupOrderVO"  parameterType="String">
        SELECT
            C.id paperId,
            A.group_id groupId,
            A.create_time createTime,
            C.title
        FROM to_group_buying A
        LEFT JOIN to_order B ON A.order_id = B.id
        LEFT JOIN to_paper C ON C.order_id = B.id
        WHERE A.open_id = #{openId}
    </select>
</mapper>