package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 项目类目信息对象 ToProjectCategoryVo
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@ApiModel(value = " ToProjectCategoryVo", description = "项目类目信息对象VO")
public class ToProjectCategoryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 类目编码 */
    @Excel(name = "类目编码")
    @ApiModelProperty(value = "类目编码", name="categoryCode")
    private String categoryCode;

    /** 上级类目编码 */
    @Excel(name = "上级类目编码")
    @ApiModelProperty(value = "上级类目编码", name="parentCode")
    private String parentCode;

    /** 类目名称 */
    @Excel(name = "类目名称")
    @ApiModelProperty(value = "类目名称", name="categoryName")
    private String categoryName;

    /** 类目顺序 */
    @Excel(name = "类目顺序")
    @ApiModelProperty(value = "类目顺序", name="categoryNo")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long categoryNo;

    /** 祖级列表 */
    @Excel(name = "祖级列表")
    @ApiModelProperty(value = "祖级列表", name="ancestors")
    private String ancestors;

}
