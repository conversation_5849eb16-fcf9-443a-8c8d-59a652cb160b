package com.ruoyi.app.controller.user;

import com.ruoyi.acceptance.service.IDoUserService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.DoUser;
import com.ruoyi.common.core.domain.vo.DoUserCardVo;
import com.ruoyi.common.core.domain.vo.DoUserVO;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 派单用户Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Api(tags = "派单用户")
@RestController
@RequestMapping("/wx/dispatch/user")
public class WxDoUserController extends BaseController {
    @Autowired
    private IDoUserService doUserService;

    @ApiOperation("通过openid获取用户信息")
    @GetMapping(value = "/getInfoByOpenId")
    public AjaxResult getInfoByOpenId()
    {
        DoUserVO doUserVO = doUserService.selectToUserByOpenId();
        if (StringUtils.isNotNull(doUserVO)){
            return success(doUserVO);
        }else {
            return error("该用户没有注册");
        }
    }


    @ApiOperation("通过openid获取用户信息")
    @GetMapping(value = "/getInfoById/{id}")
    public AjaxResult getInfoById(@PathVariable("id") Long id)
    {
        DoUserCardVo doUserCardVo = doUserService.selectToUserById(id);
        if (StringUtils.isNotNull(doUserCardVo)){
            return success(doUserCardVo);
        }else {
            return error("该用户没有注册");
        }
    }

    /**
     * 查询派单用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(DoUser doUser)
    {
        startPage();
        List<DoUser> list = doUserService.selectDoUserList(doUser);
        return getDataTable(list);
    }

    /**
     * 导出派单用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @Log(title = "派单用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DoUser doUser)
    {
        List<DoUser> list = doUserService.selectDoUserList(doUser);
        ExcelUtil<DoUser> util = new ExcelUtil<DoUser>(DoUser.class);
        util.exportExcel(response, list, "派单用户数据");
    }

    /**
     * 获取派单用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(doUserService.selectDoUserById(id));
    }

    /**
     * 新增派单用户
     */
    @ApiOperation("新增派单用户")
    @PostMapping
    public R add(@RequestBody DoUser doUser)
    {
        return doUserService.saveDoUser(doUser);
    }

    /**
     * 修改派单用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "派单用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DoUser doUser)
    {
        return toAjax(doUserService.updateDoUser(doUser));
    }

    /**
     * 删除派单用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "派单用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(doUserService.deleteDoUserByIds(ids));
    }
}
