package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToOrderFlow;
import com.ruoyi.app.vo.ToOrderFlowVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 订单流水PoVo转换器
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToOrderFlowConvert extends BeanPoVoMapper<ToOrderFlow, ToOrderFlowVo> {

        ToOrderFlowConvert INSTANCE = Mappers.getMapper(ToOrderFlowConvert.class);

}
