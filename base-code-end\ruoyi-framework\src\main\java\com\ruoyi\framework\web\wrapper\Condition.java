package com.ruoyi.framework.web.wrapper;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.constant.DbConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 分页
 **/
@Slf4j
public class Condition {


    /**
     * 转化成mybatis plus中的Page
     *
     * @param query 查询条件
     * @return IPage
     */
    public static <T> IPage<T> getPage(Query query) {
        Page<T> page = new Page<>(Convert.toLong(query.getPageNum(), DbConstant.DB_PAGE_NUM), Convert.toLong(query.getPageSize(), DbConstant.DB_PAGE_SIZE));
        List<String> orders = new ArrayList<>();
        //分割中英文逗号
        if (StrUtil.isNotBlank(query.getOrderBy())) {
            if (query.getOrderBy().contains(DbConstant.SPLIT_COMMA_EN)) {
                List<String> list = StrUtil.split(query.getOrderBy(), DbConstant.SPLIT_COMMA_EN);
                if (query.getOrderBy().contains(DbConstant.SPLIT_COMMA_CN)) {
                    for (String order : list) {
                        orders.addAll(StrUtil.split(order, DbConstant.SPLIT_COMMA_CN));
                    }
                } else {
                    orders.addAll(list);
                }
            } else if (query.getOrderBy().contains(DbConstant.SPLIT_COMMA_CN)) {
                orders.addAll(StrUtil.split(query.getOrderBy(), DbConstant.SPLIT_COMMA_CN));
            }
            for (String order : orders) {
                List<String> split = StrUtil.split(order, DbConstant.SPLIT_SPACE);
                try {
                    if (split.size() > 1 && StrUtil.equalsIgnoreCase(DbConstant.DB_DESC, split.get(1))) {
                        page.addOrder(OrderItem.desc(cleanIdentifier(split.get(0))));
                    } else {
                        page.addOrder(OrderItem.asc(cleanIdentifier(split.get(0))));
                    }
                } catch (Exception e) {
                    log.error("排序出错，请检查sql语句，错误内容={}", e.getMessage());
                }
            }

        }
        return page;
    }

    /**
     * 获取标识符，用于参数清理
     *
     * @param param 参数
     * @return 清理后的标识符
     */
    @Nullable
    public static String cleanIdentifier(@Nullable String param) {
        if (param == null) {
            return null;
        }
        StringBuilder paramBuilder = new StringBuilder();
        for (int i = 0; i < param.length(); i++) {
            char c = param.charAt(i);
            if (Character.isJavaIdentifierPart(c)) {
                paramBuilder.append(c);
            }
        }
        return paramBuilder.toString();
    }

}
