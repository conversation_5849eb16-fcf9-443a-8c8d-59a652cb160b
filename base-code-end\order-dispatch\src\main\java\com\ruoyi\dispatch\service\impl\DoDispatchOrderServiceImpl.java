package com.ruoyi.dispatch.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.dispatch.mapper.DoDispatchOrderMapper;
import com.ruoyi.dispatch.domain.DoDispatchOrder;
import com.ruoyi.dispatch.service.IDoDispatchOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 派单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
@Service
public class DoDispatchOrderServiceImpl extends ServiceImpl<DoDispatchOrderMapper,DoDispatchOrder> implements IDoDispatchOrderService {

    @Autowired
    private DoDispatchOrderMapper doDispatchOrderMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<DoDispatchOrder> queryWrapper(DoDispatchOrder doDispatchOrder) {
        LambdaQueryWrapper<DoDispatchOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(doDispatchOrder.getProjectId()), DoDispatchOrder::getProjectId, doDispatchOrder.getProjectId());
        queryWrapper.eq(ObjUtil.isNotEmpty(doDispatchOrder.getTakerId()), DoDispatchOrder::getTakerId, doDispatchOrder.getTakerId());
        return queryWrapper;
    }

    /**
     * 查询派单分页
     *
     * @param doDispatchOrder 派单
     * @return 派单
     */
    @Override
    public IPage<DoDispatchOrder> pages(DoDispatchOrder doDispatchOrder, IPage<DoDispatchOrder> page)
    {
        return doDispatchOrderMapper.selectPage(page, this.queryWrapper(doDispatchOrder));
    }

    /**
     * 查询派单列表
     * 
     * @param doDispatchOrder 派单
     * @return 派单
     */
    @Override
    public List<DoDispatchOrder> selectList(DoDispatchOrder doDispatchOrder)
    {
        return doDispatchOrderMapper.selectList(this.queryWrapper(doDispatchOrder));
    }

}
