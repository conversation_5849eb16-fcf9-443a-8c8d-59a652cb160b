package com.ruoyi.dispatch.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 会话对象 chat_conversations
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@TableName("chat_conversations")
public class ChatConversations extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 会话ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long conversationId;

    /** 会话名称 */
    @TableField(value = "conversation_name")
    private String conversationName;

    /** 会话类型(1-私聊,2-群聊) */
    @TableField(value = "conversation_type")
    private Integer conversationType;

}
