package com.ruoyi.api.vo.convert;


import com.ruoyi.api.vo.OssFileVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.system.domain.OssFile;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 文件管理PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OssFileConvert extends BeanPoVoMapper<OssFile, OssFileVo> {
        OssFileConvert INSTANCE = Mappers.getMapper(OssFileConvert.class);

}
