package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**x
 * 微信用户登录标识
 *
 * @author: 天鸣
 * @date: 2024-08-16
 */
@Data
public class ToUserLogin {

    /**
     * 用户唯一标识
     */
    @TableId(type = IdType.NONE)
    private String openid;

    /**
     * 字典码
     */
    private String sessionKey;

    /**
     * 用户在开放平台的唯一标识符
     */
    private String unionid;

    /**
     * 是否授权用户信息（0未授权，1已授权）
     */
    private Integer empowerInfo;

    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
