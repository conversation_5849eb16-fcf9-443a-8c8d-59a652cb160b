package com.ruoyi.dispatchorder.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.dispatch.domain.DoDispatchOrder;
import com.ruoyi.dispatch.service.IDoDispatchOrderService;
import com.ruoyi.dispatchorder.vo.DoDispatchOrderVo;
import com.ruoyi.dispatchorder.vo.convert.DoDispatchOrderConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 派单Manager
 *
 * <AUTHOR>
 * @date 2024-08-27
 */
@Component
public class DoDispatchOrderManager {

    @Autowired
    private IDoDispatchOrderService doDispatchOrderService;

    @Autowired
    private DoDispatchOrderConvert doDispatchOrderConvert;

    /**
    * 分页查询派单
    */
    public IPage<DoDispatchOrderVo> page(DoDispatchOrderVo doDispatchOrderVo, Query query){
        IPage<DoDispatchOrder> page = doDispatchOrderService.pages(doDispatchOrderConvert.voToPo(doDispatchOrderVo), Condition.getPage(query));
        return (IPage<DoDispatchOrderVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<DoDispatchOrderVo>(), DoDispatchOrderConvert.INSTANCE);
    }

    /**
    * 查询派单列表
    */
    public List<DoDispatchOrderVo> list(DoDispatchOrderVo doDispatchOrderVo) {
        List<DoDispatchOrder> doDispatchOrderList = doDispatchOrderService.selectList(doDispatchOrderConvert.voToPo(doDispatchOrderVo));
        return doDispatchOrderConvert.poToVoList(doDispatchOrderList);
    }

    /**
     * 查询派单详细信息
     */
    public DoDispatchOrderVo getInfo(Long id) {
        DoDispatchOrder doDispatchOrder = doDispatchOrderService.getById(id);
        return doDispatchOrderConvert.poToVo(doDispatchOrder);
    }

    /**
     * 新增派单
     */
    public boolean add(DoDispatchOrderVo doDispatchOrderVo) {
        return doDispatchOrderService.save(doDispatchOrderConvert.voToPo(doDispatchOrderVo));
    }

    /**
     * 修改派单
     */
    public boolean edit(DoDispatchOrderVo doDispatchOrderVo) {
        return doDispatchOrderService.updateById(doDispatchOrderConvert.voToPo(doDispatchOrderVo));
    }

    /**
     * 批量删除派单
     */
    public boolean remove(Long[] ids) {
        return doDispatchOrderService.removeByIds(CollUtil.toList(ids));
    }

}
