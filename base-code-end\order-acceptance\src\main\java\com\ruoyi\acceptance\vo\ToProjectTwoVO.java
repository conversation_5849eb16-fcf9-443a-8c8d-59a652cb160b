package com.ruoyi.acceptance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 项目基本信息对象 ToProjectVo
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@ApiModel(value = " ToProjectTwoVO", description = "项目基本信息对象VO")
public class ToProjectTwoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lastId;

    private Integer size;

    private String categorySelectionTwo;

    private String additionalServices;

    private BigDecimal beginPrice;

    private BigDecimal endPrice;

}
