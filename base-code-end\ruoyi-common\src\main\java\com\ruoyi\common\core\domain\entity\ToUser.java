package com.ruoyi.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.PoBaseEntity;
import lombok.Data;

/**
 * 用户信息对象 to_user
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Data
@TableName("to_user")
public class ToUser extends PoBaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @Excel(name = "openId")
    @TableField(value = "open_id")
    private String openId;

    /** 昵称 */
    @Excel(name = "昵称")
    @TableField(value = "nickname")
    private String nickname;

    /** 手机号 */
    @Excel(name = "手机号")
    @TableField(value = "phone_number")
    private String phoneNumber;

    /** 微信号 */
    @Excel(name = "微信号")
    @TableField(value = "wechat_id")
    private String wechatId;

    /** 邀请码 */
    @Excel(name = "邀请码")
    @TableField(value = "invite_code")
    private String inviteCode;

    @Excel(name = "头像")
    @TableField(value = "head_sculpture")
    private String  headSculpture;

    @Excel(name = "分享码")
    @TableField(value = "share_code")
    private String shareCode;

    @Excel(name = "是否代理")
    @TableField(value = "whether_agent")
    private Integer whetherAgent;
}
