## 开发

```bash
# 克隆项目
git clone https://gitee.com/y_project/RuoYi-Vue

# 进入项目目录
cd ruoyi-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

## 问题

### 启动报错 `95% emitting CompressionPlugin ERROR  Error: error:0308010C:digital envelope routines::unsupported`
    
Node.js v17 开始默认不支持 CompressionPlugin 里面的加密算法，可以用 `set NODE_OPTIONS=--openssl-legacy-provider` 手动开启，或者使用 Node.js v16。