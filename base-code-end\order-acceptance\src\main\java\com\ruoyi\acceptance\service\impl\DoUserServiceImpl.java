package com.ruoyi.acceptance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.acceptance.domain.ToOrder;
import com.ruoyi.acceptance.domain.ToProject;
import com.ruoyi.acceptance.domain.ToProjectCategory;
import com.ruoyi.acceptance.dto.ToProjectDTO;
import com.ruoyi.acceptance.mapper.DoUserMapper;
import com.ruoyi.acceptance.service.IDoUserService;
import com.ruoyi.acceptance.service.IToOrderService;
import com.ruoyi.acceptance.service.IToProjectCategoryService;
import com.ruoyi.acceptance.service.IToProjectService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.DoUser;
import com.ruoyi.common.core.domain.model.DispatchLoginUser;
import com.ruoyi.common.core.domain.vo.DoUserCardVo;
import com.ruoyi.common.core.domain.vo.DoUserVO;
import com.ruoyi.common.enums.OrderPayStatusEnum;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.OrderDispatch.CREDIT_SCORE;

/**
 * 派单用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@Slf4j
@Service
public class DoUserServiceImpl extends ServiceImpl<DoUserMapper, DoUser> implements IDoUserService {
    @Autowired
    private DoUserMapper doUserMapper;
    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IToProjectCategoryService categoryService;

    @Autowired
    private IToProjectService projectService;
    @Autowired
    private IToOrderService toOrderService;

    @Override
    public DoUser selectToUserByOpenId(String openId) {
        return doUserMapper.selectToUserByOpenId1(openId);
    }

    @Override
    public DoUserVO selectToUserByOpenId() {
        DispatchLoginUser dispatchLoginUser = SecurityUtils.getDispatchLoginUser();
        String openId= dispatchLoginUser.getOpenid();
        log.info("selectToUserByOpenId===>{}",openId);
        return doUserMapper.selectToUserByOpenId(openId);
    }

    /**
     * 查询派单用户
     * 
     * @param id 派单用户主键
     * @return 派单用户
     */
    @Override
    public DoUser selectDoUserById(Long id)
    {

        return doUserMapper.selectDoUserById(id);
    }

    /**
     * 查询派单用户列表
     * 
     * @param doUser 派单用户
     * @return 派单用户
     */
    @Override
    public List<DoUser> selectDoUserList(DoUser doUser)
    {
        return doUserMapper.selectDoUserList(doUser);
    }

    /**
     * 新增派单用户
     * 
     * @param doUser 派单用户
     * @return 结果
     */
    @Override
    public int insertDoUser(DoUser doUser)
    {
        doUser.setCreateTime(LocalDateTime.now());
        return doUserMapper.insert(doUser);
    }

    /**
     * 修改派单用户
     * 
     * @param doUser 派单用户
     * @return 结果
     */
    @Override
    public int updateDoUser(DoUser doUser)
    {
        doUser.setUpdateTime(LocalDateTime.now());
        return doUserMapper.updateDoUser(doUser);
    }

    /**
     * 批量删除派单用户
     * 
     * @param ids 需要删除的派单用户主键
     * @return 结果
     */
    @Override
    public int deleteDoUserByIds(Long[] ids)
    {
        return doUserMapper.deleteDoUserByIds(ids);
    }

    /**
     * 删除派单用户信息
     * 
     * @param id 派单用户主键
     * @return 结果
     */
    @Override
    public int deleteDoUserById(Long id)
    {
        return doUserMapper.deleteDoUserById(id);
    }

    @Override
    public R saveDoUser(DoUser doUser) {
        log.info("saveDoUser===>{}",doUser);
        DispatchLoginUser dispatchLoginUser = SecurityUtils.getDispatchLoginUser();
        String openId= dispatchLoginUser.getOpenid();
        LambdaQueryWrapper<DoUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DoUser::getOpenId,openId);
        DoUser tempDoUser = doUserMapper.selectOne(queryWrapper);
        doUser.setOpenId(openId);
        String tempCreditScore = sysConfigService.selectConfigByKey(CREDIT_SCORE);
        doUser.setCreditScore(Integer.parseInt(tempCreditScore));
        if(StringUtils.isNotNull(tempDoUser)){
            doUserMapper.updateById(doUser);
            return R.fail("该用户已存在,无须注册");
        }

        doUserMapper.insert(doUser);
        return R.ok();
    }

    @Override
    public DoUserCardVo selectToUserById(Long id) {
        DoUser doUser = baseMapper.selectById(id);
        DoUserCardVo doUserCardVo = new DoUserCardVo();
        BeanUtils.copyProperties(doUser,doUserCardVo);

        // 获取用户创建时间
        LocalDateTime createTime = doUser.getCreateTime();
        if (createTime != null) {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();

            // 转换为LocalDate以便计算年份差
            LocalDate createDate = createTime.toLocalDate();
            LocalDate currentDate = now.toLocalDate();

            // 计算年份差
            int years = Period.between(createDate, currentDate).getYears();

            // 如果有日期差但年份差为0，则按1年计算
            if (years == 0 && currentDate.isAfter(createDate)) {
                years = 1;
            }

            // 将计算结果设置到用户对象
            doUserCardVo.setReceivingOrdersDuration(years);
        }



        handleCategorySelection(doUserCardVo);
        handleUserProject(doUserCardVo);
        //技能标签

        return doUserCardVo;
    }


    /**
     * 处理商家接单记录
     * @param doUserCardVo
     */
    public void handleUserProject(DoUserCardVo doUserCardVo) {
        String openId = doUserCardVo.getOpenId();
        List<ToProject> projectList = projectService.selectListByMerchantOpenId(openId);
        if (CollectionUtils.isEmpty(projectList)) {
            return;
        }
        // 提取项目ID列表
        List<Long> projectIds = projectList.stream()
                .map(ToProject::getId)
                .collect(Collectors.toList());

        // 批量查询订单信息
        Map<String, ToOrder> projectOrderMap = toOrderService.selectByProjectIds(projectIds)
                .stream()
                .collect(Collectors.toMap(ToOrder::getProjectId, order -> order));


        // 转换并组装数据
        List<Map<String, Object>> collect = projectList.stream()
                .map(project -> {

                    Map<String, Object> map = new HashMap<>();
                    // 设置订单价格
                    ToOrder order = projectOrderMap.get(project.getId().toString());
                    if (order != null && order.getTotalPrice() != null && !order.getOrderPayStatus().equals(OrderPayStatusEnum.REFUNDED.getCode())) {
                        map.put("title", project.getTitle());
                        map.put("actual_delivery_time", project.getActualDeliveryTime());
                        map.put("orderPayStatus", OrderPayStatusEnum.getByCode(order.getOrderPayStatus()).getDesc());
                        map.put("totalPrice", order.getTotalPrice());
                    }

                    return map.isEmpty() ? null:map;
                })
                .collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());

        doUserCardVo.setReceivingOrdersCount(collect.size());
        doUserCardVo.setProjectList(collect);
        String createTime = doUserCardVo.getCreateTime();

    }




    /**
     * 处理选择类目
     *
     * @param doUserVo
     */
    public void handleCategorySelection(DoUserCardVo doUserVo) {


        // 批量查询处理
        if (StringUtils.isNotEmpty(doUserVo.getOrderCategoryOne()) || StringUtils.isNotEmpty(doUserVo.getOrderCategoryTwo())) {
            List<String> categoryCodeList = new ArrayList<>();

            if (StringUtils.isNotEmpty(doUserVo.getOrderCategoryOne())) {
                categoryCodeList.add(doUserVo.getOrderCategoryOne());
            }

            if (StringUtils.isNotEmpty(doUserVo.getOrderCategoryTwo())) {
                categoryCodeList.add(doUserVo.getOrderCategoryTwo());
            }

            if (!categoryCodeList.isEmpty()) {
                // 批量查询分类信息
                List<ToProjectCategory> categoryList = categoryService.selectByCateGoryCodes(categoryCodeList);
                Map<String, String> codeToNameMap = categoryList.stream()
                        .collect(Collectors.toMap(ToProjectCategory::getCategoryCode, ToProjectCategory::getCategoryName));
                doUserVo.setCategorySelectionMap(codeToNameMap);

            }
        }

    }




}
