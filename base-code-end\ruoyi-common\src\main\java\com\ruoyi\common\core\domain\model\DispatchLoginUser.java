package com.ruoyi.common.core.domain.model;

import com.ruoyi.common.core.domain.entity.DoUser;
import com.ruoyi.common.core.domain.entity.SysRole;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
@Data
public class DispatchLoginUser {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String openid;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 微信用户信息
     */
    private DoUser doUser;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 角色对象
     */
    private List<SysRole> roles;


    public DispatchLoginUser(String openid, DoUser doUser, Set<String> permissions) {
        this.openid = openid;
        this.doUser = doUser;
        this.permissions = permissions;
    }

}