package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.vo.ToOrderVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.acceptance.domain.ToOrder;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 接单订单PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToOrderConvert extends BeanPoVoMapper<ToOrder, ToOrderVo> {

        ToOrderConvert INSTANCE = Mappers.getMapper(ToOrderConvert.class);

}
