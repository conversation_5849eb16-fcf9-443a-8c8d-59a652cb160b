package com.ruoyi.acceptance.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.domain.ToOrderSub;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.dto.ToOrderSubDTO;

/**
 * 接单订单子订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
public interface IToOrderSubService extends IService<ToOrderSub> {

    /**
     * 分页查询接单订单子订单
     * @param toOrderSub 接单订单子订单
     * @param page 分页条件
     * @return 接单订单子订单集合
     */
    IPage<ToOrderSub> pages(ToOrderSub toOrderSub, IPage<ToOrderSub> page);

    /**
     * 查询接单订单子订单列表
     * 
     * @param toOrderSub 接单订单子订单
     * @return 接单订单子订单集合
     */
     List<ToOrderSub> selectList(ToOrderSub toOrderSub);

    List<ToOrderSubDTO> selectListByOpenId(String openid);
}
