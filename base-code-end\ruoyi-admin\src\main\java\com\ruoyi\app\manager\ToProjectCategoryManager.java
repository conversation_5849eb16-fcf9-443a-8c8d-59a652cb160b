package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.vo.CategoryTreeVo;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.acceptance.service.IToProjectCategoryService;
import com.ruoyi.app.vo.ToProjectCategoryVo;
import com.ruoyi.app.vo.convert.ToProjectCategoryConvert;
import com.ruoyi.acceptance.domain.ToProjectCategory;

import java.util.List;

/**
 * 项目类目信息Manager
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Component
public class ToProjectCategoryManager {

    @Autowired
    private IToProjectCategoryService toProjectCategoryService;

    @Autowired
    private ToProjectCategoryConvert toProjectCategoryConvert;

    /**
    * 分页查询项目类目信息
    */
    public IPage<ToProjectCategoryVo> page(ToProjectCategoryVo toProjectCategoryVo, Query query){
        IPage<ToProjectCategory> page = toProjectCategoryService.pages(toProjectCategoryConvert.voToPo(toProjectCategoryVo), Condition.getPage(query));
        return (IPage<ToProjectCategoryVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToProjectCategoryVo>(), ToProjectCategoryConvert.INSTANCE);
    }

    /**
    * 查询项目类目信息列表
    */
    public List<ToProjectCategoryVo> list(ToProjectCategoryVo toProjectCategoryVo) {
        List<ToProjectCategory> toProjectCategoryList = toProjectCategoryService.selectList(toProjectCategoryConvert.voToPo(toProjectCategoryVo));
        return toProjectCategoryConvert.poToVoList(toProjectCategoryList);
    }

    /**
     * 查询项目类目信息详细信息
     */
    public ToProjectCategoryVo getInfo(Long id) {
        ToProjectCategory toProjectCategory = toProjectCategoryService.getById(id);
        return toProjectCategoryConvert.poToVo(toProjectCategory);
    }

    /**
     * 新增项目类目信息
     */
    public boolean add(ToProjectCategoryVo toProjectCategoryVo) {
        return toProjectCategoryService.save(toProjectCategoryConvert.voToPo(toProjectCategoryVo));
    }

    /**
     * 修改项目类目信息
     */
    public boolean edit(ToProjectCategoryVo toProjectCategoryVo) {
        return toProjectCategoryService.updateById(toProjectCategoryConvert.voToPo(toProjectCategoryVo));
    }

    /**
     * 批量删除项目类目信息
     */
    public boolean remove(Long[] ids) {
        return toProjectCategoryService.removeByIds(CollUtil.toList(ids));
    }

    public List<CategoryTreeVo> getCategoryTree() {
        return toProjectCategoryService.getCategoryTree();

    }
}
