package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单流水对象 ToOrderFlowVo
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Data
@ApiModel(value = " ToOrderFlowVo", description = "订单流水对象VO")
public class ToOrderFlowVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 订单ID */
    @Excel(name = "订单ID")
    @ApiModelProperty(value = "订单ID", name="orderId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /** 流水类型（first_pay定金final_pay尾款） */
    @Excel(name = "流水类型", readConverterExp = "f=irst_pay定金final_pay尾款")
    @ApiModelProperty(value = "流水类型", name="flowType")
    private String flowType;

    /** 商户单号 */
    @Excel(name = "商户单号")
    @ApiModelProperty(value = "商户单号", name="outTradeNo")
    private String outTradeNo;

    /** 交易单号 */
    @Excel(name = "交易单号")
    @ApiModelProperty(value = "交易单号", name="transactionId")
    private String transactionId;

}
