<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="108px">
      <el-form-item label="ID" prop="advancePrice">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="论文名称" prop="advancePrice">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入论文名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="交付时间" prop="deliveryTime">-->
<!--        <el-date-picker clearable-->
<!--          v-model="queryParams.deliveryTime"-->
<!--          type="date"-->
<!--          value-format="yyyy-MM-dd"-->
<!--          placeholder="请选择交付时间">-->
<!--        </el-date-picker>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否申请退款" prop="applyRefund">-->
<!--        <el-select v-model="queryParams.applyRefund" placeholder="选择是否申请退款" clearable @keyup.enter.native="handleQuery">-->
<!--          <el-option label="是" :value="1"></el-option>-->
<!--          <el-option label="否" :value="0"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否团购" prop="groupPurchase">-->
<!--        <el-select v-model="queryParams.groupPurchase" placeholder="请选择是否团购" clearable  @keyup.enter.native="handleQuery">-->
<!--          <el-option label="是" :value="1"></el-option>-->
<!--          <el-option label="否" :value="0"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="是否结清尾款" prop="settlement">-->
<!--        <el-select v-model="queryParams.settlement" placeholder="请选择是否结清尾款" clearable @keyup.enter.native="handleQuery">-->
<!--          <el-option label="是" :value="1"></el-option>-->
<!--          <el-option label="否" :value="0"></el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button style="margin-left: 40px" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['acceptance:order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['acceptance:order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['acceptance:order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['acceptance:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="论文名称" align="center" prop="title" />
      <el-table-column label="订单状态" align="center" prop="orderPayStatus">
        <template slot-scope="scope">
          <span>{{orderStatus[scope.row.orderPayStatus]}}</span>
        </template>

      </el-table-column>
      <el-table-column label="预付款价" align="center" prop="advancePrice" />
      <el-table-column label="尾款" align="center" prop="balancePayment" />
      <el-table-column label="全额价格" align="center" prop="totalPrice" />
      <el-table-column label="定金支付时间" align="center" prop="depositPaymentTime" />
      <el-table-column label="交付时间" align="center" prop="deliveryTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deliveryTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否申请退款" align="center" prop="applyRefund">
        <template slot-scope="scope">
          <span>{{handleStatus(scope.row.applyRefund)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否团购" align="center" prop="groupPurchase">
        <template slot-scope="scope">
          <span>{{handleStatus(scope.row.groupPurchase)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否结清尾款" align="center" prop="settlement">
        <template slot-scope="scope">
          <span>{{handleStatus(scope.row.settlement)}}</span>
        </template>

      </el-table-column>
      <el-table-column label="尾款支付时间" align="center" prop="balancePaymentTime">
        <template slot-scope="scope">
          <span>{{scope.row.balancePaymentTime}}</span>
        </template>

      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="depositPaymentTime" />

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['acceptance:order:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['acceptance:order:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改接单订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="450px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="订单状态" prop="settlement">
          <el-select v-model="form.orderPayStatus" class="w90">
            <el-option :value="0" label="已退款"></el-option>
            <el-option :value="1" label="待付定金"></el-option>
            <el-option :value="2" label="已支付可退定金"></el-option>
            <el-option :value="3" label="已支付不可退定金"></el-option>
            <el-option :value="4" label="尾款待支付"></el-option>
            <el-option :value="5" label="交易结束"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预付款价" prop="advancePrice">
          <el-input-number :min="0" controls-position="right" class="w90" :precision="2" v-model="form.advancePrice" placeholder="请输入预付款价"></el-input-number>
        </el-form-item>
        <el-form-item label="尾款" prop="balancePayment">
          <el-input-number :min="0" controls-position="right" class="w90" :precision="2" v-model="form.balancePayment" placeholder="请输入尾款"></el-input-number>
        </el-form-item>
        <el-form-item label="全额价格" prop="totalPrice">
          <el-input-number :min="0" controls-position="right" class="w90" :precision="2" v-model="form.totalPrice" placeholder="请输入全额价格"></el-input-number>
        </el-form-item>
        <el-form-item label="交付时间" prop="deliveryTime">
          <el-date-picker clearable class="w90"
            v-model="form.deliveryTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择交付时间">
          </el-date-picker>
        </el-form-item>
<!--        <el-form-item label="是否申请退款" prop="applyRefund">-->
<!--          <el-select v-model="form.applyRefund" class="w90">-->
<!--            <el-option label="是" :value="1"></el-option>-->
<!--            <el-option label="否" :value="0"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="是否团购" prop="groupPurchase" >-->
<!--          <el-select v-model="form.groupPurchase" class="w90">-->
<!--            <el-option label="是" :value="1"></el-option>-->
<!--            <el-option label="否" :value="0"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="是否结清尾款" prop="settlement">-->
<!--          <el-select v-model="form.settlement" class="w90">-->
<!--            <el-option label="是" :value="1"></el-option>-->
<!--            <el-option label="否" :value="0"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOrder, getOrder, delOrder, addOrder, updateOrder } from "@/api/acceptance/order";
import {orderStatus} from "@/utils/statusUtils";

export default {
  name: "Order",
  computed: {
    orderStatus() {
      return orderStatus
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 接单订单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        id: "",
        pageNum: 1,
        pageSize: 10,
        paperId: null,
        advancePrice: null,
        balancePayment: null,
        paymentTime: null,
        totalPrice: null,
        deliveryTime: null,
        applyRefund: null,
        groupPurchase: null,
        settlement: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询接单订单列表 */
    getList() {
      this.loading = true;
      listOrder(this.queryParams).then(response => {
        this.orderList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        paperId: null,
        advancePrice: null,
        balancePayment: null,
        paymentTime: null,
        totalPrice: null,
        deliveryTime: null,
        applyRefund: null,
        groupPurchase: null,
        settlement: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    handleStatus(value) {
      const statusList = {
        1: "是",
        0: "否"
      }
      return statusList[value] || "";
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.id = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加接单订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getOrder(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改接单订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除接单订单编号为"' + ids + '"的数据项？').then(function() {
        return delOrder(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('acceptance/order/export', {
        ...this.queryParams
      }, `order_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped>
.w90 {
  width: 95%;
}
/deep/ .el-input-number .el-input__inner {
  text-align: left;
}
</style>
