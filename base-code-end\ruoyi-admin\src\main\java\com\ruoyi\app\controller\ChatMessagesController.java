package com.ruoyi.app.controller;

import com.ruoyi.acceptance.vo.ChatMessagesTwoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.app.vo.ChatMessagesVo;
import com.ruoyi.app.manager.ChatMessagesManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 消息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Api(tags = "消息管理")
@RestController
@RequestMapping("/dispatch/messages")
public class ChatMessagesController {

    @Autowired
    private ChatMessagesManager chatMessagesManager;

    /**
     * 分页查询消息
     */
    @ApiOperation("分页查询消息")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('dispatch:messages:list')")
    @GetMapping("/list")
    public R<IPage<ChatMessagesVo>> list(ChatMessagesVo chatMessagesVo, Query query)
    {
        return R.ok(chatMessagesManager.page(chatMessagesVo, query));
    }

    /**
    * 查询消息全部列表
    */
    @ApiOperation("查询消息全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('dispatch:messages:list')")
    @GetMapping("/allList")
    public R<List<ChatMessagesVo>> allList(ChatMessagesVo chatMessagesVo){
        return R.ok(chatMessagesManager.list(chatMessagesVo));
    }

    @ApiOperation("商户端消息列表")
    @GetMapping("/merchantMessageList")
    public R<List<ChatMessagesTwoVO>> merchantMessageList(){
        return R.ok(chatMessagesManager.merchantMessageList());
    }

    @ApiOperation("客户端消息列表")
    @GetMapping("/customerMessageList")
    public R<List<ChatMessagesTwoVO>> customerMessageList(){
        return R.ok(chatMessagesManager.customerMessageList());
    }

    /**
     * 导出消息列表
     */
    @ApiOperation("导出消息列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('dispatch:messages:export')")
    @Log(title = "消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChatMessagesVo chatMessagesVo, Query query)
    {
        List<ChatMessagesVo> list = chatMessagesManager.page(chatMessagesVo, query).getRecords();
        ExcelUtil<ChatMessagesVo> util = new ExcelUtil<ChatMessagesVo>(ChatMessagesVo.class);
        util.exportExcel(response, list, "消息数据");
    }

    /**
     * 获取消息详细信息
     */
    @ApiOperation("获取消息详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "messageId", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:messages:query')")
    @GetMapping(value = "/{messageId}")
    public R<ChatMessagesVo> getInfo(@PathVariable("messageId") Long messageId)
    {
        return R.ok(chatMessagesManager.getInfo(messageId));
    }

    /**
     * 新增消息
     */
    @ApiOperation("新增消息")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('dispatch:messages:add')")
    @Log(title = "消息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ChatMessagesVo chatMessagesVo)
    {
        return chatMessagesManager.add(chatMessagesVo) ? R.ok() : R.fail();
    }

    /**
     * 修改消息
     */
    @ApiOperation("修改消息")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('dispatch:messages:edit')")
    @Log(title = "消息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ChatMessagesVo chatMessagesVo)
    {
        return chatMessagesManager.edit(chatMessagesVo) ? R.ok() : R.fail();
    }

    /**
     * 删除消息
     */
    @ApiOperation("删除消息")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "messageIds", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('dispatch:messages:remove')")
    @Log(title = "消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{messageIds}")
    public R<?> remove(@PathVariable Long[] messageIds)
    {
        return chatMessagesManager.remove(messageIds) ? R.ok() : R.fail();
    }
}
