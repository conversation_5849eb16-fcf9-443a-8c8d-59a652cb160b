package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 消息状态对象 ChatMessageStatusVo
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@ApiModel(value = " ChatMessageStatusVo", description = "消息状态对象VO")
public class ChatMessageStatusVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 消息ID */
    @Excel(name = "消息ID")
    @NotNull(message = "消息ID显示顺序不能为空")
    @ApiModelProperty(value = "消息ID", name="messageId", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long messageId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @NotNull(message = "用户ID显示顺序不能为空")
    @ApiModelProperty(value = "用户ID", name="userId", required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 是否已读(0-未读,1-已读) */
    @Excel(name = "是否已读(0-未读,1-已读)")
    @ApiModelProperty(value = "是否已读(0-未读,1-已读)", name="isRead")
    private Integer isRead;

    /** 阅读时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "阅读时间", name="readTime")
    private Date readTime;

}
