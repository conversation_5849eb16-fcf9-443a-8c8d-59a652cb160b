package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.acceptance.service.IToWalletService;
import com.ruoyi.app.vo.ToWalletVo;
import com.ruoyi.app.vo.convert.ToWalletConvert;
import com.ruoyi.acceptance.domain.ToWallet;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包Manager
 *
 * <AUTHOR>
 * @date 2024-08-18
 */
@Component
public class ToWalletManager {

    @Autowired
    private IToWalletService toWalletService;

    @Autowired
    private ToWalletConvert toWalletConvert;

    /**
    * 分页查询钱包
    */
    public IPage<ToWalletVo> page(ToWalletVo toWalletVo, Query query){
        IPage<ToWallet> page = toWalletService.pages(toWalletConvert.voToPo(toWalletVo), Condition.getPage(query));
        return (IPage<ToWalletVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToWalletVo>(), ToWalletConvert.INSTANCE);
    }

    /**
    * 查询钱包列表
    */
    public List<ToWalletVo> list(ToWalletVo toWalletVo) {
        List<ToWallet> toWalletList = toWalletService.selectList(toWalletConvert.voToPo(toWalletVo));
        return toWalletConvert.poToVoList(toWalletList);
    }

    /**
     * 查询钱包详细信息
     */
    public ToWalletVo getInfo(Long id) {
        ToWallet toWallet = toWalletService.getById(id);
        return toWalletConvert.poToVo(toWallet);
    }

    public ToWalletVo getInfoByOpenId() {
        ToWallet toWallet = toWalletService.getInfoByOpenId();
        return toWalletConvert.poToVo(toWallet);
    }

    /**
     * 新增钱包
     */
    public boolean add(ToWalletVo toWalletVo) {
        return toWalletService.save(toWalletConvert.voToPo(toWalletVo));
    }

    /**
     * 修改钱包
     */
    public boolean edit(ToWalletVo toWalletVo) {
        return toWalletService.updateById(toWalletConvert.voToPo(toWalletVo));
    }

    /**
     * 批量删除钱包
     */
    public boolean remove(Long[] ids) {
        return toWalletService.removeByIds(CollUtil.toList(ids));
    }

    public R<?> withdrawal(BigDecimal withdrawalAmount) {
        return toWalletService.withdrawal(withdrawalAmount);
    }
}
