<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ToUserLoginMapper">

    <resultMap type="com.ruoyi.common.core.domain.entity.ToUserLogin" id="ToUserResult">
        <result property="openid" column="openid"/>
        <result property="sessionKey" column="session_key"/>
        <result property="unionid" column="unionid"/>
        <result property="empowerInfo" column="empower_info"/>
        <result property="status" column="status"/>
        <result property="loginDate" column="login_date"/>
        <result property="loginIp" column="login_ip"/>
    </resultMap>

    <sql id="selectToUserLoginVo">
        select openid, session_key, unionid, empower_info, status, login_date, login_ip
        from to_user_login
    </sql>


    <select id="getById" parameterType="String" resultMap="ToUserResult">
        <include refid="selectToUserLoginVo"/>
        where openid = #{openid}
    </select>


    <insert id="save" parameterType="com.ruoyi.common.core.domain.entity.ToUserLogin" useGeneratedKeys="true" keyProperty="id">
        insert into to_user_login
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="sessionKey != null and sessionKey != ''">session_key,</if>
            <if test="unionid != null and unionid != ''">unionid,</if>
            <if test="empowerInfo != null and empowerInfo != ''">empower_info,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="loginIp != null and loginIp != ''">login_ip,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="sessionKey != null and sessionKey != ''">#{sessionKey},</if>
            <if test="unionid != null and unionid != ''">#{unionid},</if>
            <if test="empowerInfo != null and empowerInfo != ''">#{empowerInfo},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="loginIp != null and loginIp != ''">#{loginIp},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.ruoyi.common.core.domain.entity.ToUserLogin">
        update to_user_login
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="sessionKey != null and sessionKey != ''">session_key = #{sessionKey},</if>
            <if test="unionid != null and unionid != ''">unionid = #{unionid},</if>
            <if test="empowerInfo != null and empowerInfo != ''">empower_info = #{empowerInfo},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
        </trim>
        where openid = #{openid}
    </update>

</mapper>