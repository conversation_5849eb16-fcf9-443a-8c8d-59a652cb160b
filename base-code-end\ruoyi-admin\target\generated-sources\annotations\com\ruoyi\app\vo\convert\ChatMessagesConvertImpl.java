package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatMessagesVo;
import com.ruoyi.dispatch.domain.ChatMessages;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatMessagesConvertImpl implements ChatMessagesConvert {

    @Override
    public ChatMessagesVo poToVo(ChatMessages arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatMessagesVo chatMessagesVo = new ChatMessagesVo();

        chatMessagesVo.setContent( arg0.getContent() );
        chatMessagesVo.setContentType( arg0.getContentType() );
        chatMessagesVo.setConversationId( arg0.getConversationId() );
        chatMessagesVo.setFileSize( arg0.getFileSize() );
        chatMessagesVo.setFileUrl( arg0.getFileUrl() );
        chatMessagesVo.setIsRecalled( arg0.getIsRecalled() );
        chatMessagesVo.setMessageId( arg0.getMessageId() );
        if ( arg0.getSenderId() != null ) {
            chatMessagesVo.setSenderId( Long.parseLong( arg0.getSenderId() ) );
        }

        return chatMessagesVo;
    }

    @Override
    public List<ChatMessagesVo> poToVoList(List<ChatMessages> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatMessagesVo> list = new ArrayList<ChatMessagesVo>( arg0.size() );
        for ( ChatMessages chatMessages : arg0 ) {
            list.add( poToVo( chatMessages ) );
        }

        return list;
    }

    @Override
    public ChatMessages voToPo(ChatMessagesVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatMessages chatMessages = new ChatMessages();

        chatMessages.setContent( arg0.getContent() );
        chatMessages.setContentType( arg0.getContentType() );
        chatMessages.setConversationId( arg0.getConversationId() );
        chatMessages.setFileSize( arg0.getFileSize() );
        chatMessages.setFileUrl( arg0.getFileUrl() );
        chatMessages.setIsRecalled( arg0.getIsRecalled() );
        chatMessages.setMessageId( arg0.getMessageId() );
        if ( arg0.getSenderId() != null ) {
            chatMessages.setSenderId( String.valueOf( arg0.getSenderId() ) );
        }

        return chatMessages;
    }

    @Override
    public List<ChatMessages> voToPoList(List<ChatMessagesVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatMessages> list = new ArrayList<ChatMessages>( arg0.size() );
        for ( ChatMessagesVo chatMessagesVo : arg0 ) {
            list.add( voToPo( chatMessagesVo ) );
        }

        return list;
    }
}
