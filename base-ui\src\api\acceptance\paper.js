import request from '@/utils/request'

// 查询论文管理列表
export function listPaper(query) {
  return request({
    url: '/acceptance/paper/list',
    method: 'get',
    params: query
  })
}

// 查询论文管理详细
export function getPaper(id) {
  return request({
    url: '/acceptance/paper/' + id,
    method: 'get'
  })
}

// 新增论文管理
export function addPaper(data) {
  return request({
    url: '/acceptance/paper',
    method: 'post',
    data: data
  })
}

// 修改论文管理
export function updatePaper(data) {
  return request({
    url: '/acceptance/paper',
    method: 'put',
    data: data
  })
}

// 删除论文管理
export function delPaper(id,orderIds) {
  return request({
    url: '/acceptance/paper/' + id + "/" + orderIds,
    method: 'delete'
  })
}

export function getUserList(key) {
  if(key == ''){
    key = "1"
  }
  return request({
    url: '/user/getUserList/' + key,
    method: 'get'
  })
}
