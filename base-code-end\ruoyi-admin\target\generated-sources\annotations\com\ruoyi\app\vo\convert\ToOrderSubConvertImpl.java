package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToOrderSub;
import com.ruoyi.app.vo.ToOrderSubVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToOrderSubConvertImpl implements ToOrderSubConvert {

    @Override
    public ToOrderSubVo poToVo(ToOrderSub arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderSubVo toOrderSubVo = new ToOrderSubVo();

        toOrderSubVo.setApplyRefund( arg0.getApplyRefund() );
        toOrderSubVo.setDepositPaymentTime( arg0.getDepositPaymentTime() );
        toOrderSubVo.setId( arg0.getId() );
        toOrderSubVo.setOrderPayStatus( arg0.getOrderPayStatus() );
        toOrderSubVo.setOutTradeNo( arg0.getOutTradeNo() );
        toOrderSubVo.setProjectId( arg0.getProjectId() );
        toOrderSubVo.setRequirementDescription( arg0.getRequirementDescription() );
        toOrderSubVo.setTitle( arg0.getTitle() );
        toOrderSubVo.setTotalPrice( arg0.getTotalPrice() );
        toOrderSubVo.setTransactionId( arg0.getTransactionId() );

        return toOrderSubVo;
    }

    @Override
    public List<ToOrderSubVo> poToVoList(List<ToOrderSub> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderSubVo> list = new ArrayList<ToOrderSubVo>( arg0.size() );
        for ( ToOrderSub toOrderSub : arg0 ) {
            list.add( poToVo( toOrderSub ) );
        }

        return list;
    }

    @Override
    public ToOrderSub voToPo(ToOrderSubVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderSub toOrderSub = new ToOrderSub();

        toOrderSub.setApplyRefund( arg0.getApplyRefund() );
        toOrderSub.setDepositPaymentTime( arg0.getDepositPaymentTime() );
        toOrderSub.setId( arg0.getId() );
        toOrderSub.setOrderPayStatus( arg0.getOrderPayStatus() );
        toOrderSub.setOutTradeNo( arg0.getOutTradeNo() );
        toOrderSub.setProjectId( arg0.getProjectId() );
        toOrderSub.setRequirementDescription( arg0.getRequirementDescription() );
        toOrderSub.setTitle( arg0.getTitle() );
        toOrderSub.setTotalPrice( arg0.getTotalPrice() );
        toOrderSub.setTransactionId( arg0.getTransactionId() );

        return toOrderSub;
    }

    @Override
    public List<ToOrderSub> voToPoList(List<ToOrderSubVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderSub> list = new ArrayList<ToOrderSub>( arg0.size() );
        for ( ToOrderSubVo toOrderSubVo : arg0 ) {
            list.add( voToPo( toOrderSubVo ) );
        }

        return list;
    }
}
