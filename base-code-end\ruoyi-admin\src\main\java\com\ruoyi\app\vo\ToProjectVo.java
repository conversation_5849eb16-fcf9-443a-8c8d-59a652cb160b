package com.ruoyi.app.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.api.vo.OssFileVo;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目基本信息对象 ToProjectVo
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@ApiModel(value = " ToProjectVo", description = "项目基本信息对象VO")
public class ToProjectVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 订单id */
    @Excel(name = "订单id")
    @ApiModelProperty(value = "订单id", name="orderId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /** 标题 */
    @Excel(name = "标题")
    @ApiModelProperty(value = "标题", name="title")
    private String title;

    /** 选择类目， */
    @Excel(name = "选择类目，1级")
    @ApiModelProperty(value = "选择类目，按逗号分隔", name="categorySelectionOne")
    private String categorySelectionOne;

    /** 选择类目 */
    @Excel(name = "选择类目，2级")
    @ApiModelProperty(value = "选择类目，按逗号分隔", name="categorySelectionTwo")
    private String categorySelectionTwo;

    /** 需求描述 */
    @Excel(name = "需求描述")
    @ApiModelProperty(value = "需求描述", name="requirementDescription")
    private String requirementDescription;

    /** 追加服务 */
    @Excel(name = "追加服务")
    @ApiModelProperty(value = "追加服务", name="additionalServices")
    private String additionalServices;

    /** 团购成员（用户信息表id关联） */
    @Excel(name = "团购成员", readConverterExp = "用=户信息表id关联")
    @ApiModelProperty(value = "团购成员", name="groupMembers")
    private String groupMembers;

    /** 客户openId */
    @Excel(name = "客户openId")
    @ApiModelProperty(value = "客户openId", name="customerOpenid")
    private String customerOpenid;

    /** 商户openId */
    @Excel(name = "商户openId")
    @ApiModelProperty(value = "商户openId", name="merchantOpenid")
    private String merchantOpenid;

    /** 是否非卖品（1为是0为否） */
    @Excel(name = "是否非卖品", readConverterExp = "1=为是0为否")
    @ApiModelProperty(value = "是否非卖品", name="isNotForSale")
    private Integer isNotForSale;

    /** 开始时间，双方确认交易时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间，双方确认交易时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间，双方确认交易时间", name="startTime")
    private Date startTime;

    /** 期望交付时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "期望交付时间", name="expectedDeliveryTime")
    private Date expectedDeliveryTime;

    /** 实际交付时间，项目完成时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际交付时间，项目完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "实际交付时间，项目完成时间", name="actualDeliveryTime")
    private Date actualDeliveryTime;

    /** 项目结束时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "项目结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "项目结束时间", name="projectEndTime")
    private Date projectEndTime;

    /** 附件 */
    @Excel(name = "附件")
    @ApiModelProperty(value = "附件", name="attachments")
    private String attachments;

    private String totalPrice;
    /**
     * 附件信息列表（非数据库字段，仅用于前端展示）
     */
    private List<OssFileVo> attachmentList;

    /**
     * 选择类目中文名（非数据库字段，仅用于前端展示）
     */

    private Map<String,String> categorySelectionMap;

    private Map<String,String> additionalServicesMap;

    private Integer orderPayStatus;



}
