<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.acceptance.mapper.ToPaperMapper">

    <select id="selectToPapers" resultType="com.ruoyi.acceptance.dto.ToPaperDTO">
        select
        tp.id id,
        tp.order_id orderId,
        tp.title title,
        tp.technical_selection technicalSelection,
        tp.including_services includingServices,
        tp.introduction introduction,
        tp.buy_members buyMembers,
        tp.custom custom,
        to2.advance_price advancePrice,
        to2.balance_payment balancePayment,
        to2.deposit_payment_time  depositPaymentTime,
        to2.total_price  totalPrice,
        to2.delivery_time  deliveryTime,
        to2.apply_refund  applyRefund,
        to2.group_purchase  groupPurchase,
        to2.settlement  settlement,
        to2.order_pay_status orderPayStatus,
        to2.balance_payment_time balancePaymentTime
        from to_paper tp
        left join to_order to2 on tp.order_id = to2.id
        where 1 = 1
        <if test="toPaperVo.title != null and toPaperVo.title != ''">
            AND tp.title LIKE CONCAT('%', #{toPaperVo.title}, '%')
        </if>
        <if test="toPaperVo.id != null and toPaperVo.id != ''">
            AND tp.id = #{toPaperVo.id}
        </if>
        <if test="toPaperVo.custom != null and toPaperVo.custom != ''">
            AND tp.custom = #{toPaperVo.custom}
        </if>
        order by tp.create_time desc
    </select>

    <select id="getToPaperDtoById" resultType="com.ruoyi.acceptance.dto.ToPaperDTO">
        select
        tp.id id,
        tp.order_id orderId,
        tp.title title,
        tp.technical_selection technicalSelection,
        tp.including_services includingServices,
        tp.introduction introduction,
        tp.buy_members buyMembers,
        tp.custom custom,
        tp.create_time createTime,
        tp.not_sale notSale,
        to2.advance_price advancePrice,
        to2.balance_payment balancePayment,
        to2.deposit_payment_time  depositPaymentTime,
        to2.total_price  totalPrice,
        to2.delivery_time  deliveryTime,
        to2.apply_refund  applyRefund,
        to2.group_purchase  groupPurchase,
        to2.settlement  settlement,
        to2.order_pay_status orderPayStatus,
        to2.balance_payment_time balancePaymentTime
        from to_paper tp
        left join to_order to2 on tp.order_id = to2.id
        where tp.id = #{id}
    </select>

    <select id="tradedList" resultType="com.ruoyi.acceptance.dto.ToPaperDTO">
        select
            tp.id id,
            tp.order_id orderId,
            tp.title title,
            tp.technical_selection technicalSelection,
            tp.including_services includingServices,
            tp.introduction introduction,
            tp.buy_members buyMembers,
            tp.custom custom,
            to2.advance_price advancePrice,
            to2.balance_payment balancePayment,
            to2.deposit_payment_time  depositPaymentTime,
            to2.total_price  totalPrice,
            to2.delivery_time  deliveryTime,
            to2.apply_refund  applyRefund,
            to2.group_purchase  groupPurchase,
            to2.settlement  settlement,
            to2.order_pay_status orderPayStatus,
            to2.balance_payment_time balancePaymentTime
        from to_paper tp
        left join to_order to2 on tp.order_id = to2.id
        where to2.order_pay_status = 5
        order by tp.create_time desc
    </select>

    <select id="obligationList" resultType="com.ruoyi.acceptance.dto.ToPaperDTO">
        select
        tp.id id,
        tp.order_id orderId,
        tp.title title,
        tp.technical_selection technicalSelection,
        tp.including_services includingServices,
        tp.introduction introduction,
        tp.buy_members buyMembers,
        tp.custom custom,
        to2.advance_price advancePrice,
        to2.balance_payment balancePayment,
        to2.deposit_payment_time  depositPaymentTime,
        to2.total_price  totalPrice,
        to2.delivery_time  deliveryTime,
        to2.apply_refund  applyRefund,
        to2.group_purchase  groupPurchase,
        to2.settlement  settlement,
        to2.order_pay_status orderPayStatus,
        to2.balance_payment_time balancePaymentTime
        from to_paper tp
        left join to_order to2 on tp.order_id = to2.id
        where to2.order_pay_status = 3 or to2.order_pay_status = 1
        order by tp.create_time desc
    </select>

    <select id="orderStatusList" resultType="com.ruoyi.acceptance.dto.ToPaperDTO">
        select
            tp.id id,
            tp.order_id orderId,
            tp.title title,
            tp.technical_selection technicalSelection,
            tp.including_services includingServices,
            tp.introduction introduction,
            tp.buy_members buyMembers,
            tp.create_time createTime,
            to2.advance_price advancePrice,
            to2.balance_payment balancePayment,
            to2.deposit_payment_time  depositPaymentTime,
            to2.total_price  totalPrice,
            to2.apply_refund  applyRefund,
            to2.group_purchase  groupPurchase,
            to2.settlement  settlement,
            to2.order_pay_status orderPayStatus,
            to2.balance_payment_time balancePaymentTime
        from to_paper tp
        left join to_order to2 on tp.order_id = to2.id
        where 1=1
        AND tp.not_sale != 1
        AND tp.custom = #{openId}
        <if test="paperName != null and paperName != ''">
            and CONCAT(IFNULL(tp.title, ''), IFNULL(tp.id, '')) LIKE CONCAT('%', #{paperName}, '%')
        </if>
        <if test="status == 'status_all'">
        </if>
        <if test="status == 'status_wait_pay'">
            AND to2.order_pay_status in (1,4)
        </if>
        <if test="status == 'status_wait_deliver'">
            AND to2.order_pay_status in (2,3)
        </if>
        <if test="status == 'status_end'">
            AND to2.order_pay_status in (5,0)
        </if>
        order by tp.create_time desc
    </select>

    <select id="orderStatusListForGroup" resultType="com.ruoyi.acceptance.dto.ToPaperDTO">
        select
            tp.id id,
            tp.order_id orderId,
            tp.title title,
            tp.technical_selection technicalSelection,
            tp.including_services includingServices,
            tp.introduction introduction,
            tp.buy_members buyMembers,
            tp.create_time createTime,
            to2.advance_price advancePrice,
            to2.balance_payment balancePayment,
            to2.deposit_payment_time  depositPaymentTime,
            to2.total_price  totalPrice,
            to2.delivery_time  deliveryTime,
            to2.apply_refund  applyRefund,
            to2.group_purchase  groupPurchase,
            to2.settlement  settlement,
            to2.order_pay_status orderPayStatus,
            to2.balance_payment_time balancePaymentTime
        from to_paper tp
        left join to_order to2 on tp.order_id = to2.id
        where 1=1
        AND tp.not_sale != 1
        AND tp.custom = #{openId}
        AND to2.order_pay_status in (1,2,3)
        AND tp.order_id not in (select order_id from to_group_buying where open_id = #{openId})
        order by tp.create_time DESC
    </select>

    <select id="notSaleList" resultType="com.ruoyi.acceptance.dto.ToPaperDTO">
        select
        tp.id id,
        tp.order_id orderId,
        tp.title title,
        tp.technical_selection technicalSelection,
        tp.including_services includingServices,
        tp.introduction introduction,
        tp.buy_members buyMembers,
        tp.custom custom,
        tp.not_sale notSale,
        to2.advance_price advancePrice,
        to2.balance_payment balancePayment,
        to2.deposit_payment_time  depositPaymentTime,
        to2.total_price  totalPrice,
        to2.apply_refund  applyRefund,
        to2.group_purchase  groupPurchase,
        to2.settlement  settlement,
        to2.order_pay_status orderPayStatus,
        to2.balance_payment_time balancePaymentTime
        from to_paper tp
        left join to_order to2 on tp.order_id = to2.id
        where tp.not_sale = 1
        order by tp.create_time desc
    </select>
</mapper>