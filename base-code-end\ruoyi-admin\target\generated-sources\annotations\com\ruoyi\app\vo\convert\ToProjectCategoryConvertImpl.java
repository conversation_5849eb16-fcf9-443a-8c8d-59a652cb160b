package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToProjectCategory;
import com.ruoyi.app.vo.ToProjectCategoryVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToProjectCategoryConvertImpl implements ToProjectCategoryConvert {

    @Override
    public ToProjectCategoryVo poToVo(ToProjectCategory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToProjectCategoryVo toProjectCategoryVo = new ToProjectCategoryVo();

        toProjectCategoryVo.setAncestors( arg0.getAncestors() );
        toProjectCategoryVo.setCategoryCode( arg0.getCategoryCode() );
        toProjectCategoryVo.setCategoryName( arg0.getCategoryName() );
        toProjectCategoryVo.setCategoryNo( arg0.getCategoryNo() );
        toProjectCategoryVo.setId( arg0.getId() );
        toProjectCategoryVo.setParentCode( arg0.getParentCode() );

        return toProjectCategoryVo;
    }

    @Override
    public List<ToProjectCategoryVo> poToVoList(List<ToProjectCategory> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToProjectCategoryVo> list = new ArrayList<ToProjectCategoryVo>( arg0.size() );
        for ( ToProjectCategory toProjectCategory : arg0 ) {
            list.add( poToVo( toProjectCategory ) );
        }

        return list;
    }

    @Override
    public ToProjectCategory voToPo(ToProjectCategoryVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToProjectCategory toProjectCategory = new ToProjectCategory();

        toProjectCategory.setAncestors( arg0.getAncestors() );
        toProjectCategory.setCategoryCode( arg0.getCategoryCode() );
        toProjectCategory.setCategoryName( arg0.getCategoryName() );
        toProjectCategory.setCategoryNo( arg0.getCategoryNo() );
        toProjectCategory.setId( arg0.getId() );
        toProjectCategory.setParentCode( arg0.getParentCode() );

        return toProjectCategory;
    }

    @Override
    public List<ToProjectCategory> voToPoList(List<ToProjectCategoryVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToProjectCategory> list = new ArrayList<ToProjectCategory>( arg0.size() );
        for ( ToProjectCategoryVo toProjectCategoryVo : arg0 ) {
            list.add( voToPo( toProjectCategoryVo ) );
        }

        return list;
    }
}
