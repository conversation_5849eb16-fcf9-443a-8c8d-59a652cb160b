package com.ruoyi.acceptance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.vo.ToUserVO;
import com.ruoyi.common.core.domain.vo.TransactionUserVO;
import com.ruoyi.common.core.domain.entity.ToUser;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-23
 */
public interface IToUserService extends IService<ToUser>
{
    /**
     * 查询用户信息
     *
     * @param id 用户信息主键
     * @return 用户信息
     */
    public ToUser selectToUserById(Long id);
    public ToUserVO selectToUserByOpenId();
    public ToUser selectToUserByOpenId(String openId);
    public ToUser selectShareUserByInvitedUserOpenId(String openId);

    /**
     * 查询用户信息列表
     *
     * @param toUser 用户信息
     * @return 用户信息集合
     */
    public List<ToUser> selectToUserList(ToUser toUser);

    public List<ToUser> inviteCustomerList();

    public List<TransactionUserVO> transactionCustomerList();

    /**
     * web端新增用户信息
     *
     * @param toUser 用户信息
     * @return 结果
     */
    public R<?> insertToUserForPc(ToUser toUser);

    /**
     * 移动端新增用户信息
     *
     * @param toUser 用户信息
     * @return 结果
     */
    public R<?> insertToUserForWx(ToUser toUser);

    /**
     * 修改用户信息
     *
     * @param toUser 用户信息
     * @return 结果
     */
    public int updateToUser(ToUser toUser);

    /**
     * 批量删除用户信息
     *
     * @param ids 需要删除的用户信息主键集合
     * @return 结果
     */
    public int deleteToUserByIds(Integer[] ids);

    /**
     * 删除用户信息信息
     *
     * @param id 用户信息主键
     * @return 结果
     */
    public int deleteToUserById(Integer id);

    List<ToUser> getUserList(String phoneNumber);


    public BigDecimal getInviteUserCountByOpenId(String openId);
}
