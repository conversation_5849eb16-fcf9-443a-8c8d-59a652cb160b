package com.ruoyi.acceptance.mapper;
import com.ruoyi.acceptance.domain.ToWalletHistory;
import com.ruoyi.acceptance.dto.WithdrawalListDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 钱包流水Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Repository
public interface ToWalletHistoryMapper extends BaseMapper<ToWalletHistory> {

    List<WithdrawalListDTO > withdrawalList(@Param("openId") String openId, @Param("yearMonth") String yearMonth);

}
