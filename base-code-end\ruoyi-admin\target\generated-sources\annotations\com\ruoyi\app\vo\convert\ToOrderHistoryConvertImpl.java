package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToOrderHistory;
import com.ruoyi.app.vo.ToOrderHistoryVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToOrderHistoryConvertImpl implements ToOrderHistoryConvert {

    @Override
    public ToOrderHistoryVo poToVo(ToOrderHistory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderHistoryVo toOrderHistoryVo = new ToOrderHistoryVo();

        toOrderHistoryVo.setFrozenAmount( arg0.getFrozenAmount() );
        toOrderHistoryVo.setId( arg0.getId() );
        toOrderHistoryVo.setNotWithdrawAmount( arg0.getNotWithdrawAmount() );
        toOrderHistoryVo.setOrderId( arg0.getOrderId() );
        toOrderHistoryVo.setTotalAmount( arg0.getTotalAmount() );
        toOrderHistoryVo.setWithdrawnAmount( arg0.getWithdrawnAmount() );

        return toOrderHistoryVo;
    }

    @Override
    public List<ToOrderHistoryVo> poToVoList(List<ToOrderHistory> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderHistoryVo> list = new ArrayList<ToOrderHistoryVo>( arg0.size() );
        for ( ToOrderHistory toOrderHistory : arg0 ) {
            list.add( poToVo( toOrderHistory ) );
        }

        return list;
    }

    @Override
    public ToOrderHistory voToPo(ToOrderHistoryVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToOrderHistory toOrderHistory = new ToOrderHistory();

        toOrderHistory.setFrozenAmount( arg0.getFrozenAmount() );
        toOrderHistory.setId( arg0.getId() );
        toOrderHistory.setNotWithdrawAmount( arg0.getNotWithdrawAmount() );
        toOrderHistory.setOrderId( arg0.getOrderId() );
        toOrderHistory.setTotalAmount( arg0.getTotalAmount() );
        toOrderHistory.setWithdrawnAmount( arg0.getWithdrawnAmount() );

        return toOrderHistory;
    }

    @Override
    public List<ToOrderHistory> voToPoList(List<ToOrderHistoryVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToOrderHistory> list = new ArrayList<ToOrderHistory>( arg0.size() );
        for ( ToOrderHistoryVo toOrderHistoryVo : arg0 ) {
            list.add( voToPo( toOrderHistoryVo ) );
        }

        return list;
    }
}
