package com.ruoyi.app.controller.order;

import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.R;

import javax.validation.Valid;
import com.ruoyi.acceptance.vo.ToOrderVo;
import com.ruoyi.app.manager.ToOrderManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 接单订单Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "移动端接单订单管理")
@RestController
@RequestMapping("/wx/acceptance/order")
public class WxToOrderController {

    @Autowired
    private ToOrderManager toOrderManager;



    /**
    * 查询接单订单全部列表
    */
    @ApiOperation("查询接单订单全部列表")
    @ApiOperationSupport(order = 2)
    @GetMapping("/allList")
    public R<List<ToOrderVo>> allList(ToOrderVo toOrderVo){
        return R.ok(toOrderManager.list(toOrderVo));
    }

    /**
     * 导出接单订单列表
     */
    @ApiOperation("导出接单订单列表")
    @ApiOperationSupport(order = 3)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToOrderVo toOrderVo, Query query)
    {
        List<ToOrderVo> list = toOrderManager.page(toOrderVo, query).getRecords();
        ExcelUtil<ToOrderVo> util = new ExcelUtil<ToOrderVo>(ToOrderVo.class);
        util.exportExcel(response, list, "接单订单数据");
    }


    /**
     * 新增接单订单
     */
    @ApiOperation("新增接单订单")
    @ApiOperationSupport(order = 4)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToOrderVo toOrderVo)
    {
        return toOrderManager.add(toOrderVo) ? R.ok() : R.fail();
    }

    /**
     * 修改接单订单
     */
    @ApiOperation("修改接单订单")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToOrderVo toOrderVo)
    {
        return toOrderManager.edit(toOrderVo) ? R.ok() : R.fail();
    }

    /**
     * 删除接单订单
     */
    @ApiOperation("删除接单订单")
    @ApiOperationSupport(order = 6)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toOrderManager.remove(ids) ? R.ok() : R.fail();
    }
}
