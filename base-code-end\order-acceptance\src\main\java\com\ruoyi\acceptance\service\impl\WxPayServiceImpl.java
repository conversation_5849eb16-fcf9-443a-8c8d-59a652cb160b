package com.ruoyi.acceptance.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ijpay.core.IJPayHttpResponse;
import com.ijpay.core.enums.RequestMethodEnum;
import com.ijpay.core.kit.AesUtil;
import com.ijpay.core.kit.HttpKit;
import com.ijpay.core.kit.PayKit;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.core.utils.DateTimeZoneUtil;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.enums.WxDomainEnum;
import com.ijpay.wxpay.enums.v3.BasePayApiEnum;
import com.ijpay.wxpay.enums.v3.OtherApiEnum;
import com.ijpay.wxpay.enums.v3.TransferApiEnum;
import com.ijpay.wxpay.model.v3.*;
import com.ruoyi.acceptance.domain.*;
import com.ruoyi.acceptance.mapper.ToGroupBuyingMapper;
import com.ruoyi.acceptance.mapper.ToOrderMapper;
import com.ruoyi.acceptance.mapper.ToPaperMapper;
import com.ruoyi.acceptance.service.IToOrderFlowService;
import com.ruoyi.acceptance.service.IToOrderService;
import com.ruoyi.acceptance.service.WxPayService;
import com.ruoyi.common.config.WxPayConfig;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.common.constant.AcceptanceConstants.*;

@Slf4j
@Service
public class WxPayServiceImpl implements WxPayService {

    @Autowired
    private IToOrderService toOrderService;
    @Autowired
    private IToOrderFlowService toOrderFlowService;
    @Autowired
    private ToOrderMapper toOrderMapper;
    @Autowired
    private ToPaperMapper toPaperMapper;
    @Autowired
    private ToGroupBuyingMapper toGroupBuyingMapper;

    /**
     * 企业付款到零钱
     */
    @Override
    public R withdrawalToSmallChange(Integer amount) {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();

        String out_batch_no = PayKit.generateStr();
        String out_detail_no = PayKit.generateStr();
        String batch_name = PayKit.generateStr();
        TransferDetailInput transferDetailInput = new TransferDetailInput()
                .setOut_detail_no(out_detail_no)
                .setTransfer_amount(amount)
                .setOpenid(openId)
                .setTransfer_remark("乐玛测试");
        List<TransferDetailInput> transferDetailInputs = new ArrayList<>();
        transferDetailInputs.add(transferDetailInput);

        BatchTransferModel transferBatchModel = new BatchTransferModel()
                .setAppid(WxPayConfig.appIdOut)
                .setBatch_name(batch_name)
                .setBatch_remark("乐玛测试")
                .setOut_batch_no(out_batch_no)
                .setTransfer_detail_list(transferDetailInputs)
//                .setTransfer_scene_id()
                .setTotal_amount(1)
                .setTotal_num(1);
        log.info("转账参数: {}", JSONUtil.toJsonStr(transferBatchModel));
        // 调用 API 发起转账
        try {
            IJPayHttpResponse response = WxPayApi.v3(
                    RequestMethodEnum.POST,
                    WxDomainEnum.CHINA.toString(), // 根据实际情况选择合适的域名
                    TransferApiEnum.TRANSFER_BATCHES.toString(), // 获取枚举 URL
                    WxPayConfig.mchIdOut,
                    getSerialNumber(),
                    null,
                    WxPayConfig.apiClientKeyPathOut,
                    JSONUtil.toJsonStr(transferBatchModel) // 将模型转换为 JSON 字符串
            );
            // 处理响应
            log.info("转账响应 {}", response);
            if (response.getStatus() == 200) {
                // 转账成功，处理返回的数据
                System.out.println("转账成功，响应内容：" + response.getBody());
            } else {
                // 处理错误情况
                System.out.println("转账失败，错误码：" + response.getStatus() + "，错误信息：" + response.getBody());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return R.ok();
    }

    @Transactional
    @Override
    public R payRefund(String orderId) {
        ToOrder toOrderFromDb = toOrderMapper.selectById(orderId);
        LambdaQueryWrapper<ToPaper> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ToPaper::getOrderId, toOrderFromDb.getId());
        ToPaper toPaperFromDb = toPaperMapper.selectOne(lambdaQueryWrapper);
        if (toOrderFromDb.getOrderPayStatus()!=2){
            return R.fail("您的订单状态不为已支付可退定金！");
        }
        LambdaQueryWrapper<ToOrderFlow> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
        lambdaQueryWrapper1.eq(ToOrderFlow::getOrderId,toOrderFromDb.getId()).eq(ToOrderFlow::getFlowType,FIRST_PAY);
        ToOrderFlow toOrderFlow = toOrderFlowService.getOne(lambdaQueryWrapper1);
        String transactionId = toOrderFlow.getTransactionId();
        String outTradeNo = toOrderFlow.getOutTradeNo();
        BigDecimal result = toOrderFromDb.getAdvancePrice().multiply(new BigDecimal("100"));
        // 将结果转换为整数
        int refundAmount = result.intValue();
        log.info("进入退款接口------>" );
        log.info("执行操作的 原支付交易对应的微信订单号：{}", transactionId);
        log.info("执行操作的 原支付交易对应的商户订单号：{}", outTradeNo );
        try {
            String outRefundNo = PayKit.generateStr();
            log.info("商户退款单号: {}", outRefundNo);
            List<RefundGoodsDetail> list = new ArrayList<>();
            RefundGoodsDetail refundGoodsDetail = new RefundGoodsDetail()
                    .setMerchant_goods_id( outRefundNo )
                    .setGoods_name( "取消订单" )
                    .setUnit_price( refundAmount )     //金额，单位为分
                    .setRefund_amount( refundAmount )
                    .setRefund_quantity(1);
            list.add(refundGoodsDetail);
            RefundModel refundModel = new RefundModel()
                    .setOut_refund_no(outRefundNo)
                    .setReason("取消订单")
//                    .setNotify_url(wechatPayV3Bean.getDomain().concat("/wechat/operation/pay/refundNotify")) //退款异步回调通知
                    .setAmount(new RefundAmount().setRefund(refundAmount).setTotal(refundAmount).setCurrency("CNY"))
                    .setGoods_detail(list);
            if (transactionId!=null && !transactionId.equals("")) {
                refundModel.setTransaction_id(transactionId);
            }
            if (outTradeNo!=null && !outTradeNo.equals("")) {
                refundModel.setOut_trade_no( outTradeNo );
            }
            log.info("退款参数: {}", JSONUtil.toJsonStr(refundModel));
            IJPayHttpResponse response = WxPayApi.v3(
                    RequestMethodEnum.POST,
                    WxDomainEnum.CHINA.toString(),
                    BasePayApiEnum.DOMESTIC_REFUND.toString(),
                    WxPayConfig.mchIdOut,
                    getSerialNumber(),
                    null,
                    WxPayConfig.apiClientKeyPathOut,
                    JSONUtil.toJsonStr(refundModel)
            );
            log.info("退款响应 {}", response);
            // 根据证书序列号查询对应的证书来验证签名结果
            boolean verifySignature = WxPayKit.verifySignature(response, WxPayConfig.platformCertPathOut);
            log.info("verifySignature: {}", verifySignature);
            String body = response.getBody();
            JSONObject jsonObject = JSONUtil.parseObj(body); //转换为JSON
            if (verifySignature && response.getStatus()==200) {
                //退款成功，处理业务逻辑
                // 1.更新订单状态
                toOrderFromDb.setOrderPayStatus(0);
                toOrderMapper.updateById(toOrderFromDb);
                // 去除团购
                LambdaQueryWrapper<ToGroupBuying> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(ToGroupBuying::getOrderId, orderId);
                ToGroupBuying toGroupBuying = new ToGroupBuying();
                toGroupBuying.setWhetherOutGroup(1);
                toGroupBuyingMapper.update(toGroupBuying, queryWrapper);
                return R.ok("退款成功");
            }else {
                return R.fail("退款失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.fail("退款失败");
        }
    }

    @Override
    public R jsApiPay(String orderId) {
        try {
            WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
            String openId= wxLoginUser.getOpenid();
            ToOrder toOrderFromDb = toOrderMapper.selectById(orderId);
            LambdaQueryWrapper<ToPaper> lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.eq(ToPaper::getOrderId, toOrderFromDb.getId());
            ToPaper toPaperFromDb = toPaperMapper.selectOne(lambdaQueryWrapper);
            String timeExpire = DateTimeZoneUtil.dateToTimeZone(System.currentTimeMillis() + 1000 * 60 * 3);
            UnifiedOrderModel unifiedOrderModel = null;
            if (toOrderFromDb.getOrderPayStatus()==REFUNDABLE_DEPOSIT||toOrderFromDb.getOrderPayStatus()==NON_REFUNDABLE_DEPOSIT){
                return R.fail("您已成功支付定金，无需重复支付！");
            }else if (toOrderFromDb.getOrderPayStatus()==TRANSACTION_COMPLETED){
                return R.fail("您已成功支付尾款，无需重复支付！");
            } else if (toOrderFromDb.getOrderPayStatus()==DEPOSIT_TO_PAID){
                //待付定金
                BigDecimal result = toOrderFromDb.getAdvancePrice().multiply(new BigDecimal("100"));
                int advancePrice = result.intValue();
                unifiedOrderModel = new UnifiedOrderModel()
                        // APPID
                        .setAppid(WxPayConfig.appIdOut)
                        // 商户号
                        .setMchid(WxPayConfig.mchIdOut)
                        .setDescription(toPaperFromDb.getTitle())
                        .setOut_trade_no(PayKit.generateStr())
                        .setTime_expire(timeExpire)
                        .setAttach("微信系开发脚手架 https://gitee.com/javen205/TNWX")
                        .setNotify_url(WxPayConfig.notifyUrlOut)
                        .setAmount(new Amount().setTotal(advancePrice))
                        .setPayer(new Payer().setOpenid(openId));
            }else if (toOrderFromDb.getOrderPayStatus()==TAIL_HOSPITALITY_PAYMENT){
                //尾款待支付
                BigDecimal result = toOrderFromDb.getBalancePayment().multiply(new BigDecimal("100"));
                int balancePayment = result.intValue();
                unifiedOrderModel = new UnifiedOrderModel()
                        // APPID
                        .setAppid(WxPayConfig.appIdOut)
                        // 商户号
                        .setMchid(WxPayConfig.mchIdOut)
                        .setDescription(toPaperFromDb.getTitle())
                        .setOut_trade_no(PayKit.generateStr())
                        .setTime_expire(timeExpire)
                        .setAttach("微信系开发脚手架 https://gitee.com/javen205/TNWX")
                        .setNotify_url(WxPayConfig.notifyUrlOut)
                        .setAmount(new Amount().setTotal(balancePayment))
                        .setPayer(new Payer().setOpenid(openId));
            }
            log.info("统一下单参数 {}", JSONUtil.toJsonStr(unifiedOrderModel));
            //订单表记录微信支付商户订单号
            ToOrder toOrder = new ToOrder();
            toOrder.setId(Long.valueOf(orderId));
            toOrder.setOutTradeNo(unifiedOrderModel.getOut_trade_no());
            toOrderService.updateById(toOrder);
            IJPayHttpResponse response = WxPayApi.v3(
                    RequestMethodEnum.POST,
                    WxDomainEnum.CHINA.toString(),
                    BasePayApiEnum.JS_API_PAY.toString(),
                    WxPayConfig.mchIdOut,
                    getSerialNumber(),
                    null,
                    WxPayConfig.apiClientKeyPathOut,
                    JSONUtil.toJsonStr(unifiedOrderModel)
            );
            log.info("统一下单响应 {}", response);
            // 根据证书序列号查询对应的证书来验证签名结果
            boolean verifySignature = WxPayKit.verifySignature(response, WxPayConfig.platformCertPathOut);
            log.info("verifySignature: {}", verifySignature);
            if (response.getStatus() == HttpStatus.HTTP_OK && verifySignature) {
                String body = response.getBody();
                JSONObject jsonObject = JSONUtil.parseObj(body);
                String prepayId = jsonObject.getStr("prepay_id");
                Map<String, String> map = WxPayKit.jsApiCreateSign(WxPayConfig.appIdOut, prepayId, WxPayConfig.apiClientKeyPathOut);
                log.info("唤起支付参数:{}", map);
                return R.ok("获取成功",JSONUtil.toJsonStr(map));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.fail("唤起失败");
    }

    @Transactional
    @Override
    public void payNotify(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> map = new HashMap<>(12);
        try {
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String serialNo = request.getHeader("Wechatpay-Serial");
            String signature = request.getHeader("Wechatpay-Signature");

            log.info("timestamp:{} nonce:{} serialNo:{} signature:{}", timestamp, nonce, serialNo, signature);
            String result = HttpKit.readData(request);
            log.info("支付通知密文 {}", result);

            // 需要通过证书序列号查找对应的证书，verifyNotify 中有验证证书的序列号
            String plainText = WxPayKit.verifyNotify(serialNo, result, signature, nonce, timestamp, WxPayConfig.apiKeyOut, WxPayConfig.platformCertPathOut);

            log.info("支付通知明文 {}", plainText);

            if (StrUtil.isNotEmpty(plainText)) {
                // 回调之后修改订单状态
                JSONObject jsonObject = JSONUtil.parseObj(plainText);
                String outTradeNo = jsonObject.getStr("out_trade_no");
                String transactionId = jsonObject.getStr("transaction_id");
                ToOrder toOrder = toOrderService.selectOneByOutTradeNo(outTradeNo);
                if (StringUtils.isNotNull(toOrder)){
                    if (toOrder.getOrderPayStatus()==1){
                        UpdateWrapper<ToOrder> wrapper = new UpdateWrapper<>();
                        wrapper.set("order_pay_status",2).set("deposit_payment_time", LocalDateTime.now()).set("transaction_id",transactionId).eq("out_trade_no",outTradeNo);
                        toOrderService.update(wrapper);

                        ToOrderFlow toOrderFlow = new ToOrderFlow();
                        toOrderFlow.setOrderId(toOrder.getId());
                        toOrderFlow.setOutTradeNo(outTradeNo);
                        toOrderFlow.setTransactionId(transactionId);
                        toOrderFlow.setFlowType(FIRST_PAY);
                        toOrderFlowService.save(toOrderFlow);
                    }else if (toOrder.getOrderPayStatus()==4){
                        UpdateWrapper<ToOrder> wrapper = new UpdateWrapper<>();
                        wrapper.set("order_pay_status",5).set("balance_payment_time", LocalDateTime.now()).eq("out_trade_no",outTradeNo);
                        toOrderService.update(wrapper);

                        ToOrderFlow toOrderFlow = new ToOrderFlow();
                        toOrderFlow.setOrderId(toOrder.getId());
                        toOrderFlow.setOutTradeNo(outTradeNo);
                        toOrderFlow.setTransactionId(transactionId);
                        toOrderFlow.setFlowType(FINAL_PAY);
                        toOrderFlowService.save(toOrderFlow);
                    }
                }
                response.setStatus(200);
                map.put("code", "SUCCESS");
                map.put("message", "SUCCESS");
            } else {
                response.setStatus(500);
                map.put("code", "ERROR");
                map.put("message", "签名错误");
            }
            response.setHeader("Content-type", ContentType.JSON.toString());
            response.getOutputStream().write(JSONUtil.toJsonStr(map).getBytes(StandardCharsets.UTF_8));
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String v3Get() {
        // 获取平台证书列表
        try {
            IJPayHttpResponse response = WxPayApi.v3(
                    RequestMethodEnum.GET,
                    WxDomainEnum.CHINA.toString(),
                    OtherApiEnum.GET_CERTIFICATES.toString(),
                    WxPayConfig.mchIdOut,
                    getSerialNumber(),
                    null,
                    WxPayConfig.apiClientKeyPathOut,
                    ""
            );
            String serialNumber = response.getHeader("Wechatpay-Serial");
            String body = response.getBody();
            int status = response.getStatus();
            log.info("serialNumber: {}", serialNumber);
            log.info("status: {}", status);
            log.info("body: {}", body);
            int isOk = 200;
            if (status == isOk) {
                JSONObject jsonObject = JSONUtil.parseObj(body);
                JSONArray dataArray = jsonObject.getJSONArray("data");
                // 默认认为只有一个平台证书
                JSONObject encryptObject = dataArray.getJSONObject(0);
                JSONObject encryptCertificate = encryptObject.getJSONObject("encrypt_certificate");
                String associatedData = encryptCertificate.getStr("associated_data");
                String cipherText = encryptCertificate.getStr("ciphertext");
                String nonce = encryptCertificate.getStr("nonce");
                String serialNo = encryptObject.getStr("serial_no");
                final String platSerialNo = savePlatformCert(associatedData, nonce, cipherText, WxPayConfig.platformCertPathOut);
                log.info("平台证书序列号: {} serialNo: {}", platSerialNo, serialNo);
            }
            // 根据证书序列号查询对应的证书来验证签名结果
            boolean verifySignature = WxPayKit.verifySignature(response, WxPayConfig.platformCertPathOut);
            System.out.println("verifySignature:" + verifySignature);
            return body;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存平台证书
     * @param associatedData    associated_data
     * @param nonce             nonce
     * @param cipherText        cipherText
     * @param certPath          证书保存路径
     * @return
     */
    private String savePlatformCert(String associatedData, String nonce, String cipherText, String certPath) {
        try {
            AesUtil aesUtil = new AesUtil(WxPayConfig.apiKeyOut.getBytes(StandardCharsets.UTF_8));
            // 平台证书密文解密
            // encrypt_certificate 中的  associated_data nonce  ciphertext
            String publicKey = aesUtil.decryptToString(
                    associatedData.getBytes(StandardCharsets.UTF_8),
                    nonce.getBytes(StandardCharsets.UTF_8),
                    cipherText
            );
            // 保存证书
            FileWriter writer = new FileWriter(certPath);
            writer.write(publicKey);
            // 获取平台证书序列号
            X509Certificate certificate = PayKit.getCertificate(new ByteArrayInputStream(publicKey.getBytes()));
            return certificate.getSerialNumber().toString(16).toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    private String getSerialNumber() {
        // 获取证书序列号
        X509Certificate certificate = PayKit.getCertificate(WxPayConfig.apiClientCertPathOut);
        if (null != certificate) {
            String serialNo = certificate.getSerialNumber().toString(16).toUpperCase();
            // 提前两天检查证书是否有效
            boolean isValid = PayKit.checkCertificateIsValid(certificate, WxPayConfig.mchIdOut, -2);
            log.info("证书是否可用 {} 证书有效期为 {}", isValid, DateUtil.format(certificate.getNotAfter(), DatePattern.NORM_DATETIME_PATTERN));
            System.out.println("serialNo:" + serialNo);
            return serialNo;
        }
        return null;
    }

}
