package com.ruoyi.system.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.system.mapper.OssFileMapper;
import com.ruoyi.system.domain.OssFile;
import com.ruoyi.system.service.IOssFileService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 文件管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class OssFileServiceImpl extends ServiceImpl<OssFileMapper,OssFile> implements IOssFileService {

    @Autowired
    private OssFileMapper ossFileMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<OssFile> queryWrapper(OssFile ossFile) {
        LambdaQueryWrapper<OssFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(ossFile.getUrl()), OssFile::getUrl, ossFile.getUrl());
        queryWrapper.eq(ObjUtil.isNotEmpty(ossFile.getFileSize()), OssFile::getFileSize, ossFile.getFileSize());
        return queryWrapper;
    }

    /**
     * 查询文件管理分页
     *
     * @param ossFile 文件管理
     * @return 文件管理
     */
    @Override
    public IPage<OssFile> pages(OssFile ossFile, IPage<OssFile> page)
    {
        return ossFileMapper.selectPage(page, this.queryWrapper(ossFile));
    }

    /**
     * 查询文件管理列表
     * 
     * @param ossFile 文件管理
     * @return 文件管理
     */
    @Override
    public List<OssFile> selectList(OssFile ossFile)
    {
        return ossFileMapper.selectList(this.queryWrapper(ossFile));
    }

}
