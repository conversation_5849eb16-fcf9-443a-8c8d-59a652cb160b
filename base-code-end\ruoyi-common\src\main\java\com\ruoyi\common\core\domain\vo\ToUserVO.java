package com.ruoyi.common.core.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户信息对象 to_user
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
public class ToUserVO
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phoneNumber;

    /** 微信号 */
    @Excel(name = "微信号")
    private String wechatId;

    @Excel(name = "openId")
    private String openId;

    /** 邀请码 */
    @Excel(name = "邀请码")
    private String inviteCode;

    @Excel(name = "头像")
    private String  headSculpture;

    @Excel(name = "分享码")
    private String  shareCode;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private String createDate;

    /** 更新时间 */
    @Excel(name = "更新时间")
    private String updateDate;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private String createBy;

    /** 更新时间 */
    @Excel(name = "更新时间")
    private String updateBy;
    private String roleKey;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getWechatId() {
        return wechatId;
    }

    public void setWechatId(String wechatId) {
        this.wechatId = wechatId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }

    public String getHeadSculpture() {
        return headSculpture;
    }

    public void setHeadSculpture(String headSculpture) {
        this.headSculpture = headSculpture;
    }

    public String getShareCode() {
        return shareCode;
    }

    public void setShareCode(String shareCode) {
        this.shareCode = shareCode;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getRoleKey() {
        return roleKey;
    }

    public void setRoleKey(String roleKey) {
        this.roleKey = roleKey;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public String toString() {
        return "ToUser{" +
                "id=" + id +
                ", nickname='" + nickname + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", wechatId='" + wechatId + '\'' +
                ", openId='" + openId + '\'' +
                ", inviteCode='" + inviteCode + '\'' +
                ", headSculpture='" + headSculpture + '\'' +
                ", shareCode='" + shareCode + '\'' +
                ", createDate='" + createDate + '\'' +
                ", updateDate='" + updateDate + '\'' +
                ", roleKey='" + roleKey + '\'' +
                '}';
    }
}
