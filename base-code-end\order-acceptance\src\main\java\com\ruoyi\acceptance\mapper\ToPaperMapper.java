package com.ruoyi.acceptance.mapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.dto.ToPaperDTO;
import com.ruoyi.acceptance.vo.ToPaperVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.ruoyi.acceptance.domain.ToPaper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 论文管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-12
 */
@Repository
public interface ToPaperMapper extends BaseMapper<ToPaper> {

    IPage<ToPaperDTO> selectToPapers(@Param("toPaperVo") ToPaperVo toPaperVo, @Param("page") IPage<ToPaperDTO> page);

    ToPaperDTO getToPaperDtoById(@Param("id") Long id);

    List<ToPaperDTO> tradedList();

    List<ToPaperDTO> obligationList();

    List<ToPaperDTO> orderStatusList(@Param("status") String status,@Param("openId") String openId,@Param("paperName")String paperName);

    List<ToPaperDTO> orderStatusListForGroup(@Param("openId") String openId);

    List<ToPaperDTO> notSaleList();
}
