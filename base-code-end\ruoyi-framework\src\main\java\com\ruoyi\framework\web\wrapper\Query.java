package com.ruoyi.framework.web.wrapper;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description: 分页查询条件
 *
 * <AUTHOR>
 **/
@Data
@Accessors(chain = true)
@ApiModel(description = "分页信息")
public class Query {

	/**
	 * 当前页
	 */
	@ApiModelProperty(value = "当前页")
	private Integer pageNum;

	/**
	 * 每页的数量
	 */
	@ApiModelProperty(value = "每页的数量")
	private Integer pageSize;

	/**
	 * 排序的字段名,多个按照逗号分割, 不区分中英文，不区分大小写
	 * 举例: 1.单个排序 如：a desc 或者 a ASC ;
	 *      2.不加排序关键字默认正序 如: a
	 *      3.组合排序，之间使用“，”或“,” 相隔如 a desc，b asc,c
	 */
	@ApiModelProperty(value = "排序的字段名")
	private String orderBy;

}
