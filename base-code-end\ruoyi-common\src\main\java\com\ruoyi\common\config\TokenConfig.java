package com.ruoyi.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * token配置
 *
 * @author: 天鸣
 * @date: 2024-07-15
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "token")
public class TokenConfig implements InitializingBean {

    // 令牌自定义标识
    private String header;

    // 令牌秘钥
    private String secret;

    // 令牌有效期（默认30分钟）
    private int expireTime;

    //请求源标识
    private String source;

    //请求源类型
    private SourceType sourceType;

    @Getter
    @Setter
    public static class SourceType {

        private String wxApplet;
        private String dispatchApplet;

    }

    public static String HEADER;
    public static String SECRET;
    public static int EXPIRE_TIME;
    public static String SOURCE;
    public static String WX_APPLET;
    public static String DISPATCH_APPLET;

    @Override
    public void afterPropertiesSet() {
        HEADER = header;
        SECRET = secret;
        EXPIRE_TIME = expireTime;
        SOURCE = source;
        WX_APPLET = sourceType.wxApplet;
        DISPATCH_APPLET = sourceType.dispatchApplet;
    }
}
