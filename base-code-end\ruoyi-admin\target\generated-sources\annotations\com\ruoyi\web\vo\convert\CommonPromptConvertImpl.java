package com.ruoyi.web.vo.convert;

import com.ruoyi.system.dto.CommonPromptDTO;
import com.ruoyi.web.vo.CommonPromptVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CommonPromptConvertImpl implements CommonPromptConvert {

    @Override
    public CommonPromptVo dtoToVo(CommonPromptDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommonPromptVo commonPromptVo = new CommonPromptVo();

        commonPromptVo.setAiType( arg0.getAiType() );
        List<Long> list = arg0.getCommonPromptProjectTypes();
        if ( list != null ) {
            commonPromptVo.setCommonPromptProjectTypes( new ArrayList<Long>( list ) );
        }
        commonPromptVo.setContent( arg0.getContent() );
        commonPromptVo.setEnableSearch( arg0.getEnableSearch() );
        commonPromptVo.setEnableThinking( arg0.getEnableThinking() );
        commonPromptVo.setIsActive( arg0.getIsActive() );
        commonPromptVo.setPromptId( arg0.getPromptId() );
        commonPromptVo.setPromptTitle( arg0.getPromptTitle() );
        commonPromptVo.setSystemPrompt( arg0.getSystemPrompt() );

        return commonPromptVo;
    }

    @Override
    public List<CommonPromptVo> dtoToVoList(List<CommonPromptDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<CommonPromptVo> list = new ArrayList<CommonPromptVo>( arg0.size() );
        for ( CommonPromptDTO commonPromptDTO : arg0 ) {
            list.add( dtoToVo( commonPromptDTO ) );
        }

        return list;
    }

    @Override
    public CommonPromptDTO voToDto(CommonPromptVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CommonPromptDTO commonPromptDTO = new CommonPromptDTO();

        commonPromptDTO.setAiType( arg0.getAiType() );
        List<Long> list = arg0.getCommonPromptProjectTypes();
        if ( list != null ) {
            commonPromptDTO.setCommonPromptProjectTypes( new ArrayList<Long>( list ) );
        }
        commonPromptDTO.setContent( arg0.getContent() );
        commonPromptDTO.setEnableSearch( arg0.getEnableSearch() );
        commonPromptDTO.setEnableThinking( arg0.getEnableThinking() );
        commonPromptDTO.setIsActive( arg0.getIsActive() );
        commonPromptDTO.setPromptId( arg0.getPromptId() );
        commonPromptDTO.setPromptTitle( arg0.getPromptTitle() );
        commonPromptDTO.setSystemPrompt( arg0.getSystemPrompt() );

        return commonPromptDTO;
    }

    @Override
    public List<CommonPromptDTO> voToDtoList(List<CommonPromptVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<CommonPromptDTO> list = new ArrayList<CommonPromptDTO>( arg0.size() );
        for ( CommonPromptVo commonPromptVo : arg0 ) {
            list.add( voToDto( commonPromptVo ) );
        }

        return list;
    }
}
