package com.ruoyi.dispatch.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.vo.ChatConversationsTwoVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.dispatch.domain.ChatConversations;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 会话Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IChatConversationsService extends IService<ChatConversations> {

    /**
     * 分页查询会话
     * @param chatConversations 会话
     * @param page 分页条件
     * @return 会话集合
     */
    IPage<ChatConversations> pages(ChatConversations chatConversations, IPage<ChatConversations> page);

    /**
     * 查询会话列表
     * 
     * @param chatConversations 会话
     * @return 会话集合
     */
     List<ChatConversations> selectList(ChatConversations chatConversations);

    R<?> saveConversation(ChatConversationsTwoVO chatConversationsTwoVO);
}
