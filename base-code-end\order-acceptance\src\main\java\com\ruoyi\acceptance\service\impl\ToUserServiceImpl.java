package com.ruoyi.acceptance.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.acceptance.domain.ToWallet;
import com.ruoyi.acceptance.mapper.ToWalletMapper;
import com.ruoyi.acceptance.service.IToUserService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.acceptance.mapper.ToUserMapper;
import com.ruoyi.common.core.domain.vo.ToUserVO;
import com.ruoyi.common.core.domain.vo.TransactionUserVO;
import com.ruoyi.common.core.domain.entity.ToUser;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static com.ruoyi.common.constant.AcceptanceConstants.*;
import static com.ruoyi.common.constant.OrderAcceptanceConstant.*;

/**
 * 用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-23
 */
@Slf4j
@Service
public class ToUserServiceImpl extends ServiceImpl<ToUserMapper, ToUser> implements IToUserService
{
    @Autowired
    private ToUserMapper toUserMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private ToWalletMapper toWalletMapper;

    /**
     * 查询用户信息
     *
     * @param id 用户信息主键
     * @return 用户信息
     */
    @Override
    public ToUser selectToUserById(Long id)
    {
        return toUserMapper.selectById(id);
    }

    @Override
    public ToUserVO selectToUserByOpenId() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        log.info("selectToUserByOpenId===>{}",openId);
        return toUserMapper.selectToUserByOpenId(openId);
    }

    @Override
    public ToUser selectToUserByOpenId(String openId) {
        return toUserMapper.selectToUserByOpenId1(openId);
    }

    @Override
    public ToUser selectShareUserByInvitedUserOpenId(String inviteCode) {
        return toUserMapper.selectShareUserByInvitedUserOpenId(inviteCode);
    }

    /**
     * 查询用户信息列表
     *
     * @param toUser 用户信息
     * @return 用户信息
     */
    @Override
    public List<ToUser> selectToUserList(ToUser toUser)
    {
        return toUserMapper.selectToUserList(toUser);
    }

    @Override
    public List<ToUser> inviteCustomerList() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        log.info("inviteCustomerList入参：{}",openId);
        return toUserMapper.inviteCustomerList(openId);
    }

    @Override
    public List<TransactionUserVO> transactionCustomerList() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        log.info("transactionCustomerList入参：{}",openId);
        return toUserMapper.transactionCustomerList(openId);
    }

    /**
     * 新增用户信息
     *
     * @param toUser 用户信息
     * @return 结果
     */
    @Override
    public R<?> insertToUserForPc(ToUser toUser)
    {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        toUser.setOpenId(openId);
        ToUserVO tempUser = toUserMapper.selectToUserByOpenId(openId);
        if (StringUtils.isNotNull(tempUser)){
            LambdaQueryWrapper<ToUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.eq(ToUser::getOpenId,openId);
            toUserMapper.update(toUser,userLambdaQueryWrapper);
        }else {
            String randomString = RandomUtil.randomString(RANDOM_STRINGS, 8);
            toUser.setShareCode(randomString);
            toUserMapper.insert(toUser);

            Long roleId = sysRoleMapper.selectRoleIdByRoleKey(CUSTOM);
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(toUser.getId());
            sysUserRole.setRoleId(roleId);
            sysUserRoleMapper.insert(sysUserRole);
        }
        return R.ok();
    }

    @Override
    public R<?> insertToUserForWx(ToUser toUser)
    {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId= wxLoginUser.getOpenid();
        toUser.setOpenId(openId);
        ToUserVO tempUser = toUserMapper.selectToUserByOpenId(openId);
        if (StringUtils.isNotNull(tempUser)){
            // 自己不能邀请自己
            if (StringUtils.isNotNull(toUser.getInviteCode())&&tempUser.getShareCode().equals(toUser.getInviteCode())){
                return R.fail(CANNOT_INVITE_ONESELF,"不能邀请自己");
            }
            LambdaQueryWrapper<ToUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.eq(ToUser::getOpenId,openId);
            toUserMapper.update(toUser,userLambdaQueryWrapper);

            // 更新钱包名称
            ToWallet toWallet = new ToWallet();
            toWallet.setAccountName(toUser.getNickname()+"_"+toUser.getPhoneNumber()+"的钱包");
            LambdaQueryWrapper<ToWallet> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(ToWallet::getOpenId,openId);
            toWalletMapper.update(toWallet,queryWrapper);
        }else {
            String randomString = RandomUtil.randomString(RANDOM_STRINGS, 8);
            toUser.setShareCode(randomString);
            toUserMapper.insert(toUser);

            Long roleId = sysRoleMapper.selectRoleIdByRoleKey(CUSTOM);
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(toUser.getId());
            sysUserRole.setRoleId(roleId);
            sysUserRoleMapper.insert(sysUserRole);

            // 初始化钱包
            ToWallet toWallet = new ToWallet();
            toWallet.setOpenId(openId);
            toWallet.setAccountName(toUser.getNickname()+"_"+toUser.getPhoneNumber()+"的钱包");
            toWalletMapper.insert(toWallet);
        }
        return R.ok("注册成功");
    }

    /**
     * 修改用户信息
     *
     * @param toUser 用户信息
     * @return 结果
     */
    @Override
    public int updateToUser(ToUser toUser)
    {
        Long roleId = -1l;
        if (YES.equals(toUser.getWhetherAgent())){
            roleId = sysRoleMapper.selectRoleIdByRoleKey(AGENT);
        }else if (NO.equals(toUser.getWhetherAgent())){
            roleId = sysRoleMapper.selectRoleIdByRoleKey(CUSTOM);
        }
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(toUser.getId());
        sysUserRole.setRoleId(roleId);
        LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRole::getUserId,toUser.getId());
        sysUserRoleMapper.update(sysUserRole,queryWrapper);

        return toUserMapper.updateById(toUser);
    }

    /**
     * 批量删除用户信息
     *
     * @param ids 需要删除的用户信息主键
     * @return 结果
     */
    @Override
    public int deleteToUserByIds(Integer[] ids)
    {
        return toUserMapper.deleteToUserByIds(ids);
    }

    /**
     * 删除用户信息信息
     *
     * @param id 用户信息主键
     * @return 结果
     */
    @Override
    public int deleteToUserById(Integer id)
    {
        return toUserMapper.deleteToUserById(id);
    }

    @Override
    public List<ToUser> getUserList(String phoneNumber) {
        return toUserMapper.getUserList(phoneNumber);
    }

    @Override
    public BigDecimal getInviteUserCountByOpenId(String openId) {
        return toUserMapper.getInviteUserCountByOpenId(openId);
    }
}
