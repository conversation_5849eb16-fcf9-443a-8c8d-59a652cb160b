package com.ruoyi.acceptance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.acceptance.dto.HeadSculptureDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MyGroupOrderVO {
    // 团长属性
    private Long paperId;

    // 团购订单号属性
    private String groupId;

    //团长头像
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    //是否空团 1是0否
    private String title;

    //1未团购 2团购中 3团购成功 4团购失败 5团购倒计时结束
    private Integer groupStatus;

    //1为结束 0为未结束
    private Integer groupEndFlag;

    // 团购成员头像属性，这里使用String来表示头像的URL或路径
    private List<HeadSculptureDTO> headSculptureDTOS;
}
