package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ChatConversationsVo;
import com.ruoyi.dispatch.domain.ChatConversations;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatConversationsConvertImpl implements ChatConversationsConvert {

    @Override
    public ChatConversationsVo poToVo(ChatConversations arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatConversationsVo chatConversationsVo = new ChatConversationsVo();

        chatConversationsVo.setConversationId( arg0.getConversationId() );
        chatConversationsVo.setConversationName( arg0.getConversationName() );
        chatConversationsVo.setConversationType( arg0.getConversationType() );

        return chatConversationsVo;
    }

    @Override
    public List<ChatConversationsVo> poToVoList(List<ChatConversations> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatConversationsVo> list = new ArrayList<ChatConversationsVo>( arg0.size() );
        for ( ChatConversations chatConversations : arg0 ) {
            list.add( poToVo( chatConversations ) );
        }

        return list;
    }

    @Override
    public ChatConversations voToPo(ChatConversationsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatConversations chatConversations = new ChatConversations();

        chatConversations.setConversationId( arg0.getConversationId() );
        chatConversations.setConversationName( arg0.getConversationName() );
        chatConversations.setConversationType( arg0.getConversationType() );

        return chatConversations;
    }

    @Override
    public List<ChatConversations> voToPoList(List<ChatConversationsVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ChatConversations> list = new ArrayList<ChatConversations>( arg0.size() );
        for ( ChatConversationsVo chatConversationsVo : arg0 ) {
            list.add( voToPo( chatConversationsVo ) );
        }

        return list;
    }
}
