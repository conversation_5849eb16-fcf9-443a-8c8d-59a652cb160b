package com.ruoyi.acceptance.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.domain.ToOrderFlow;

import java.util.List;

/**
 * 订单流水Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
public interface IToOrderFlowService extends IService<ToOrderFlow> {

    /**
     * 分页查询订单流水
     * @param toOrderFlow 订单流水
     * @param page 分页条件
     * @return 订单流水集合
     */
    IPage<ToOrderFlow> pages(ToOrderFlow toOrderFlow, IPage<ToOrderFlow> page);

    /**
     * 查询订单流水列表
     * 
     * @param toOrderFlow 订单流水
     * @return 订单流水集合
     */
     List<ToOrderFlow> selectList(ToOrderFlow toOrderFlow);

}
