package com.ruoyi.acceptance.dto;

import jdk.nashorn.internal.objects.annotations.Constructor;
import lombok.*;

import java.math.BigDecimal;

@Data
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class InvitationRankingDTO {
    private String nickname;
    private String headSculpture;
    private String agentOpenId;
    private Integer totalCustomerQuantity;
    private Integer rank;

}
