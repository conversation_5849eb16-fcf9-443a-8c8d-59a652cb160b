package com.ruoyi.web.controller.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.app.manager.ToOrderManager;
import com.ruoyi.acceptance.vo.ToOrderVo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 接单订单Controller
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Api(tags = "接单订单管理")
@RestController
@RequestMapping("/acceptance/order")
public class ToOrderController {

    @Autowired
    private ToOrderManager toOrderManager;

    /**
     * 分页查询接单订单
     */
    @ApiOperation("分页查询接单订单")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('acceptance:order:list')")
    @GetMapping("/list")
    public R<IPage<ToOrderVo>> list(ToOrderVo toOrderVo, Query query)
    {
        return R.ok(toOrderManager.selectOrders(toOrderVo, query));
    }

    /**
    * 查询接单订单全部列表
    */
    @ApiOperation("查询接单订单全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('acceptance:order:list')")
    @GetMapping("/allList")
    public R<List<ToOrderVo>> allList(ToOrderVo toOrderVo){
        return R.ok(toOrderManager.list(toOrderVo));
    }

    /**
     * 导出接单订单列表
     */
    @ApiOperation("导出接单订单列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('acceptance:order:export')")
    @Log(title = "接单订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ToOrderVo toOrderVo, Query query)
    {
        List<ToOrderVo> list = toOrderManager.page(toOrderVo, query).getRecords();
        ExcelUtil<ToOrderVo> util = new ExcelUtil<ToOrderVo>(ToOrderVo.class);
        util.exportExcel(response, list, "接单订单数据");
    }

    /**
     * 获取接单订单详细信息
     */
    @ApiOperation("获取接单订单详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:order:query')")
    @GetMapping(value = "/{id}")
    public R<ToOrderVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toOrderManager.getInfoForPc(id));
    }

    /**
     * 新增接单订单
     */
    @ApiOperation("新增接单订单")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('acceptance:order:add')")
    @Log(title = "接单订单", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToOrderVo toOrderVo)
    {
        return toOrderManager.add(toOrderVo) ? R.ok() : R.fail();
    }

    /**
     * 修改接单订单
     */
    @ApiOperation("修改接单订单")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('acceptance:order:edit')")
    @Log(title = "接单订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToOrderVo toOrderVo)
    {
        return toOrderManager.edit(toOrderVo) ? R.ok() : R.fail();
    }

    /**
     * 删除接单订单
     */
    @ApiOperation("删除接单订单")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('acceptance:order:remove')")
    @Log(title = "接单订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toOrderManager.remove(ids) ? R.ok() : R.fail();
    }
}