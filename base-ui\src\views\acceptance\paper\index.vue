<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="论文ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户" prop="custom">
        <el-select v-model="queryParams.custom" clearable filterable remote placeholder="请选择客户" :remote-method="handGetUserList" style="width: 100%">
          <el-option
            v-for="user in userOptions"
            :key="user.phoneNumber"
            :label="user.nickname + ' (' + user.phoneNumber + ')'"
            :value="user.openId">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['acceptance:paper:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['acceptance:paper:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['acceptance:paper:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['acceptance:paper:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="paperList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="论文ID" align="center" prop="id" />
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="客户" align="center" prop="custom" >
        <template slot-scope="scope">
          <span>{{ getCustom(scope.row.custom) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="技术选型" align="center" prop="technicalSelection">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.to_paper_technology" :value="scope.row.technicalSelection ? scope.row.technicalSelection.split(',') : []"/>
        </template>
      </el-table-column>
      <el-table-column label="包含服务" align="center" prop="includingServices">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.to_paper_service" :value="scope.row.includingServices ? scope.row.includingServices.split(',') : []"/>
        </template>
      </el-table-column>
      <el-table-column label="简介" align="center" prop="introduction">
        <template  slot-scope="scope">
          <el-tooltip>
            <div class="w500px" slot="content">
              <span>{{scope.row.introduction}}</span>
            </div>
            <div class="introduction-content">{{scope.row.introduction}}</div>
          </el-tooltip>

        </template>
      </el-table-column>
<!--      <el-table-column label="预付款价" align="center" prop="advancePrice" />-->
<!--      <el-table-column label="尾款" align="center" prop="balancePayment" />-->
<!--      <el-table-column label="全额价格" align="center" prop="totalPrice" />-->
      <el-table-column label="交付时间" align="center" prop="deliveryTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deliveryTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="是否退款" align="center" prop="applyRefund">-->
<!--        <template slot-scope="scope">-->
<!--          <span>-->
<!--            {{getStatus(scope.row.applyRefund)}}-->
<!--          </span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="团购成员" align="center" prop="buyMembers"></el-table-column>-->
<!--      <el-table-column label="是否团购" align="center" prop="groupPurchase">-->
<!--        <template slot-scope="scope">-->
<!--           <span>-->
<!--              {{getStatus(scope.row.applyRefund)}}-->
<!--            </span>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="支付时间" align="center" prop="paymentTime" />-->
<!--      <el-table-column label="是否结尾款" align="center" prop="settlement">-->
<!--        <template slot-scope="scope">-->
<!--           <span>-->
<!--          {{getStatus(scope.row.settlement)}}-->
<!--        </span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row, 'edit')"
              v-hasPermi="['acceptance:paper:edit']"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['acceptance:paper:remove']"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-tickets"
              @click="handleUpdate(scope.row, 'detail')"
              v-hasPermi="['acceptance:paper:remove']"
            >详情</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改论文管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" :disabled="type === 'detail'">

        <el-form-item label="客户" prop="custom">
           <el-select v-model="form.custom" clearable filterable remote placeholder="请选择客户" :remote-method="handGetUserList" style="width: 100%">
             <el-option
               v-for="user in userOptions"
               :key="user.phoneNumber"
               :label="user.nickname + ' (' + user.phoneNumber + ')'"
               :value="user.openId">
             </el-option>
           </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="技术选型" prop="technicalSelection">
          <el-checkbox-group v-model="form.technicalSelection">
            <el-checkbox
              v-for="dict in dict.type.to_paper_technology"
              :key="dict.value"
              :label="dict.value">
              {{dict.label}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="包含服务" prop="includingServices">
          <el-checkbox-group v-model="form.includingServices">
            <el-checkbox
              v-for="dict in dict.type.to_paper_service"
              :key="dict.value"
              :label="dict.value">
              {{dict.label}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="简介" prop="introduction">
          <el-input v-model="form.introduction" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="预付款价" prop="advancePrice">
          <el-input v-model="form.advancePrice" placeholder="请输入预付款价" />
        </el-form-item>
        <el-form-item label="尾款" prop="balancePayment">
          <el-input v-model="form.balancePayment" placeholder="请输入尾款" />
        </el-form-item>
        <el-form-item label="全额价格" prop="totalPrice">
          <el-input v-model="form.totalPrice" placeholder="请输入全额价格" />
        </el-form-item>
        <el-form-item label="交付时间" prop="deliveryTime">
          <el-date-picker clearable
            v-model="form.deliveryTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择交付时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否团购" prop="groupPurchase">
          <el-select v-model="form.groupPurchase">
            <el-option :value="1" label="是">是</el-option>
            <el-option :value="0" label="否">否</el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPaper, getPaper, delPaper, addPaper, updatePaper,getUserList } from "@/api/acceptance/paper";

export default {
  name: "Paper",
  dicts: ['to_paper_technology', 'to_paper_service'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      orderIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 论文管理表格数据
      paperList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      type: "edit",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        id: null,
        title: null,
        custom: null,
        technicalSelection: null,
        includingServices: null,
        introduction: null,
        buyMembers: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      userOptions: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询论文管理列表 */
    async getList() {
      this.loading = true;
      await this.handGetUserList("");
      listPaper(this.queryParams).then(response => {
        this.paperList = response.data.records;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    getCustom(openId) {
      const find = this.userOptions.find(item => {
        return item.openId === openId
      })
      return find.nickname + ' (' + find.phoneNumber + ')';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    getStatus(status) {
      const statusList = {
        1: "是",
        0:"否"
      }
      return statusList[status] || "";
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orderId: null,
        title: null,
        technicalSelection: [],
        includingServices: [],
        introduction: null,
        buyMembers: null,
        advancePrice: null,
        balancePayment: null,
        paymentTime: null,
        totalPrice: null,
        deliveryTime: null,
        applyRefund: null,
        groupPurchase: null,
        settlement: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        custom:null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.orderIds = selection.map(item => item.orderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.type = "add";
      this.title = "添加论文管理";
      this.open = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row, type) {
      this.reset();
      const id = row.id || this.ids
      this.type = type; // 类型
      getPaper(id).then(response => {
        this.form = response.data;
        this.form.technicalSelection = this.form.technicalSelection.split(",");
        this.form.includingServices = this.form.includingServices.split(",");
        this.open = true;
        this.title = "修改论文管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.technicalSelection = this.form.technicalSelection.join(",");
          this.form.includingServices = this.form.includingServices.join(",");
          if (this.form.id != null) {
            updatePaper(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPaper(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const orderIds = row.orderId || this.orderIds;
      this.$modal.confirm('是否确认删除论文管理编号为"' + ids + '"的数据项？').then(function() {
        return delPaper(ids,orderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('acceptance/paper/export', {
        ...this.queryParams
      }, `paper_${new Date().getTime()}.xlsx`)
    },
    /* 获取选项列表 */
    handGetUserList(key) {
      getUserList(key).then((res) => {
        this.userOptions = res;
      });
    }
  }
};
</script>
<style scoped>
.introduction-content {
  max-width: 100px;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
}
.w500px {
  width: 500px;
}
</style>
