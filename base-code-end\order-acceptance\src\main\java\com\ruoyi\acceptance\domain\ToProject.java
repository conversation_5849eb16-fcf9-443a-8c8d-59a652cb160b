package com.ruoyi.acceptance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.PoBaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 项目基本信息对象 to_project
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Data
@TableName("to_project")
public class ToProject extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 订单id */
    @TableField(value = "order_id")
    private Long orderId;

    /** 标题 */
    @TableField(value = "title")
    private String title;

    /** 选择类目，1级*/
    @TableField(value = "category_selection_one")
    private String categorySelectionOne;

    /** 选择类目，按逗号分隔 */
    @TableField(value = "category_selection_two")
    private String categorySelectionTwo;

    /** 需求描述 */
    @TableField(value = "requirement_description")
    private String requirementDescription;

    /** 追加服务 */
    @TableField(value = "additional_services")
    private String additionalServices;

    /** 团购成员（用户信息表id关联） */
    @TableField(value = "group_members")
    private String groupMembers;

    /** 客户openId */
    @TableField(value = "customer_openid")
    private String customerOpenid;

    /** 商户openId */
    @TableField(value = "merchant_openid")
    private String merchantOpenid;

    /** 是否非卖品（1为是0为否） */
    @TableField(value = "is_not_for_sale")
    private Integer isNotForSale;

    /** 开始时间，双方确认交易时间 */
    @TableField(value = "start_time")
    private Date startTime;

    /** 期望交付时间 */
    @TableField(value = "expected_delivery_time")
    private Date expectedDeliveryTime;

    /** 实际交付时间，项目完成时间 */
    @TableField(value = "actual_delivery_time")
    private Date actualDeliveryTime;

    /** 项目结束时间 */
    @TableField(value = "project_end_time")
    private Date projectEndTime;

    /** 附件 */
    @TableField(value = "attachments")
    private String attachments;

}
