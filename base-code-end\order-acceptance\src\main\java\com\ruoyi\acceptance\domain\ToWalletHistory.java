package com.ruoyi.acceptance.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 钱包流水对象 to_wallet_history
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
@TableName("to_wallet_history")
public class ToWalletHistory extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 用户的OpenID */
    @TableField(value = "open_id")
    private String openId;

    /** 冻结金额 */
    @TableField(value = "frozen_amount")
    private BigDecimal frozenAmount;

    /** 待提现金额 */
    @TableField(value = "not_withdraw_amount")
    private BigDecimal notWithdrawAmount;

    /** 已提现金额 */
    @TableField(value = "withdrawn_amount")
    private BigDecimal withdrawnAmount;

    /** 总金额 */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /** 总金额 */
    @TableField(value = "flow_type")
    private BigDecimal flowType;

}
