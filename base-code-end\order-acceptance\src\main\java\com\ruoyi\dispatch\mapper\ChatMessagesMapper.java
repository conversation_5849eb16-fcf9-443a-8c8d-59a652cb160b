package com.ruoyi.dispatch.mapper;
import com.ruoyi.acceptance.vo.ChatMessagesTwoVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.ruoyi.dispatch.domain.ChatMessages;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Repository
public interface ChatMessagesMapper extends BaseMapper<ChatMessages> {
    List<ChatMessagesTwoVO> merchantMessageList(@Param("merchantOpenid") String merchantOpenid);

    List<ChatMessagesTwoVO> customerMessageList(@Param("customerOpenid") String customerOpenid);
}
