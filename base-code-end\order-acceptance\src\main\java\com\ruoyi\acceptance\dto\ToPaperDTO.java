package com.ruoyi.acceptance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ToPaperDTO {
    /** ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /** 订单id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;
    /** 标题 */
    private String title;
    /** 技术选型 */
    private String technicalSelection;
    /** 包含服务 */
    private String includingServices;
    /** 简介 */
    private String introduction;
    /** 团购成员（用户信息表id关联） */
    private String buyMembers;
    /** 预付款价 */
    private BigDecimal advancePrice;
    /** 尾款 */
    private BigDecimal balancePayment;
    /** 定金支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date depositPaymentTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date balancePaymentTime;
    /** 全额价格 */
    private BigDecimal totalPrice;
    /** 交付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryTime;
    /** 是否申请退款 */
    private Integer applyRefund;
    /** 是否团购 */
    private Integer groupPurchase;
    /** 是否结清尾款 */
    private Integer settlement;
    private Integer orderPayStatus;
    //openid
    private String custom;
    // 是否显示退款按钮 0不显示 1显示
    private Integer refundFlag;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    //是否非卖品
    private Integer notSale;
    //团购倒计时时间差
    private Long groupDaysRemaining;
    //1为结束 0为未结束
    private Integer groupEndFlag;
    //1未团购 2团购中 3团购成功 4团购失败
    private Integer groupStatus;
    private String groupId;
    private List<HeadSculptureDTO> headSculptureDTOS;
}