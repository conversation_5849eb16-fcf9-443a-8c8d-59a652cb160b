package com.ruoyi.web.controller.common;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import javax.validation.Valid;
import com.ruoyi.web.vo.CommonPromptVo;
import com.ruoyi.web.manager.CommonPromptManager;
import com.ruoyi.common.utils.poi.ExcelUtil;
import javax.servlet.http.HttpServletResponse;
import com.ruoyi.framework.web.wrapper.Query;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 提示词Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Api(tags = "提示词管理")
@RestController
@RequestMapping("/common/prompt")
public class CommonPromptController {

    @Autowired
    private CommonPromptManager commonPromptManager;

    /**
     * 分页查询提示词
     */
    @ApiOperation("分页查询提示词")
    @ApiOperationSupport(order = 1)
    @PreAuthorize("@ss.hasPermi('common:prompt:list')")
    @GetMapping("/list")
    public R<IPage<CommonPromptVo>> list(CommonPromptVo commonPromptVo, Query query)
    {
        return R.ok(commonPromptManager.page(commonPromptVo, query));
    }

    /**
    * 查询提示词全部列表
    */
    @ApiOperation("查询提示词全部列表")
    @ApiOperationSupport(order = 2)
    @PreAuthorize("@ss.hasPermi('common:prompt:list')")
    @GetMapping("/allList")
    public R<List<CommonPromptVo>> allList(CommonPromptVo commonPromptVo){
        return R.ok(commonPromptManager.list(commonPromptVo));
    }

    /**
     * 导出提示词列表
     */
    @ApiOperation("导出提示词列表")
    @ApiOperationSupport(order = 3)
    @PreAuthorize("@ss.hasPermi('common:prompt:export')")
    @Log(title = "提示词", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommonPromptVo commonPromptVo, Query query)
    {
        List<CommonPromptVo> list = commonPromptManager.page(commonPromptVo, query).getRecords();
        ExcelUtil<CommonPromptVo> util = new ExcelUtil<CommonPromptVo>(CommonPromptVo.class);
        util.exportExcel(response, list, "提示词数据");
    }

    /**
     * 获取提示词详细信息
     */
    @ApiOperation("获取提示词详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "promptId", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('common:prompt:query')")
    @GetMapping(value = "/{promptId}")
    public R<CommonPromptVo> getInfo(@PathVariable("promptId") Long promptId)
    {
        return R.ok(commonPromptManager.getInfo(promptId));
    }

    /**
     * 新增提示词
     */
    @ApiOperation("新增提示词")
    @ApiOperationSupport(order = 4)
    @PreAuthorize("@ss.hasPermi('common:prompt:add')")
    @Log(title = "提示词", businessType = BusinessType.INSERT)
    @PostMapping
    public R<?> add(@Valid @RequestBody CommonPromptVo commonPromptVo)
    {
        return commonPromptManager.add(commonPromptVo) ? R.ok() : R.fail();
    }

    /**
     * 修改提示词
     */
    @ApiOperation("修改提示词")
    @ApiOperationSupport(order = 5)
    @PreAuthorize("@ss.hasPermi('common:prompt:edit')")
    @Log(title = "提示词", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody CommonPromptVo commonPromptVo)
    {
        return commonPromptManager.edit(commonPromptVo) ? R.ok() : R.fail();
    }

    /**
     * 删除提示词
     */
    @ApiOperation("删除提示词")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "promptIds", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @PreAuthorize("@ss.hasPermi('common:prompt:remove')")
    @Log(title = "提示词", businessType = BusinessType.DELETE)
    @DeleteMapping("/{promptIds}")
    public R<?> remove(@PathVariable Long[] promptIds)
    {
        return commonPromptManager.remove(promptIds) ? R.ok() : R.fail();
    }
}
