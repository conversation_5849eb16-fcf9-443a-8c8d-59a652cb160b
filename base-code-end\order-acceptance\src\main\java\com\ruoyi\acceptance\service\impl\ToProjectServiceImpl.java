package com.ruoyi.acceptance.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.acceptance.domain.ToOrderSub;
import com.ruoyi.acceptance.domain.ToProject;
import com.ruoyi.acceptance.dto.ToProjectTwoDTO;
import com.ruoyi.acceptance.mapper.ToOrderMapper;
import com.ruoyi.acceptance.mapper.ToOrderSubMapper;
import com.ruoyi.acceptance.mapper.ToProjectMapper;
import com.ruoyi.acceptance.service.IToProjectService;
import com.ruoyi.acceptance.vo.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 项目基本信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Service
public class ToProjectServiceImpl extends ServiceImpl<ToProjectMapper,ToProject> implements IToProjectService {

    @Autowired
    private ToProjectMapper toProjectMapper;
    @Autowired
    private ToOrderMapper toOrderMapper;
    @Autowired
    private ToOrderSubMapper toOrderSubMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToProject> queryWrapper(ToProject toProject) {
        LambdaQueryWrapper<ToProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(toProject.getTitle()), ToProject::getTitle, toProject.getTitle());
        return queryWrapper;
    }

    /**
     * 查询项目基本信息分页
     *
     * @param toProject 项目基本信息
     * @return 项目基本信息
     */
    @Override
    public IPage<ToProject> pages(ToProject toProject, IPage<ToProject> page)
    {
        return toProjectMapper.selectPage(page, this.queryWrapper(toProject));
    }

    /**
     * 查询项目基本信息列表
     * 
     * @param toProject 项目基本信息
     * @return 项目基本信息
     */
    @Override
    public List<ToProject> selectList(ToProject toProject)
    {
        return toProjectMapper.selectList(this.queryWrapper(toProject));
    }

    @Override
    public ToProject saveProjectFromWx(ToProject toProject) {

        baseMapper.insert(toProject);
        return toProject;


    }

    @Override
    public List<ToProject> selectListByUserOpenId(String openId) {
        LambdaQueryWrapper<ToProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ToProject::getCustomerOpenid,openId).orderByDesc(ToProject::getCreateTime);
        return baseMapper.selectList(queryWrapper);

    }

    @Override
    public List<ToProject> selectListByMerchantOpenId(String openId) {
        LambdaQueryWrapper<ToProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ToProject::getMerchantOpenid,openId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ToProject> selectOneByUserOpenId(String openId) {
        LambdaQueryWrapper<ToProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ToProject::getMerchantOpenid,openId);
        return baseMapper.selectList(queryWrapper);

    }

    @Override
    public List<ToProjectThreeVO> projectList(ToProjectTwoVO toProjectTwoVO) {
        return toProjectMapper.projectList(toProjectTwoVO);
    }

    @Override
    public ToProjectFourVO projectDetail(String projectId) {
        return toProjectMapper.projectDetail(projectId);
    }

    @Override
    public List<ToProjectSixVO> myProjectList(ToProjectTwoDTO toProjectTwoDTO) {
        return toProjectMapper.myProjectList(toProjectTwoDTO);
    }

    @Override
    public ToProjectFiveVO myProjectDetail(String projectId) {
        ToProjectFiveVO toProjectFiveVO = toProjectMapper.myProjectDetail(projectId);
        List<ToOrderSub> toOrderSubs = toOrderSubMapper.selectList(Wrappers.lambdaQuery(ToOrderSub.class).eq(ToOrderSub::getProjectId, projectId));
        List<ToOrderSubVO> projectSubList = new ArrayList<>();
        toOrderSubs.forEach(toOrderSub -> {
            ToOrderSubVO toOrderSubVO = new ToOrderSubVO();
            BeanUtils.copyProperties(toOrderSub,toOrderSubVO);
            projectSubList.add(toOrderSubVO);
        });
        toProjectFiveVO.setProjectSubList(projectSubList);
        return toProjectFiveVO;
    }

}
