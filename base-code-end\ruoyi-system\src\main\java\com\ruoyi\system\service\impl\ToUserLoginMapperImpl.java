package com.ruoyi.system.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.ruoyi.common.core.domain.entity.ToUserLogin;
import com.ruoyi.system.mapper.ToUserLoginMapper;
import com.ruoyi.system.service.IToUserLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 微信小程序登录
 *
 * @author: 天鸣
 * @date: 2024-08-16
 */
@Service
public class ToUserLoginMapperImpl implements IToUserLoginService {

    @Autowired
    private ToUserLoginMapper toUserLoginMapper;

    @Override
    public void save(ToUserLogin toUserLogin) {
        toUserLogin.setCreateTime(LocalDateTimeUtil.now());
        toUserLoginMapper.save(toUserLogin);
    }

    @Override
    public void updateById(ToUserLogin toUserLogin) {
        toUserLoginMapper.updateById(toUserLogin);
    }

    @Override
    public ToUserLogin getById(String openid) {
        return toUserLoginMapper.getById(openid);
    }
}
