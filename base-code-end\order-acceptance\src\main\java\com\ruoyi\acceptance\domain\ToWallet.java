package com.ruoyi.acceptance.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 钱包对象 to_wallet
 * 
 * <AUTHOR>
 * @date 2024-08-18
 */
@Data
@TableName("to_wallet")
public class ToWallet extends PoBaseEntity {
    private static final long serialVersionUID = 1L;
    /** 钱包id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /** 账户名称 */
    @TableField(value = "account_name")
    private String accountName;
    /** 账户余额 */
    @TableField(value = "balance")
    private BigDecimal balance;
    /** openid */
    @TableField(value = "open_id")
    private String openId;
    @TableField(value = "frozen_amount")
    private BigDecimal frozenAmount;
    @TableField(value = "not_withdraw_amount")
    private BigDecimal notWithdrawAmount;
    @TableField(value = "withdrawn_amount")
    private BigDecimal withdrawnAmount;
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

}
