package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToWalletHistory;
import com.ruoyi.app.vo.ToWalletHistoryVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 钱包流水PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToWalletHistoryConvert extends BeanPoVoMapper<ToWalletHistory, ToWalletHistoryVo> {

        ToWalletHistoryConvert INSTANCE = Mappers.getMapper(ToWalletHistoryConvert.class);

}
