package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.vo.ChatConversationsTwoVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.dispatch.service.IChatConversationsService;
import com.ruoyi.app.vo.ChatConversationsVo;
import com.ruoyi.app.vo.convert.ChatConversationsConvert;
import com.ruoyi.dispatch.domain.ChatConversations;

import java.util.List;

/**
 * 会话Manager
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Component
public class ChatConversationsManager {

    @Autowired
    private IChatConversationsService chatConversationsService;

    @Autowired
    private ChatConversationsConvert chatConversationsConvert;

    /**
    * 分页查询会话
    */
    public IPage<ChatConversationsVo> page(ChatConversationsVo chatConversationsVo, Query query){
        IPage<ChatConversations> page = chatConversationsService.pages(chatConversationsConvert.voToPo(chatConversationsVo), Condition.getPage(query));
        return (IPage<ChatConversationsVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ChatConversationsVo>(), ChatConversationsConvert.INSTANCE);
    }

    /**
    * 查询会话列表
    */
    public List<ChatConversationsVo> list(ChatConversationsVo chatConversationsVo) {
        List<ChatConversations> chatConversationsList = chatConversationsService.selectList(chatConversationsConvert.voToPo(chatConversationsVo));
        return chatConversationsConvert.poToVoList(chatConversationsList);
    }

    /**
     * 查询会话详细信息
     */
    public ChatConversationsVo getInfo(Long conversationId) {
        ChatConversations chatConversations = chatConversationsService.getById(conversationId);
        return chatConversationsConvert.poToVo(chatConversations);
    }

    /**
     * 新增会话
     */
    public boolean add(ChatConversationsVo chatConversationsVo) {
        return chatConversationsService.save(chatConversationsConvert.voToPo(chatConversationsVo));
    }

    /**
     * 修改会话
     */
    public boolean edit(ChatConversationsVo chatConversationsVo) {
        return chatConversationsService.updateById(chatConversationsConvert.voToPo(chatConversationsVo));
    }

    /**
     * 批量删除会话
     */
    public boolean remove(Long[] conversationIds) {
        return chatConversationsService.removeByIds(CollUtil.toList(conversationIds));
    }

    public R<?> saveConversation(ChatConversationsTwoVO chatConversationsTwoVO) {
        return chatConversationsService.saveConversation(chatConversationsTwoVO);
    }
}
