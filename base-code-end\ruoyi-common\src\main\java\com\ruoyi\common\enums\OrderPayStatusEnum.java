package com.ruoyi.common.enums;

import lombok.Getter;

/**
 * 订单支付状态枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum OrderPayStatusEnum {
    
    REFUNDED(0, "已退款"),
    PENDING_DEPOSIT(1, "待付定金"),
    PAID_REFUNDABLE_DEPOSIT(2, "已支付可退定金"),
    PAID_NON_REFUNDABLE_DEPOSIT(3, "已支付不可退定金"),
    PENDING_FINAL_PAYMENT(4, "尾款待支付"),
    COMPLETED(5, "交易结束");
    
    private final Integer code;
    private final String desc;
    
    OrderPayStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    

    
    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 枚举对象
     */
    public static OrderPayStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (OrderPayStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        return null;
    }
    
    /**
     * 判断状态码是否有效
     * 
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
