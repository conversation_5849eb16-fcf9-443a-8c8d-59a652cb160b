package com.ruoyi.app.vo.convert;

import com.ruoyi.app.vo.ToWalletVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import com.ruoyi.acceptance.domain.ToWallet;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 钱包PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-08-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToWalletConvert extends BeanPoVoMapper<ToWallet, ToWalletVo> {

        ToWalletConvert INSTANCE = Mappers.getMapper(ToWalletConvert.class);

}
