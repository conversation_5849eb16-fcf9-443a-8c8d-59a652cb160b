<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dispatch.mapper.ChatMessagesMapper">

    <select id="merchantMessageList" resultType="com.ruoyi.acceptance.vo.ChatMessagesTwoVO">
        SELECT
        aa.*,bb.content new_msg
        from (
            SELECT
            a.conversation_id,a.sender_id,count(a.message_id) message_num,c.head_sculpture,MAX(a.create_time) last_time,c.nickname,MAX(a.message_id) message_id
            from chat_messages  a
            left join chat_conversation_members b on a.conversation_id = b.conversation_id
            left join to_user c on c.open_id = a.sender_id
            where
            b.user_openid = "oG9u35LrN0rYBqosDTLbh56L1H30"
            and a.sender_id != "oG9u35LrN0rYBqosDTLbh56L1H30"
            GROUP BY a.conversation_id,a.sender_id,c.head_sculpture,c.nickname
        ) aa
        left join chat_messages bb on aa.message_id = bb.message_id
    </select>

    <select id="customerMessageList" resultType="com.ruoyi.acceptance.vo.ChatMessagesTwoVO">
        SELECT
        a.conversation_id,a.sender_id,count(a.message_id) message_num,c.head_sculpture,MAX(a.create_time) last_time
        from chat_messages  a
        left join chat_conversation_members b on a.conversation_id = b.conversation_id
        left join do_user c on c.open_id = a.sender_id
        where
        b.user_openid = #{customerOpenid}
        and a.sender_id != #{customerOpenid}
        GROUP BY a.conversation_id,a.sender_id,c.head_sculpture
    </select>

</mapper>