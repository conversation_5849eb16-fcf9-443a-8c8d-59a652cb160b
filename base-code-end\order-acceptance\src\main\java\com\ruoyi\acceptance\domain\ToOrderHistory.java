package com.ruoyi.acceptance.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 订单流水对象 to_order_history
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Data
@TableName("to_order_history")
public class ToOrderHistory extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 订单ID */
    @TableField(value = "order_id")
    private Long orderId;

    /** 代理的OpenID */
    @TableField(value = "agent_open_id")
    private String agentOpenId;

    /** 客户的OpenID */
    @TableField(value = "customer_open_id")
    private String customerOpenId;

    /** 订单付款状态（0已退款1待付定金2已支付可退定金3已支付不可退定金4尾款待支付5交易结束） */
    @TableField(value = "order_pay_status")
    private String orderPayStatus;

    /** web端用户id */
    @TableField(value = "user_id")
    private String userId;

    /** 冻结金额 */
    @TableField(value = "frozen_amount")
    private BigDecimal frozenAmount;

    /** 待提现金额 */
    @TableField(value = "not_withdraw_amount")
    private BigDecimal notWithdrawAmount;

    /** 已提现金额 */
    @TableField(value = "withdrawn_amount")
    private BigDecimal withdrawnAmount;

    /** 总金额 */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

}
