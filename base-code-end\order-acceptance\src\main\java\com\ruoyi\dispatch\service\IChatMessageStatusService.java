package com.ruoyi.dispatch.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.dispatch.domain.ChatMessageStatus;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 消息状态Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IChatMessageStatusService extends IService<ChatMessageStatus> {

    /**
     * 分页查询消息状态
     * @param chatMessageStatus 消息状态
     * @param page 分页条件
     * @return 消息状态集合
     */
    IPage<ChatMessageStatus> pages(ChatMessageStatus chatMessageStatus, IPage<ChatMessageStatus> page);

    /**
     * 查询消息状态列表
     * 
     * @param chatMessageStatus 消息状态
     * @return 消息状态集合
     */
     List<ChatMessageStatus> selectList(ChatMessageStatus chatMessageStatus);

}
