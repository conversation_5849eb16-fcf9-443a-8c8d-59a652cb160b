package com.ruoyi.system.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.OssFile;

/**
 * 文件管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IOssFileService extends IService<OssFile> {

    /**
     * 分页查询文件管理
     * @param ossFile 文件管理
     * @param page 分页条件
     * @return 文件管理集合
     */
    IPage<OssFile> pages(OssFile ossFile, IPage<OssFile> page);

    /**
     * 查询文件管理列表
     * 
     * @param ossFile 文件管理
     * @return 文件管理集合
     */
     List<OssFile> selectList(OssFile ossFile);

}
