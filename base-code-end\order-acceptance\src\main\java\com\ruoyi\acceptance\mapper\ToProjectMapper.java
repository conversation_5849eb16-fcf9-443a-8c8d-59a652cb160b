package com.ruoyi.acceptance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.acceptance.domain.ToProject;
import com.ruoyi.acceptance.dto.ToProjectTwoDTO;
import com.ruoyi.acceptance.vo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 项目基本信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Repository
public interface ToProjectMapper extends BaseMapper<ToProject> {


    List<ToProjectThreeVO> projectList(ToProjectTwoVO toProjectTwoVO);

    ToProjectFourVO projectDetail(@Param("projectId") String projectId);

    List<ToProjectSixVO> myProjectList(ToProjectTwoDTO toProjectTwoDTO);

    ToProjectFiveVO myProjectDetail(@Param("projectId") String projectId);
}
