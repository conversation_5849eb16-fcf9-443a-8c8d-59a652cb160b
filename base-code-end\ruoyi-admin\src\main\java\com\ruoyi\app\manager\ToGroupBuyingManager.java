package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.domain.ToGroupBuying;
import com.ruoyi.acceptance.service.IToGroupBuyingService;
import com.ruoyi.acceptance.vo.GroupInfoVO;
import com.ruoyi.acceptance.vo.MyGroupOrderVO;
import com.ruoyi.app.vo.ToGroupBuyingVo;
import com.ruoyi.app.vo.convert.ToGroupBuyingConvert;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 团购信息Manager
 *
 * <AUTHOR>
 * @date 2024-08-11
 */
@Component
public class ToGroupBuyingManager {

    @Autowired
    private IToGroupBuyingService toGroupBuyingService;

    @Autowired
    private ToGroupBuyingConvert toGroupBuyingConvert;

    /**
    * 分页查询团购信息
    */
    public IPage<ToGroupBuyingVo> page(ToGroupBuyingVo toGroupBuyingVo, Query query){
        IPage<ToGroupBuying> page = toGroupBuyingService.pages(toGroupBuyingConvert.voToPo(toGroupBuyingVo), Condition.getPage(query));
        return (IPage<ToGroupBuyingVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToGroupBuyingVo>(), ToGroupBuyingConvert.INSTANCE);
    }

    /**
    * 查询团购信息列表
    */
    public List<ToGroupBuyingVo> list(ToGroupBuyingVo toGroupBuyingVo) {
        List<ToGroupBuying> toGroupBuyingList = toGroupBuyingService.selectList(toGroupBuyingConvert.voToPo(toGroupBuyingVo));
        return toGroupBuyingConvert.poToVoList(toGroupBuyingList);
    }

    /**
     * 查询团购信息详细信息
     */
    public ToGroupBuyingVo getInfo(Long id) {
        ToGroupBuying toGroupBuying = toGroupBuyingService.getById(id);
        return toGroupBuyingConvert.poToVo(toGroupBuying);
    }

    /**
     * 查询团购信息详细信息
     */
    public GroupInfoVO getGroupInfoByPaperId(String groupId) {
        return toGroupBuyingService.getGroupInfoByPaperId(groupId);
    }

    /**
     * 新增团购信息
     */
    public R add(Long orderId) {
        return toGroupBuyingService.saveToGroupBuying(orderId);
    }

    /**
     * 新增团购信息
     */
    public boolean addGroup(ToGroupBuyingVo toGroupBuyingVo) {
        return toGroupBuyingService.acceptGroupBuying(toGroupBuyingConvert.voToPo(toGroupBuyingVo));
    }

    /**
     * 修改团购信息
     */
    public boolean edit(ToGroupBuyingVo toGroupBuyingVo) {
        return toGroupBuyingService.updateById(toGroupBuyingConvert.voToPo(toGroupBuyingVo));
    }

    /**
     * 批量删除团购信息
     */
    public boolean remove(Long[] ids) {
        return toGroupBuyingService.removeByIds(CollUtil.toList(ids));
    }

    public List<MyGroupOrderVO> myGroupOrderList() {
        return toGroupBuyingService.myGroupOrderList();
    }

}
