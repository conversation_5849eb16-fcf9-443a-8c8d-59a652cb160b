package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.domain.ToOrderHistory;
import com.ruoyi.acceptance.service.IToOrderHistoryService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.app.vo.ToOrderHistoryVo;
import com.ruoyi.app.vo.convert.ToOrderHistoryConvert;

import java.util.List;

/**
 * 订单流水Manager
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Component
public class ToOrderHistoryManager {

    @Autowired
    private IToOrderHistoryService toOrderHistoryService;

    @Autowired
    private ToOrderHistoryConvert toOrderHistoryConvert;

    /**
    * 分页查询订单流水
    */
    public IPage<ToOrderHistoryVo> page(ToOrderHistoryVo toOrderHistoryVo, Query query){
        IPage<ToOrderHistory> page = toOrderHistoryService.pages(toOrderHistoryConvert.voToPo(toOrderHistoryVo), Condition.getPage(query));
        return (IPage<ToOrderHistoryVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToOrderHistoryVo>(), ToOrderHistoryConvert.INSTANCE);
    }

    /**
    * 查询订单流水列表
    */
    public List<ToOrderHistoryVo> list(ToOrderHistoryVo toOrderHistoryVo) {
        List<ToOrderHistory> toOrderHistoryList = toOrderHistoryService.selectList(toOrderHistoryConvert.voToPo(toOrderHistoryVo));
        return toOrderHistoryConvert.poToVoList(toOrderHistoryList);
    }

    /**
     * 查询订单流水详细信息
     */
    public ToOrderHistoryVo getInfo(Long id) {
        ToOrderHistory toOrderHistory = toOrderHistoryService.getById(id);
        return toOrderHistoryConvert.poToVo(toOrderHistory);
    }

    /**
     * 新增订单流水
     */
    public boolean add(ToOrderHistoryVo toOrderHistoryVo) {
        return toOrderHistoryService.save(toOrderHistoryConvert.voToPo(toOrderHistoryVo));
    }

    /**
     * 修改订单流水
     */
    public boolean edit(ToOrderHistoryVo toOrderHistoryVo) {
        return toOrderHistoryService.updateById(toOrderHistoryConvert.voToPo(toOrderHistoryVo));
    }

    /**
     * 批量删除订单流水
     */
    public boolean remove(Long[] ids) {
        return toOrderHistoryService.removeByIds(CollUtil.toList(ids));
    }

    public R<?> distributionReport(String date) {
        return toOrderHistoryService.distributionReport(date);
    }

    public R<?> invitationRanking() {
        return toOrderHistoryService.invitationRanking();
    }

    public R<?> commissionRanking() {
        return toOrderHistoryService.commissionRanking();
    }

    public R<?> transactionList(String date) {
        return toOrderHistoryService.transactionList(date);
    }
}
