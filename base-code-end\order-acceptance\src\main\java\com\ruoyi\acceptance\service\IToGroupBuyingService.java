package com.ruoyi.acceptance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.domain.ToGroupBuying;
import com.ruoyi.acceptance.dto.GroupInfoTwoDTO;
import com.ruoyi.acceptance.vo.GroupInfoVO;
import com.ruoyi.acceptance.vo.MyGroupOrderVO;
import com.ruoyi.common.core.domain.R;

import java.util.List;

/**
 * 团购信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-11
 */
public interface IToGroupBuyingService extends IService<ToGroupBuying> {

    /**
     * 分页查询团购信息
     * @param toGroupBuying 团购信息
     * @param page 分页条件
     * @return 团购信息集合
     */
    IPage<ToGroupBuying> pages(ToGroupBuying toGroupBuying, IPage<ToGroupBuying> page);

    /**
     * 查询团购信息列表
     * 
     * @param toGroupBuying 团购信息
     * @return 团购信息集合
     */
     List<ToGroupBuying> selectList(ToGroupBuying toGroupBuying);

    R saveToGroupBuying(Long orderId);

     boolean acceptGroupBuying(ToGroupBuying toGroupBuying);

    GroupInfoVO getGroupInfoByPaperId(String groupId);

    boolean addGroup(ToGroupBuying toGroupBuying);

    GroupInfoTwoDTO selectGroupCountdown(Long orderId);

    List<MyGroupOrderVO> myGroupOrderList();
}
