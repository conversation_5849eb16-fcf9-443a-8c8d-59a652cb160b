package com.ruoyi.acceptance.service.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.acceptance.dto.ToOrderDTO;
import com.ruoyi.acceptance.vo.ToOrderVo;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.acceptance.mapper.ToOrderMapper;
import com.ruoyi.acceptance.domain.ToOrder;
import com.ruoyi.acceptance.service.IToOrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 接单订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class ToOrderServiceImpl extends ServiceImpl<ToOrderMapper,ToOrder> implements IToOrderService {

    @Autowired
    private ToOrderMapper toOrderMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ToOrder> queryWrapper(ToOrder toOrder) {
        LambdaQueryWrapper<ToOrder> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getPaperId()), ToOrder::getPaperId, toOrder.getPaperId());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getAdvancePrice()), ToOrder::getAdvancePrice, toOrder.getAdvancePrice());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getBalancePayment()), ToOrder::getBalancePayment, toOrder.getBalancePayment());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getDepositPaymentTime()), ToOrder::getDepositPaymentTime, toOrder.getDepositPaymentTime());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getTotalPrice()), ToOrder::getTotalPrice, toOrder.getTotalPrice());
//        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getDeliveryTime()), ToOrder::getDeliveryTime, toOrder.getDeliveryTime());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getApplyRefund()), ToOrder::getApplyRefund, toOrder.getApplyRefund());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getGroupPurchase()), ToOrder::getGroupPurchase, toOrder.getGroupPurchase());
        queryWrapper.eq(ObjUtil.isNotEmpty(toOrder.getSettlement()), ToOrder::getSettlement, toOrder.getSettlement());
        return queryWrapper;
    }

    /**
     * 查询接单订单分页
     *
     * @param toOrder 接单订单
     * @return 接单订单
     */
    @Override
    public IPage<ToOrder> pages(ToOrder toOrder, IPage<ToOrder> page)
    {
        return toOrderMapper.selectPage(page, this.queryWrapper(toOrder));
    }

    /**
     * 查询接单订单列表
     * 
     * @param toOrder 接单订单
     * @return 接单订单
     */
    @Override
    public List<ToOrder> selectList(ToOrder toOrder)
    {
        return toOrderMapper.selectList(this.queryWrapper(toOrder));
    }

    @Override
    public ToOrder selectOneById(Long id) {
        return toOrderMapper.selectById(id);
    }

    @Override
    public ToOrder selectOneByOutTradeNo(String outTradeNo) {
        QueryWrapper<ToOrder> wrapper = new QueryWrapper<>();
        wrapper.eq("out_trade_no",outTradeNo);
        return toOrderMapper.selectOne(wrapper);
    }

    @Override
    public IPage<ToOrderDTO> selectOrders(ToOrderVo toOrderVo, IPage<ToOrderDTO> page) {
        return toOrderMapper.selectOrders(toOrderVo,page);
    }

    @Override
    public ToOrderDTO getInfoForPc(Long id) {
        return toOrderMapper.getInfoForPc(id);
    }

    @Override
    public boolean addWxUserToOrder(ToOrder toOrder) {
        //只处理订单金钱的问题

        //项目订单id在传入之前处理
        //toOrder.setProjectId(toProject.getId().toString());
        //设置定金、尾款
        if (toOrder.getTotalPrice() != null) {
            BigDecimal totalPrice = toOrder.getTotalPrice();
            BigDecimal halfPrice = totalPrice.divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_HALF_UP);
            toOrder.setAdvancePrice(halfPrice);
            toOrder.setBalancePayment(halfPrice);
        }
        toOrder.setGroupPurchase(0);
        toOrder.setSettlement(0);
        toOrder.setOrderPayStatus(1);
        return baseMapper.insert(toOrder) >0;

    }

    @Override
    public ToOrder selectOneByProjectId(Long projectId) {

        LambdaQueryWrapper<ToOrder> toOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        toOrderLambdaQueryWrapper.eq(ToOrder::getProjectId,projectId);
        return baseMapper.selectOne(toOrderLambdaQueryWrapper);
    }

    @Override
    public List<ToOrder> selectByProjectIds(List<Long> projectIds) {
        LambdaQueryWrapper<ToOrder> toOrderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        toOrderLambdaQueryWrapper.in(ToOrder::getProjectId,projectIds);
        return baseMapper.selectList(toOrderLambdaQueryWrapper);
    }


}
