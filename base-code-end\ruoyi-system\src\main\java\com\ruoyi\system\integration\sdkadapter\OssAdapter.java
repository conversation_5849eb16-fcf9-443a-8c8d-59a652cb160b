package com.ruoyi.system.integration.sdkadapter;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.*;
import com.ruoyi.common.config.OssConfig;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.integration.dto.OssResultDto;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 阿里云OSS工具类
 *
 * <AUTHOR>
 */
public class OssAdapter {

    private static final Logger log = LoggerFactory.getLogger(OssAdapter.class);

    /**
     * oss 工具客户端
     */
    private static volatile OSSClient ossClient = null;

    /**
     * 单例模式
     * 初始化 oss 客户端
     */
    private static OSSClient initOSS() {
        if (ossClient == null) {
            synchronized (OssAdapter.class) {
                if (ossClient == null) {
                    ossClient = new OSSClient(OssConfig.OSS_END_POINT,
                            new DefaultCredentialProvider(OssConfig.OSS_ACCESS_KEY_ID, OssConfig.OSS_ACCESS_KEY_SECRET),
                            new ClientConfiguration());
                }
            }
        }
        return ossClient;
    }

    /**
     * 上传文件-自定义路径
     *
     * @param file 上传文件
     * @param path 上传至OSS的文件完整路径，例：example/img
     * @return
     */
    public static OssResultDto uploadFile(MultipartFile file, String path) {
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.error("上传异常", e);
            return new OssResultDto(false, null, null, e.getMessage());
        }
        String fileType = file.getContentType();
        String fileName = setFilePath(file, path);
        String oldFilename = file.getOriginalFilename();
        // 上传文件
        return uploadInputStream(inputStream, fileType, oldFilename, fileName);
    }

    /**
     * 上传文件-yml配置路径
     *
     * @param file 上传文件
     * @return
     */
    public static OssResultDto uploadFile(MultipartFile file) {
        // 文件流
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.error("上传异常", e);
            return new OssResultDto(false, null, null, e.getMessage());
        }
        // 获取文件类型
        String fileType = file.getContentType();
        String fileName = setFilePath(file, null);
        String oldFilename = file.getOriginalFilename();
        // 上传文件
        return uploadInputStream(inputStream, fileType, oldFilename, fileName);
    }

    /**
     * 上传文件-自定义路径
     *
     * @param inputStream 上传文件流
     * @param fileType    文件类型，例：png
     * @param fileName    上传至OSS的文件完整路径，例：cf/abc.png
     *                    上传至根目录，例：abc.png
     * @return
     */
    public static OssResultDto uploadInputStream(InputStream inputStream, String fileType, String oldFilename, String fileName) {
        if (inputStream == null) {
            return new OssResultDto(false, null, null, "文件不能为空");
        }
        // 上传文件最大值 MB->bytes
        long maxSize = OssConfig.OSS_MAX_SIZE * 1024 * 1024;
        // 本次上传文件的大小
        long fileSize = getInputStreamSize(inputStream);
        if (fileSize <= 0 || fileSize > maxSize) {
            return new OssResultDto(false, null, null, "请检查文件大小");
        }

        // 上传文件
        return putFile(inputStream, fileType, oldFilename, fileSize, fileName);
    }

    /**
     * 上传文件
     *
     * @param input    上传文件流
     * @param fileType 文件类型，例：png
     * @param fileName 上传至OSS的文件完整路径，例：cf/abc.png
     *                 上传至根目录，例：abc.png
     * @return
     */
    private static OssResultDto putFile(InputStream input, String fileType, String oldFilename, Long fileSize, String fileName) {
        ossClient = initOSS();
        try {
            // 创建上传Object的Metadata
            ObjectMetadata meta = new ObjectMetadata();
            // 设置上传内容类型
            meta.setContentType(fileType);
            //被下载时网页的缓存行为
            meta.setCacheControl("no-cache");
            //创建上传请求
            PutObjectRequest request = new PutObjectRequest(OssConfig.OSS_BUCKET_NAME, fileName, input, meta);
            //上传文件
            ossClient.putObject(request);
            //预览地址
            String url = OssConfig.OSS_DNS + "/" + fileName;
            //获取上传成功的文件地址
            return new OssResultDto(true, fileName, oldFilename, fileSize, url, "上传成功");
        } catch (OSSException | ClientException e) {
            log.error("上传异常", e);
            return new OssResultDto(false, fileName, null, e.getMessage());
        }
    }

    /**
     * 根据文件名生成文件的访问地址（带过期时间）
     *
     * @param fileName 文件名
     * @return
     */
    public static String getOssUrl(String fileName) {
        ossClient = initOSS();
        // 生成过期时间
        long expireEndTime = System.currentTimeMillis() + OssConfig.OSS_POLICY_EXPIRE * 1000;
        Date expiration = new Date(expireEndTime);
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(OssConfig.OSS_BUCKET_NAME, fileName);
        generatePresignedUrlRequest.setExpiration(expiration);
        URL url = ossClient.generatePresignedUrl(generatePresignedUrlRequest);
        return url.toString();
    }

    /**
     * 通过文件名下载文件
     *
     * @param fileName      要下载的文件名（OSS服务器上的）
     *                      例如：4DB049D0604047989183CB68D76E969D.jpg
     * @param localFileName 本地要创建的文件名（下载到本地的）
     *                      例如：C:\Users\<USER>\Desktop\test.jpg
     */
    public static void downloadFile(String fileName, String localFileName) {
        ossClient = initOSS();
        // 下载OSS文件到指定目录。如果指定的本地文件存在会覆盖，不存在则新建。
        ossClient.getObject(new GetObjectRequest(OssConfig.OSS_BUCKET_NAME, fileName), new File(localFileName));
    }

    /**
     * 通过文件名获取文件流
     *
     * @param fileName 要下载的文件名（OSS服务器上的）
     *                 例如：4DB049D0604047989183CB68D76E969D.jpg
     */
    public static InputStream getInputStream(String fileName) {
        ossClient = initOSS();
        // 下载OSS文件到本地文件。如果指定的本地文件存在会覆盖，不存在则新建。
        return ossClient.getObject(new GetObjectRequest(OssConfig.OSS_BUCKET_NAME, fileName)).getObjectContent();
    }

    /**
     * 通过文件名获取byte[]
     *
     * @param fileName 要下载的文件名（OSS服务器上的）
     *                 例如：4DB049D0604047989183CB68D76E969D.jpg
     */
    public static byte[] getBytes(String fileName) {
        InputStream inputStream = getInputStream(fileName);
        FastByteArrayOutputStream fastByteArrayOutputStream = IoUtil.read(inputStream);
        return fastByteArrayOutputStream.toByteArray();
    }

    /**
     * 根据文件名删除文件
     *
     * @param fileName 需要删除的文件名
     * @return boolean 是否删除成功
     * 例如：4DB049D0604047989183CB68D76E969D.jpg
     */
    public static boolean deleteFile(String fileName) {
        ossClient = initOSS();
        try {
            if (OssConfig.OSS_BUCKET_NAME == null || fileName == null) {
                return false;
            }
            GenericRequest request = new DeleteObjectsRequest(OssConfig.OSS_BUCKET_NAME).withKey(fileName);
            ossClient.deleteObject(request);
        } catch (Exception e) {
            log.error("上传异常", e);
            return false;
        }
        return true;
    }

    /**
     * 列举所有的文件url
     */
    public static List<String> urlList() {
        ossClient = initOSS();
        List<String> list = new ArrayList<>();
        // 构造ListObjectsRequest请求
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(OssConfig.OSS_BUCKET_NAME);
        // 列出文件
        ObjectListing listing = ossClient.listObjects(listObjectsRequest);
        // 遍历所有文件
        for (OSSObjectSummary objectSummary : listing.getObjectSummaries()) {
            // 把key全部转化成可以访问的url
            String url = getOssUrl(objectSummary.getKey());
            list.add(url);
        }
        return list;
    }

    /**
     * 现象：inputStream.available() == 0 但实际文件不为空
     * 原因：从网络中读取InputStream后，可能因网络质量一次读取后InputStream长度为0，这里最多读5次，没读到就视为0
     */
    private static long getInputStreamSize(InputStream inputStream) {
        long fileSize = 0;
        int cunt = 1;
        try {
            while (fileSize == 0 && cunt <= 5) {
                fileSize = inputStream.available();
                cunt++;
            }
        } catch (IOException ignored) {
        }
        return fileSize;
    }

    /**
     * setFilePath 设置文件所在文件夹
     */
    public static String setFilePath(MultipartFile file, String path) {
        String fileName;
        String fileNameStr = setFileName(file);
        if (StrUtil.isNotBlank(path)) {
            fileName = path + "/" + DateUtils.dateTime() + "/" + fileNameStr;
        } else {
            fileName = OssConfig.OSS_FILE_PATH + "/" + DateUtils.dateTime() + "/" + fileNameStr;
        }
        return fileName;


    }

    /**
     * fileName 重新命名文件,防止文件名重复,默认uuid
     */
    public static String setFileName(MultipartFile file) {
        String fileNameStr;
        String suffixName = FilenameUtils.getExtension(file.getOriginalFilename());
        if ("timestamp".equals(OssConfig.OSS_FILE_NAME)) {
            fileNameStr = System.currentTimeMillis() + "" + new SecureRandom().nextInt(0x0400);
        } else {
            fileNameStr = IdUtils.fastSimpleUUID();
        }
        // 生成上传文件名
        fileNameStr = fileNameStr + "." + suffixName;
        return fileNameStr;


    }
}
