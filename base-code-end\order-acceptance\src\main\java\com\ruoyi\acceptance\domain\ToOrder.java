package com.ruoyi.acceptance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.PoBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 接单订单对象 to_order
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@TableName("to_order")
public class ToOrder extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "customer_openid")
    private String customOpenId;


    @TableField(value = "project_id")
    private String projectId;

    /** 预付款价 */
    @TableField(value = "advance_price")
    private BigDecimal advancePrice;

    /** 尾款 */
    @TableField(value = "balance_payment")
    private BigDecimal balancePayment;

    /** 定金支付时间 */
    @TableField(value = "deposit_payment_time")
    private Date depositPaymentTime;

    /** 尾款支付时间 */
    @TableField(value = "balance_payment_time")
    private Date balancePaymentTime;

    /** 全额价格 */
    @TableField(value = "total_price")
    private BigDecimal totalPrice;



    /** 是否申请退款 */
    @TableField(value = "apply_refund")
    private Integer applyRefund;

    /** 是否团购 */
    @TableField(value = "group_purchase")
    private Integer groupPurchase;

    /** 是否结清尾款 */
    @TableField(value = "settlement")
    private Integer settlement;

    /** 订单付款状态 */
    @Excel(name = "订单付款状态")
    @ApiModelProperty(value = "订单付款状态", name="orderPayStatus")
    private Integer orderPayStatus;

    /** 微信支付商户订单号 */
    @Excel(name = "微信支付商户订单号")
    @ApiModelProperty(value = "微信支付商户订单号", name="outTradeNo")
    private String outTradeNo;

    /** 微信订单号 */
    @Excel(name = "微信订单号")
    @ApiModelProperty(value = "微信订单号", name="transactionId")
    private String transactionId;


}
