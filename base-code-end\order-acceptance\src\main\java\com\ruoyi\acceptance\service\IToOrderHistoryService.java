package com.ruoyi.acceptance.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.acceptance.domain.ToOrderHistory;
import com.ruoyi.common.core.domain.R;

/**
 * 订单流水Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IToOrderHistoryService extends IService<ToOrderHistory> {

    /**
     * 分页查询订单流水
     * @param toOrderHistory 订单流水
     * @param page 分页条件
     * @return 订单流水集合
     */
    IPage<ToOrderHistory> pages(ToOrderHistory toOrderHistory, IPage<ToOrderHistory> page);

    /**
     * 查询订单流水列表
     * 
     * @param toOrderHistory 订单流水
     * @return 订单流水集合
     */
     List<ToOrderHistory> selectList(ToOrderHistory toOrderHistory);

     R<?> distributionReport(String date);

    R<?> invitationRanking();

    R<?> commissionRanking();

    R<?> transactionList(String date);

    void addWxUserToOrderHistory(Long id, String openId, String agentOpenId);
}
