package com.ruoyi.api.vo.convert;

import com.ruoyi.api.vo.OssFileVo;
import com.ruoyi.system.domain.OssFile;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OssFileConvertImpl implements OssFileConvert {

    @Override
    public OssFileVo poToVo(OssFile arg0) {
        if ( arg0 == null ) {
            return null;
        }

        OssFileVo ossFileVo = new OssFileVo();

        ossFileVo.setFileName( arg0.getFileName() );
        if ( arg0.getFileSize() != null ) {
            ossFileVo.setFileSize( String.valueOf( arg0.getFileSize() ) );
        }
        ossFileVo.setOldFileName( arg0.getOldFileName() );
        ossFileVo.setUrl( arg0.getUrl() );

        return ossFileVo;
    }

    @Override
    public List<OssFileVo> poToVoList(List<OssFile> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<OssFileVo> list = new ArrayList<OssFileVo>( arg0.size() );
        for ( OssFile ossFile : arg0 ) {
            list.add( poToVo( ossFile ) );
        }

        return list;
    }

    @Override
    public OssFile voToPo(OssFileVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        OssFile ossFile = new OssFile();

        ossFile.setFileName( arg0.getFileName() );
        if ( arg0.getFileSize() != null ) {
            ossFile.setFileSize( Long.parseLong( arg0.getFileSize() ) );
        }
        ossFile.setOldFileName( arg0.getOldFileName() );
        ossFile.setUrl( arg0.getUrl() );

        return ossFile;
    }

    @Override
    public List<OssFile> voToPoList(List<OssFileVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<OssFile> list = new ArrayList<OssFile>( arg0.size() );
        for ( OssFileVo ossFileVo : arg0 ) {
            list.add( voToPo( ossFileVo ) );
        }

        return list;
    }
}
