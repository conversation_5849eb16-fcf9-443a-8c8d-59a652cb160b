package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToProject;
import com.ruoyi.app.vo.ToProjectVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToProjectConvertImpl implements ToProjectConvert {

    @Override
    public ToProjectVo poToVo(ToProject arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToProjectVo toProjectVo = new ToProjectVo();

        toProjectVo.setActualDeliveryTime( arg0.getActualDeliveryTime() );
        toProjectVo.setAdditionalServices( arg0.getAdditionalServices() );
        toProjectVo.setAttachments( arg0.getAttachments() );
        toProjectVo.setCategorySelectionOne( arg0.getCategorySelectionOne() );
        toProjectVo.setCategorySelectionTwo( arg0.getCategorySelectionTwo() );
        toProjectVo.setCustomerOpenid( arg0.getCustomerOpenid() );
        toProjectVo.setExpectedDeliveryTime( arg0.getExpectedDeliveryTime() );
        toProjectVo.setGroupMembers( arg0.getGroupMembers() );
        toProjectVo.setId( arg0.getId() );
        toProjectVo.setIsNotForSale( arg0.getIsNotForSale() );
        toProjectVo.setMerchantOpenid( arg0.getMerchantOpenid() );
        toProjectVo.setOrderId( arg0.getOrderId() );
        toProjectVo.setProjectEndTime( arg0.getProjectEndTime() );
        toProjectVo.setRequirementDescription( arg0.getRequirementDescription() );
        toProjectVo.setStartTime( arg0.getStartTime() );
        toProjectVo.setTitle( arg0.getTitle() );

        return toProjectVo;
    }

    @Override
    public List<ToProjectVo> poToVoList(List<ToProject> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToProjectVo> list = new ArrayList<ToProjectVo>( arg0.size() );
        for ( ToProject toProject : arg0 ) {
            list.add( poToVo( toProject ) );
        }

        return list;
    }

    @Override
    public ToProject voToPo(ToProjectVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToProject toProject = new ToProject();

        toProject.setActualDeliveryTime( arg0.getActualDeliveryTime() );
        toProject.setAdditionalServices( arg0.getAdditionalServices() );
        toProject.setAttachments( arg0.getAttachments() );
        toProject.setCategorySelectionOne( arg0.getCategorySelectionOne() );
        toProject.setCategorySelectionTwo( arg0.getCategorySelectionTwo() );
        toProject.setCustomerOpenid( arg0.getCustomerOpenid() );
        toProject.setExpectedDeliveryTime( arg0.getExpectedDeliveryTime() );
        toProject.setGroupMembers( arg0.getGroupMembers() );
        toProject.setId( arg0.getId() );
        toProject.setIsNotForSale( arg0.getIsNotForSale() );
        toProject.setMerchantOpenid( arg0.getMerchantOpenid() );
        toProject.setOrderId( arg0.getOrderId() );
        toProject.setProjectEndTime( arg0.getProjectEndTime() );
        toProject.setRequirementDescription( arg0.getRequirementDescription() );
        toProject.setStartTime( arg0.getStartTime() );
        toProject.setTitle( arg0.getTitle() );

        return toProject;
    }

    @Override
    public List<ToProject> voToPoList(List<ToProjectVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToProject> list = new ArrayList<ToProject>( arg0.size() );
        for ( ToProjectVo toProjectVo : arg0 ) {
            list.add( voToPo( toProjectVo ) );
        }

        return list;
    }
}
