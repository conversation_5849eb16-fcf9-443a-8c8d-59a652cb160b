package com.ruoyi.web.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.CommonPromptProjectType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 提示词对象 CommonPromptVo
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ApiModel(value = " CommonPromptVo", description = "提示词对象VO")
public class CommonPromptVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long promptId;

    /** AI类型（如qwen） */
    @Excel(name = "AI类型")
    @NotBlank(message="AI类型不能为空")
    @ApiModelProperty(value = "AI类型", name="aiType", required = true)
    private String aiType;


    /** 系统人设 */
    @Excel(name = "AI类型")
    @NotBlank(message="系统人设不能为空")
    @ApiModelProperty(value = "系统人设", name="systemPrompt", required = true)
    private String systemPrompt;


    /** 提示词标题 */
    @Excel(name = "提示词标题")
    @NotBlank(message="提示词标题不能为空")
    @ApiModelProperty(value = "提示词标题", name="promptTitle", required = true)
    private String promptTitle;

    /** 提示词内容 */
    @Excel(name = "提示词内容")
    @NotBlank(message="提示词内容不能为空")
    @ApiModelProperty(value = "提示词内容", name="content", required = true)
    private String content;

    /** 是否启用 */
    @Excel(name = "是否启用")
    @NotNull(message = "是否启用不能为空")
    @ApiModelProperty(value = "是否启用", name="isActive", required = true)
    private Integer isActive;


    @Excel(name = "是否启用深度思考")
    @NotNull(message = "是否启用深度思考不能为空")
    @ApiModelProperty(value = "是否启用深度思考", name="enableThinking", required = true)
    /** 是否启用深度思考 */
    private Integer enableThinking;

    @Excel(name = "是否启用搜索")
    @NotNull(message = "是否启用不能为空")
    @ApiModelProperty(value = "是否启用搜索", name="enableSearch", required = true)
    /** 是否启用搜索 */
    private Integer enableSearch;

    /** 提示词类目 */
    @ApiModelProperty(value = "提示词类目", name="commonPromptProjectTypes")
    private List<Long> commonPromptProjectTypes;
}
