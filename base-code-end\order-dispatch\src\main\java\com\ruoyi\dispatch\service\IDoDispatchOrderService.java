package com.ruoyi.dispatch.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.dispatch.domain.DoDispatchOrder;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 派单Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
public interface IDoDispatchOrderService extends IService<DoDispatchOrder> {

    /**
     * 分页查询派单
     * @param doDispatchOrder 派单
     * @param page 分页条件
     * @return 派单集合
     */
    IPage<DoDispatchOrder> pages(DoDispatchOrder doDispatchOrder, IPage<DoDispatchOrder> page);

    /**
     * 查询派单列表
     * 
     * @param doDispatchOrder 派单
     * @return 派单集合
     */
     List<DoDispatchOrder> selectList(DoDispatchOrder doDispatchOrder);

}
