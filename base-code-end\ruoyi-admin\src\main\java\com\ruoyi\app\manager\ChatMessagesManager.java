package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.vo.ChatMessagesTwoVO;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.dispatch.service.IChatMessagesService;
import com.ruoyi.app.vo.ChatMessagesVo;
import com.ruoyi.app.vo.convert.ChatMessagesConvert;
import com.ruoyi.dispatch.domain.ChatMessages;

import java.util.List;

/**
 * 消息Manager
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Component
public class ChatMessagesManager {

    @Autowired
    private IChatMessagesService chatMessagesService;

    @Autowired
    private ChatMessagesConvert chatMessagesConvert;

    /**
    * 分页查询消息
    */
    public IPage<ChatMessagesVo> page(ChatMessagesVo chatMessagesVo, Query query){
        IPage<ChatMessages> page = chatMessagesService.pages(chatMessagesConvert.voToPo(chatMessagesVo), Condition.getPage(query));
        return (IPage<ChatMessagesVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ChatMessagesVo>(), ChatMessagesConvert.INSTANCE);
    }

    /**
    * 查询消息列表
    */
    public List<ChatMessagesVo> list(ChatMessagesVo chatMessagesVo) {
        List<ChatMessages> chatMessagesList = chatMessagesService.selectList(chatMessagesConvert.voToPo(chatMessagesVo));
        return chatMessagesConvert.poToVoList(chatMessagesList);
    }

    /**
     * 查询消息详细信息
     */
    public ChatMessagesVo getInfo(Long messageId) {
        ChatMessages chatMessages = chatMessagesService.getById(messageId);
        return chatMessagesConvert.poToVo(chatMessages);
    }

    /**
     * 新增消息
     */
    public boolean add(ChatMessagesVo chatMessagesVo) {
        return chatMessagesService.save(chatMessagesConvert.voToPo(chatMessagesVo));
    }

    /**
     * 修改消息
     */
    public boolean edit(ChatMessagesVo chatMessagesVo) {
        return chatMessagesService.updateById(chatMessagesConvert.voToPo(chatMessagesVo));
    }

    /**
     * 批量删除消息
     */
    public boolean remove(Long[] messageIds) {
        return chatMessagesService.removeByIds(CollUtil.toList(messageIds));
    }

    public List<ChatMessagesTwoVO> merchantMessageList() {
        return chatMessagesService.merchantMessageList();
    }

    public List<ChatMessagesTwoVO> customerMessageList() {
        return chatMessagesService.customerMessageList();
    }
}
