package com.ruoyi.dispatch.service.impl;

import java.util.List;
import cn.hutool.core.util.ObjUtil;
import com.ruoyi.acceptance.vo.ChatMessagesTwoVO;
import com.ruoyi.common.core.domain.model.DispatchLoginUser;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.dispatch.mapper.ChatMessagesMapper;
import com.ruoyi.dispatch.domain.ChatMessages;
import com.ruoyi.dispatch.service.IChatMessagesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


/**
 * 消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Service
public class ChatMessagesServiceImpl extends ServiceImpl<ChatMessagesMapper,ChatMessages> implements IChatMessagesService {

    @Autowired
    private ChatMessagesMapper chatMessagesMapper;

    /**
    * 条件构造器
    */
    private LambdaQueryWrapper<ChatMessages> queryWrapper(ChatMessages chatMessages) {
        LambdaQueryWrapper<ChatMessages> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessages.getConversationId()), ChatMessages::getConversationId, chatMessages.getConversationId());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessages.getSenderId()), ChatMessages::getSenderId, chatMessages.getSenderId());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessages.getContentType()), ChatMessages::getContentType, chatMessages.getContentType());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessages.getContent()), ChatMessages::getContent, chatMessages.getContent());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessages.getFileUrl()), ChatMessages::getFileUrl, chatMessages.getFileUrl());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessages.getFileSize()), ChatMessages::getFileSize, chatMessages.getFileSize());
        queryWrapper.eq(ObjUtil.isNotEmpty(chatMessages.getIsRecalled()), ChatMessages::getIsRecalled, chatMessages.getIsRecalled());
        return queryWrapper;
    }

    /**
     * 查询消息分页
     *
     * @param chatMessages 消息
     * @return 消息
     */
    @Override
    public IPage<ChatMessages> pages(ChatMessages chatMessages, IPage<ChatMessages> page)
    {
        return chatMessagesMapper.selectPage(page, this.queryWrapper(chatMessages));
    }

    /**
     * 查询消息列表
     * 
     * @param chatMessages 消息
     * @return 消息
     */
    @Override
    public List<ChatMessages> selectList(ChatMessages chatMessages)
    {
        return chatMessagesMapper.selectList(this.queryWrapper(chatMessages));
    }

    @Override
    public List<ChatMessagesTwoVO> merchantMessageList() {
        DispatchLoginUser dispatchLoginUser = SecurityUtils.getDispatchLoginUser();
        String merchantOpenid = dispatchLoginUser.getOpenid();
        return chatMessagesMapper.merchantMessageList(merchantOpenid);
    }

    @Override
    public List<ChatMessagesTwoVO> customerMessageList() {
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String customerOpenid = wxLoginUser.getOpenid();
        return chatMessagesMapper.customerMessageList(customerOpenid);
    }

}
