import request from '@/utils/request'

// 查询派单列表
export function listDispatch(query) {
  return request({
    url: '/dispatch/dispatch/list',
    method: 'get',
    params: query
  })
}

// 查询派单详细
export function getDispatch(id) {
  return request({
    url: '/dispatch/dispatch/' + id,
    method: 'get'
  })
}

// 新增派单
export function addDispatch(data) {
  return request({
    url: '/dispatch/dispatch',
    method: 'post',
    data: data
  })
}

// 修改派单
export function updateDispatch(data) {
  return request({
    url: '/dispatch/dispatch',
    method: 'put',
    data: data
  })
}

// 删除派单
export function delDispatch(id) {
  return request({
    url: '/dispatch/dispatch/' + id,
    method: 'delete'
  })
}
