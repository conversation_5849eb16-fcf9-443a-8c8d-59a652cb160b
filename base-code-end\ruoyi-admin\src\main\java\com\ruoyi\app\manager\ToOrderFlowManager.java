package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.domain.ToOrderFlow;
import com.ruoyi.acceptance.service.IToOrderFlowService;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.app.vo.ToOrderFlowVo;
import com.ruoyi.app.vo.convert.ToOrderFlowConvert;
import java.util.List;

/**
 * 订单流水Manager
 *
 * <AUTHOR>
 * @date 2025-01-10
 */
@Component
public class ToOrderFlowManager {

    @Autowired
    private IToOrderFlowService toOrderFlowService;

    @Autowired
    private ToOrderFlowConvert toOrderFlowConvert;

    /**
    * 分页查询订单流水
    */
    public IPage<ToOrderFlowVo> page(ToOrderFlowVo toOrderFlowVo, Query query){
        IPage<ToOrderFlow> page = toOrderFlowService.pages(toOrderFlowConvert.voToPo(toOrderFlowVo), Condition.getPage(query));
        return (IPage<ToOrderFlowVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToOrderFlowVo>(), ToOrderFlowConvert.INSTANCE);
    }

    /**
    * 查询订单流水列表
    */
    public List<ToOrderFlowVo> list(ToOrderFlowVo toOrderFlowVo) {
        List<ToOrderFlow> toOrderFlowList = toOrderFlowService.selectList(toOrderFlowConvert.voToPo(toOrderFlowVo));
        return toOrderFlowConvert.poToVoList(toOrderFlowList);
    }

    /**
     * 查询订单流水详细信息
     */
    public ToOrderFlowVo getInfo(Long id) {
        ToOrderFlow toOrderFlow = toOrderFlowService.getById(id);
        return toOrderFlowConvert.poToVo(toOrderFlow);
    }

    /**
     * 新增订单流水
     */
    public boolean add(ToOrderFlowVo toOrderFlowVo) {
        return toOrderFlowService.save(toOrderFlowConvert.voToPo(toOrderFlowVo));
    }

    /**
     * 修改订单流水
     */
    public boolean edit(ToOrderFlowVo toOrderFlowVo) {
        return toOrderFlowService.updateById(toOrderFlowConvert.voToPo(toOrderFlowVo));
    }

    /**
     * 批量删除订单流水
     */
    public boolean remove(Long[] ids) {
        return toOrderFlowService.removeByIds(CollUtil.toList(ids));
    }

}
