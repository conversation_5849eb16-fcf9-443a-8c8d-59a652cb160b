package com.ruoyi.app.controller.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.ruoyi.acceptance.dto.ToProjectTwoDTO;
import com.ruoyi.acceptance.service.IToProjectService;
import com.ruoyi.acceptance.vo.*;
import com.ruoyi.app.manager.ToProjectManager;

import com.ruoyi.app.vo.ToProjectVo;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.DispatchLoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.wrapper.Query;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 项目基本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@Api(tags = "微信端项目基本信息管理")
@RestController
@RequestMapping("/wx/acceptance/project")
public class WXToProjectController {

    @Autowired
    private ToProjectManager toProjectManager;
    @Autowired
    private IToProjectService iToProjectService;

    /**
     * 分页查询项目基本信息
     */
    @ApiOperation("分页查询项目基本信息")
    @ApiOperationSupport(order = 1)
    @GetMapping("/list")
    public R<IPage<ToProjectVo>> list(ToProjectVo toProjectVo, Query query)
    {
        return R.ok(toProjectManager.page(toProjectVo, query));
    }

//    /**
//    * 查询项目基本信息全部列表
//    */
//    @ApiOperation("查询项目基本信息全部列表")
//    @ApiOperationSupport(order = 2)
//    @GetMapping("/allList")
//    public R<List<ToProjectVo>> allList(ToProjectVo toProjectVo){
//        return R.ok(toProjectManager.list(toProjectVo));
//    }

//    /**
//     * 导出项目基本信息列表
//     */
//    @ApiOperation("导出项目基本信息列表")
//    @ApiOperationSupport(order = 3)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ToProjectVo toProjectVo, Query query)
//    {
//        List<ToProjectVo> list = toProjectManager.page(toProjectVo, query).getRecords();
//        ExcelUtil<ToProjectVo> util = new ExcelUtil<ToProjectVo>(ToProjectVo.class);
//        util.exportExcel(response, list, "项目基本信息数据");
//    }

    /**
     * 获取项目基本信息详细信息
     */
    @ApiOperation("获取项目基本信息详细")
    @ApiOperationSupport(order = 3)
    @ApiImplicitParam(name = "id", value = "主键", required = true, dataType = "Long", paramType = "path", dataTypeClass = Long.class)
    @GetMapping(value = "/{id}")
    public R<ToProjectVo> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(toProjectManager.getInfo(id));
    }

    /**
     * 查询项目基本信息全部列表
     */
    @ApiOperation("商户首页下拉加载查询接口")
    @PostMapping("/projectList")
    public R<List<ToProjectThreeVO>> projectList(@RequestBody ToProjectTwoVO toProjectTwoVO){
        return R.ok(iToProjectService.projectList(toProjectTwoVO));
    }

    @ApiOperation("商户端订单明细接口")
    @GetMapping("/projectDetail/{projectId}")
    public R<ToProjectFourVO> projectDetail(@PathVariable String projectId){
        return R.ok(iToProjectService.projectDetail(projectId));
    }

    @ApiOperation("商户我的页面接单列表接口")
    @GetMapping("/myProjectList/{status}")
    public R<List<ToProjectSixVO>> myProjectList(@PathVariable Integer status){
        ToProjectTwoDTO toProjectTwoDTO = new ToProjectTwoDTO();
        toProjectTwoDTO.setStatus(status);
        DispatchLoginUser dispatchLoginUser = SecurityUtils.getDispatchLoginUser();
        String openId= dispatchLoginUser.getOpenid();
        toProjectTwoDTO.setMerchantOpenid(openId);
        return R.ok(iToProjectService.myProjectList(toProjectTwoDTO));
    }

    @ApiOperation("商户我的页面接单明细接口")
    @GetMapping("/myProjectDetail/{projectId}")
    public R<ToProjectFiveVO> myProjectDetail(@PathVariable String projectId){
        return R.ok(iToProjectService.myProjectDetail(projectId));
    }

    /**
     * 新增项目基本信息
     */
    @ApiOperation("新增项目基本信息")
    @ApiOperationSupport(order = 4)
    @PostMapping
    public R<?> add(@Valid @RequestBody ToProjectVo toProjectVo){
        return toProjectManager.saveProjectFromWx(toProjectVo) ? R.ok() : R.fail();
    }



    /**
     * 修改项目基本信息
     */
    @ApiOperation("修改项目基本信息")
    @ApiOperationSupport(order = 5)
    @PutMapping
    public R<?> edit(@Valid @RequestBody ToProjectVo toProjectVo)
    {   return toProjectManager.editProjectFromWx(toProjectVo) ? R.ok() : R.fail();
//        return toProjectManager.edit(toProjectVo) ? R.ok() : R.fail();
    }

    /**
     * 客户端确认订单交易
     */
    @ApiOperation("客户端确认订单交易")
    @PostMapping("/confirmTransaction")
    public R<?> confirmTransaction(@Valid @RequestBody ToProjectVo toProjectVo) {
//        return toProjectManager.confirmTransaction();
        return R.ok();
    }

    /**
     * 删除项目基本信息
     */
    @ApiOperation("删除项目基本信息")
    @ApiOperationSupport(order = 6)
    @ApiImplicitParam(name = "ids", value = "主键", required = true, dataType = "Long", paramType = "path", allowMultiple = true, dataTypeClass = Long.class)
    @DeleteMapping("/{ids}")
    public R<?> remove(@PathVariable Long[] ids)
    {
        return toProjectManager.remove(ids) ? R.ok() : R.fail();
    }


    /**
     * 查询项目基本信息全部列表
     */
    @ApiOperation("查询用户项目（订单）基本信息全部列表")
    @ApiOperationSupport(order = 7)
    @GetMapping("/currentUser/allList")
    public R<List<ToProjectVo>> currentUserList(){
        return R.ok(toProjectManager.getCurrentUserList());
    }











}
