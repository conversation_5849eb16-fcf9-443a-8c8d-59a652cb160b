package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 提示词对象 common_prompt
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("common_prompt")
public class CommonPrompt extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long promptId;

    /** AI类型（如qwen） */
    @TableField(value = "ai_type")
    private String aiType;

    /** 提示词标题 */
    @TableField(value = "prompt_title")
    private String promptTitle;

    /** 系统人设 */
    @TableField(value = "system_prompt")
    private String systemPrompt;

    /** 提示词内容 */
    @TableField(value = "content")
    private String content;

    /** 是否启用 */
    @TableField(value = "is_active")
    private Integer isActive;

    /** 是否启用深度思考 */
    @TableField(value = "enable_thinking")
    private Integer enableThinking;

    /** 是否启用搜索 */
    @TableField(value = "enable_search")
    private Integer enableSearch;
}
