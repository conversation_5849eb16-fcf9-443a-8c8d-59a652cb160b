package com.ruoyi.app.manager;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.acceptance.domain.ToOrder;
import com.ruoyi.acceptance.domain.ToOrderHistory;
import com.ruoyi.acceptance.domain.ToProjectCategory;
import com.ruoyi.acceptance.service.*;
import com.ruoyi.api.manager.OssFileManager;
import com.ruoyi.api.vo.OssFileVo;

import com.ruoyi.app.vo.convert.ToOrderConvert;
import com.ruoyi.common.core.domain.convert.IPageConvert;
import com.ruoyi.common.core.domain.entity.ToUser;
import com.ruoyi.common.core.domain.model.WxLoginUser;
import com.ruoyi.common.enums.OrderPayStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.wrapper.Condition;
import com.ruoyi.framework.web.wrapper.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.app.vo.ToProjectVo;
import com.ruoyi.app.vo.convert.ToProjectConvert;
import com.ruoyi.acceptance.domain.ToProject;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目基本信息Manager
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Component
public class ToProjectManager {

    @Autowired
    private IToProjectService toProjectService;

    @Autowired
    private ToProjectConvert toProjectConvert;

    @Autowired
    private IToOrderService toOrderService;

    @Autowired
    private ToOrderConvert toOrderConvert;

    @Autowired
    private IToOrderHistoryService toOrderHistoryService;

    @Autowired
    private IToUserService toUserService;

    @Autowired
    private OssFileManager ossFileManager;

    @Autowired
    private IToProjectCategoryService categoryService;

    /**
     * 分页查询项目基本信息
     */
    public IPage<ToProjectVo> page(ToProjectVo toProjectVo, Query query) {
        IPage<ToProject> page = toProjectService.pages(toProjectConvert.voToPo(toProjectVo), Condition.getPage(query));
        return (IPage<ToProjectVo>) IPageConvert.instance().IPagePoToIPageDto(page, new Page<ToProjectVo>(), ToProjectConvert.INSTANCE);
    }

    /**
     * 查询项目基本信息列表
     */
    public List<ToProjectVo> list(ToProjectVo toProjectVo) {
        List<ToProject> toProjectList = toProjectService.selectList(toProjectConvert.voToPo(toProjectVo));
        return toProjectConvert.poToVoList(toProjectList);
    }

    /**
     * 查询项目基本信息详细信息
     */
    public ToProjectVo getInfo(Long id) {
        ToProjectVo projectVo = toProjectConvert.poToVo(toProjectService.getById(id));
        ToOrder toOrder = toOrderService.selectOneByProjectId(id);
        projectVo.setTotalPrice(toOrder.getTotalPrice().toString());
        // 处理附件信息,将附件信息带给前端
        handleAttachments(projectVo);
        handleCategorySelection(projectVo);
        handleAdditionalServices(projectVo);
        projectVo.setOrderPayStatus(toOrder.getOrderPayStatus());
        return projectVo;
    }

    /**
     * 新增项目基本信息
     */
    public boolean add(ToProjectVo toProjectVo) {
        return toProjectService.save(toProjectConvert.voToPo(toProjectVo));
    }

    /**
     * 修改项目基本信息
     */
    public boolean edit(ToProjectVo toProjectVo) {
        return toProjectService.updateById(toProjectConvert.voToPo(toProjectVo));
    }

    /**
     * 批量删除项目基本信息
     */
    public boolean remove(Long[] ids) {
        return toProjectService.removeByIds(CollUtil.toList(ids));
    }


    /**
     * 处理项目附件信息
     */
    private void handleAttachments(ToProjectVo projectVo) {
        // 如果有附件ID
        if (StringUtils.isNotEmpty(projectVo.getAttachments())) {
            // 分割ID
            String[] fileIds = projectVo.getAttachments().split(",");
            // 查询附件信息
            List<OssFileVo> fileList = Arrays.stream(fileIds)
                    .map(id -> ossFileManager.getInfo(Long.valueOf(id)))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // 设置附件信息列表
            projectVo.setAttachmentList(fileList);
        }
    }

    /**
     * 处理选择类目
     *
     * @param projectVo
     */
    public void handleCategorySelection(ToProjectVo projectVo) {


        // 批量查询处理
        if (StringUtils.isNotEmpty(projectVo.getCategorySelectionOne()) || StringUtils.isNotEmpty(projectVo.getCategorySelectionTwo())) {
            List<String> categoryCodeList = new ArrayList<>();

            if (StringUtils.isNotEmpty(projectVo.getCategorySelectionOne())) {
                categoryCodeList.add(projectVo.getCategorySelectionOne());
            }

            if (StringUtils.isNotEmpty(projectVo.getCategorySelectionTwo())) {
                categoryCodeList.add(projectVo.getCategorySelectionTwo());
            }

            if (!categoryCodeList.isEmpty()) {
                // 批量查询分类信息
                List<ToProjectCategory> categoryList = categoryService.selectByCateGoryCodes(categoryCodeList);
                Map<String, String> codeToNameMap = categoryList.stream()
                        .collect(Collectors.toMap(ToProjectCategory::getCategoryCode, ToProjectCategory::getCategoryName));
                projectVo.setCategorySelectionMap(codeToNameMap);

            }
        }

    }


    /**
     * 处理选择类目
     *
     * @param projectVo
     */
    public void handleAdditionalServices(ToProjectVo projectVo) {
        String additionalServices = projectVo.getAdditionalServices();
        if (additionalServices !=null && !additionalServices.isEmpty()){
            List<String> collect = Arrays.stream(additionalServices.split(",")).collect(Collectors.toList());
            List<ToProjectCategory> toProjectCategories = categoryService.selectByCateGoryCodes(collect);
            Map<String, String> serviceMsgMap = toProjectCategories.stream().collect(Collectors.toMap(ToProjectCategory::getCategoryCode, ToProjectCategory::getCategoryName));
            projectVo.setAdditionalServicesMap(serviceMsgMap);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean saveProjectFromWx(ToProjectVo toProjectVo) {
        try {
            // 获取当前用户
            WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
            String openId = wxLoginUser.getOpenid();

            // 数据校验
            if (StringUtils.isEmpty(toProjectVo.getTotalPrice())) {
                throw new ServiceException("订单总价不能为空");
            }

            // 1. 保存项目
            ToProject oldtoProject = toProjectConvert.voToPo(toProjectVo);
            oldtoProject.setCustomerOpenid(openId);
            ToProject toProject = toProjectService.saveProjectFromWx(oldtoProject);

            if (toProject == null || toProject.getId() == null) {
                throw new ServiceException("项目保存失败");
            }

            // 2. 创建订单
            ToOrder toOrder = new ToOrder();
            BigDecimal totalPrice = new BigDecimal(toProjectVo.getTotalPrice());
            toOrder.setTotalPrice(totalPrice);
            //订单状态，后续采用枚举
            toOrder.setOrderPayStatus(OrderPayStatusEnum.PENDING_DEPOSIT.getCode());
            BigDecimal halfPrice = totalPrice.divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_HALF_UP);
            // 设置定金
            toOrder.setAdvancePrice(halfPrice);
            // 设置尾款（也是一半）
            toOrder.setBalancePayment(halfPrice);
            toOrder.setSettlement(0);
            toOrder.setProjectId(toProject.getId().toString());
            toOrder.setCustomOpenId(openId);
            toOrderService.addWxUserToOrder(toOrder);
            Long orderId = toOrder.getId();
            if (orderId == null) {
                throw new ServiceException("订单创建失败");
            }

            // 3. 添加订单流水（后续需要删除）
            ToUser sharedUser = toUserService.selectShareUserByInvitedUserOpenId(openId);
            String sharedOpenId = sharedUser != null ? sharedUser.getOpenId() : null;
            ToOrderHistory toOrderHistory = new ToOrderHistory();
            toOrderHistory.setAgentOpenId(sharedOpenId);
            toOrderHistory.setCustomerOpenId(openId);
            boolean result = toOrderHistoryService.save(toOrderHistory);

            if (!result) {
                throw new ServiceException("订单流水创建失败");
            }

            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("系统异常，请稍后重试");
        }

    }


    @Transactional(rollbackFor = Exception.class)
    public boolean editProjectFromWx(ToProjectVo toProjectVo) {
        if (toProjectVo == null || toProjectVo.getId() == null) {
            throw new ServiceException("项目信息为空或ID为空");
        }

        try {
            // 参数校验
            Long projectId = toProjectVo.getId();
            String totalPriceStr = toProjectVo.getTotalPrice();
            if (StringUtils.isEmpty(totalPriceStr)) {
                throw new ServiceException("项目总价不能为空");
            }

            // 1.更新project
            ToProject toProject = toProjectService.getById(projectId);
            if (toProject == null) {
                throw new ServiceException("项目不存在");
            }

            boolean projectUpdated = toProjectService.updateById(toProject);

            if (!projectUpdated) {
                throw new ServiceException("更新项目失败");
            }

            // 2.根据projectId找到order
            ToOrder toOrder = toOrderService.selectOneByProjectId(projectId);
            if (toOrder == null) {
                throw new ServiceException("订单不存在");
            }


            // 3.更新order
            BigDecimal totalPrice = new BigDecimal(toProjectVo.getTotalPrice());
            toOrder.setTotalPrice(totalPrice);
            BigDecimal halfPrice = totalPrice.divide(BigDecimal.valueOf(2), 2, BigDecimal.ROUND_HALF_UP);
            // 设置定金
            toOrder.setAdvancePrice(halfPrice);
            // 设置尾款（也是一半）
            toOrder.setBalancePayment(halfPrice);

            boolean orderUpdated = toOrderService.updateById(toOrder);

            if (!orderUpdated) {
                throw new ServiceException("更新订单失败");
            }

            return true;
        } catch (Exception e) {
            throw e;

        }
    }

    public List<ToProjectVo> getCurrentUserList() {

        // 获取当前用户信息
        WxLoginUser wxLoginUser = SecurityUtils.getWxLoginUser();
        String openId = wxLoginUser.getOpenid();

        // 查询项目列表
        List<ToProject> projectList = toProjectService.selectListByUserOpenId(openId);
        if (CollectionUtils.isEmpty(projectList)) {
            return Collections.emptyList();
        }

        // 提取项目ID列表
        List<Long> projectIds = projectList.stream()
                .map(ToProject::getId)
                .collect(Collectors.toList());

        // 批量查询订单信息
        Map<String, ToOrder> projectOrderMap = toOrderService.selectByProjectIds(projectIds)
                .stream()
                .collect(Collectors.toMap(ToOrder::getProjectId, order -> order));

        // 转换并组装数据
        return projectList.stream()
                .map(project -> {
                    ToProjectVo projectVo = toProjectConvert.poToVo(project);
                    // 设置订单价格
                    ToOrder order = projectOrderMap.get(project.getId().toString());
                    if (order != null && order.getTotalPrice() != null) {
                        projectVo.setTotalPrice(order.getTotalPrice().toString());
                        //添加订单状态
                        projectVo.setOrderPayStatus(order.getOrderPayStatus());
                    }
                    // 处理分类信息
                    handleCategorySelection(projectVo);


                    return projectVo;
                })
                .collect(Collectors.toList());
    }


}
