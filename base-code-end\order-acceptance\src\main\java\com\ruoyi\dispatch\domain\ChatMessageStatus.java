package com.ruoyi.dispatch.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.ruoyi.common.core.domain.PoBaseEntity;

/**
 * 消息状态对象 chat_message_status
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
@Data
@TableName("chat_message_status")
public class ChatMessageStatus extends PoBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 消息ID */
    @TableField(value = "message_id")
    private Long messageId;

    /** 用户ID */
    @TableField(value = "user_id")
    private Long userId;

    /** 是否已读(0-未读,1-已读) */
    @TableField(value = "is_read")
    private Integer isRead;

    /** 阅读时间 */
    @TableField(value = "read_time")
    private Date readTime;

}
