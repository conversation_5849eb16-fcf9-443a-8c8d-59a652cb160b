package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.domain.ToWalletHistory;
import com.ruoyi.app.vo.ToWalletHistoryVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToWalletHistoryConvertImpl implements ToWalletHistoryConvert {

    @Override
    public ToWalletHistoryVo poToVo(ToWalletHistory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToWalletHistoryVo toWalletHistoryVo = new ToWalletHistoryVo();

        toWalletHistoryVo.setFrozenAmount( arg0.getFrozenAmount() );
        toWalletHistoryVo.setId( arg0.getId() );
        toWalletHistoryVo.setNotWithdrawAmount( arg0.getNotWithdrawAmount() );
        toWalletHistoryVo.setOpenId( arg0.getOpenId() );
        toWalletHistoryVo.setTotalAmount( arg0.getTotalAmount() );
        toWalletHistoryVo.setWithdrawnAmount( arg0.getWithdrawnAmount() );

        return toWalletHistoryVo;
    }

    @Override
    public List<ToWalletHistoryVo> poToVoList(List<ToWalletHistory> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToWalletHistoryVo> list = new ArrayList<ToWalletHistoryVo>( arg0.size() );
        for ( ToWalletHistory toWalletHistory : arg0 ) {
            list.add( poToVo( toWalletHistory ) );
        }

        return list;
    }

    @Override
    public ToWalletHistory voToPo(ToWalletHistoryVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToWalletHistory toWalletHistory = new ToWalletHistory();

        toWalletHistory.setFrozenAmount( arg0.getFrozenAmount() );
        toWalletHistory.setId( arg0.getId() );
        toWalletHistory.setNotWithdrawAmount( arg0.getNotWithdrawAmount() );
        toWalletHistory.setOpenId( arg0.getOpenId() );
        toWalletHistory.setTotalAmount( arg0.getTotalAmount() );
        toWalletHistory.setWithdrawnAmount( arg0.getWithdrawnAmount() );

        return toWalletHistory;
    }

    @Override
    public List<ToWalletHistory> voToPoList(List<ToWalletHistoryVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToWalletHistory> list = new ArrayList<ToWalletHistory>( arg0.size() );
        for ( ToWalletHistoryVo toWalletHistoryVo : arg0 ) {
            list.add( voToPo( toWalletHistoryVo ) );
        }

        return list;
    }
}
