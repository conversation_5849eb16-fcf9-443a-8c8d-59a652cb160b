package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.dto.ToOrderDTO;
import com.ruoyi.acceptance.vo.ToOrderVo;
import com.ruoyi.common.core.domain.convert.BeanPoVoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 接单订单PoVo转换器
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ToOrderDTOConvert extends BeanPoVoMapper<ToOrderDTO, ToOrderVo> {
        ToOrderDTOConvert INSTANCE = Mappers.getMapper(ToOrderDTOConvert.class);
}
