package com.ruoyi.dispatch.service;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.dispatch.domain.ChatConversationMembers;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 会话成员Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IChatConversationMembersService extends IService<ChatConversationMembers> {

    /**
     * 分页查询会话成员
     * @param chatConversationMembers 会话成员
     * @param page 分页条件
     * @return 会话成员集合
     */
    IPage<ChatConversationMembers> pages(ChatConversationMembers chatConversationMembers, IPage<ChatConversationMembers> page);

    /**
     * 查询会话成员列表
     * 
     * @param chatConversationMembers 会话成员
     * @return 会话成员集合
     */
     List<ChatConversationMembers> selectList(ChatConversationMembers chatConversationMembers);

}
