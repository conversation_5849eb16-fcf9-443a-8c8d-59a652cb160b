package com.ruoyi.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.acceptance.domain.ToOrder;
import com.ruoyi.acceptance.service.IToOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static com.ruoyi.common.constant.AcceptanceConstants.NON_REFUNDABLE_DEPOSIT;

@Slf4j
@Component("AcceptanceTask")
public class AcceptanceTask {

    @Autowired
    private IToOrderService toOrderService;

    public void changeRefundStatus() {
        LambdaQueryWrapper<ToOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ToOrder::getOrderPayStatus,2);
        List<ToOrder> toOrders = toOrderService.list(queryWrapper);
        for (ToOrder toOrder : toOrders){
            Date depositPaymentTime = toOrder.getDepositPaymentTime();
            Date currentDate = new Date();
            long diffInMillies = currentDate.getTime() - depositPaymentTime.getTime();
            if (diffInMillies >= 2 * 24 * 60 * 60 * 1000) {// 不可退款
                toOrder.setOrderPayStatus(NON_REFUNDABLE_DEPOSIT);
                log.info("定时任务修改订单状态为已付定金不可退款的订单ID{}",toOrder.getId());
            }
        }
        toOrderService.updateBatchById(toOrders);
    }

}
