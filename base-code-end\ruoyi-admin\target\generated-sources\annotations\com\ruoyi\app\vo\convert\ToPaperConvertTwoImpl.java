package com.ruoyi.app.vo.convert;

import com.ruoyi.acceptance.dto.HeadSculptureDTO;
import com.ruoyi.acceptance.dto.ToPaperDTO;
import com.ruoyi.acceptance.vo.ToPaperVo;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-25T11:18:41+0800",
    comments = "version: 1.5.0.Beta1, compiler: Eclipse JDT (IDE) 3.42.50.v20250720-0225, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ToPaperConvertTwoImpl implements ToPaperConvertTwo {

    @Override
    public ToPaperVo poToVo(ToPaperDTO arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToPaperVo toPaperVo = new ToPaperVo();

        toPaperVo.setAdvancePrice( arg0.getAdvancePrice() );
        toPaperVo.setApplyRefund( arg0.getApplyRefund() );
        toPaperVo.setBalancePayment( arg0.getBalancePayment() );
        toPaperVo.setBalancePaymentTime( arg0.getBalancePaymentTime() );
        toPaperVo.setBuyMembers( arg0.getBuyMembers() );
        toPaperVo.setCreateTime( arg0.getCreateTime() );
        toPaperVo.setCustom( arg0.getCustom() );
        toPaperVo.setDeliveryTime( arg0.getDeliveryTime() );
        toPaperVo.setDepositPaymentTime( arg0.getDepositPaymentTime() );
        toPaperVo.setGroupDaysRemaining( arg0.getGroupDaysRemaining() );
        toPaperVo.setGroupEndFlag( arg0.getGroupEndFlag() );
        toPaperVo.setGroupId( arg0.getGroupId() );
        toPaperVo.setGroupPurchase( arg0.getGroupPurchase() );
        toPaperVo.setGroupStatus( arg0.getGroupStatus() );
        List<HeadSculptureDTO> list = arg0.getHeadSculptureDTOS();
        if ( list != null ) {
            toPaperVo.setHeadSculptureDTOS( new ArrayList<HeadSculptureDTO>( list ) );
        }
        toPaperVo.setId( arg0.getId() );
        toPaperVo.setIncludingServices( arg0.getIncludingServices() );
        toPaperVo.setIntroduction( arg0.getIntroduction() );
        toPaperVo.setNotSale( arg0.getNotSale() );
        toPaperVo.setOrderId( arg0.getOrderId() );
        toPaperVo.setOrderPayStatus( arg0.getOrderPayStatus() );
        toPaperVo.setRefundFlag( arg0.getRefundFlag() );
        toPaperVo.setSettlement( arg0.getSettlement() );
        toPaperVo.setTechnicalSelection( arg0.getTechnicalSelection() );
        toPaperVo.setTitle( arg0.getTitle() );
        toPaperVo.setTotalPrice( arg0.getTotalPrice() );

        return toPaperVo;
    }

    @Override
    public List<ToPaperVo> poToVoList(List<ToPaperDTO> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToPaperVo> list = new ArrayList<ToPaperVo>( arg0.size() );
        for ( ToPaperDTO toPaperDTO : arg0 ) {
            list.add( poToVo( toPaperDTO ) );
        }

        return list;
    }

    @Override
    public ToPaperDTO voToPo(ToPaperVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ToPaperDTO toPaperDTO = new ToPaperDTO();

        toPaperDTO.setAdvancePrice( arg0.getAdvancePrice() );
        toPaperDTO.setApplyRefund( arg0.getApplyRefund() );
        toPaperDTO.setBalancePayment( arg0.getBalancePayment() );
        toPaperDTO.setBalancePaymentTime( arg0.getBalancePaymentTime() );
        toPaperDTO.setBuyMembers( arg0.getBuyMembers() );
        toPaperDTO.setCreateTime( arg0.getCreateTime() );
        toPaperDTO.setCustom( arg0.getCustom() );
        toPaperDTO.setDeliveryTime( arg0.getDeliveryTime() );
        toPaperDTO.setDepositPaymentTime( arg0.getDepositPaymentTime() );
        toPaperDTO.setGroupDaysRemaining( arg0.getGroupDaysRemaining() );
        toPaperDTO.setGroupEndFlag( arg0.getGroupEndFlag() );
        toPaperDTO.setGroupId( arg0.getGroupId() );
        toPaperDTO.setGroupPurchase( arg0.getGroupPurchase() );
        toPaperDTO.setGroupStatus( arg0.getGroupStatus() );
        List<HeadSculptureDTO> list = arg0.getHeadSculptureDTOS();
        if ( list != null ) {
            toPaperDTO.setHeadSculptureDTOS( new ArrayList<HeadSculptureDTO>( list ) );
        }
        toPaperDTO.setId( arg0.getId() );
        toPaperDTO.setIncludingServices( arg0.getIncludingServices() );
        toPaperDTO.setIntroduction( arg0.getIntroduction() );
        toPaperDTO.setNotSale( arg0.getNotSale() );
        toPaperDTO.setOrderId( arg0.getOrderId() );
        toPaperDTO.setOrderPayStatus( arg0.getOrderPayStatus() );
        toPaperDTO.setRefundFlag( arg0.getRefundFlag() );
        toPaperDTO.setSettlement( arg0.getSettlement() );
        toPaperDTO.setTechnicalSelection( arg0.getTechnicalSelection() );
        toPaperDTO.setTitle( arg0.getTitle() );
        toPaperDTO.setTotalPrice( arg0.getTotalPrice() );

        return toPaperDTO;
    }

    @Override
    public List<ToPaperDTO> voToPoList(List<ToPaperVo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<ToPaperDTO> list = new ArrayList<ToPaperDTO>( arg0.size() );
        for ( ToPaperVo toPaperVo : arg0 ) {
            list.add( voToPo( toPaperVo ) );
        }

        return list;
    }
}
